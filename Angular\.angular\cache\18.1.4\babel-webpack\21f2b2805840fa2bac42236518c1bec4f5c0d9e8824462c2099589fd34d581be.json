{"ast": null, "code": "import { FormControl } from '@angular/forms';\nimport _, { each } from 'lodash';\nimport { map, Subject, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { AppSettings } from 'src/app/app.settings';\nimport { ConfirmationDialogComponent } from '../../shared/confirmation-dialog/confirmation-dialog.component';\nimport { RoleEditComponent } from '../role-edit/role-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"../../services/exceljs.service\";\nimport * as i3 from \"../../services/http-utils.service\";\nimport * as i4 from \"../../services/app.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../services/user.service\";\nimport * as i7 from \"../../services/role.service\";\nimport * as i8 from \"../../services/kendo-column.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"@progress/kendo-angular-grid\";\nimport * as i12 from \"@progress/kendo-angular-inputs\";\nimport * as i13 from \"@progress/kendo-angular-buttons\";\nimport * as i14 from \"ng-inline-svg-2\";\nimport * as i15 from \"@progress/kendo-angular-dropdowns\";\nconst _c0 = [\"normalGrid\"];\nconst _c1 = () => [10, 15, 20, 50, 100];\nconst _c2 = a0 => ({\n  pageSizes: a0,\n  previousNext: true,\n  info: true,\n  type: \"numeric\",\n  buttonCount: 5\n});\nconst _c3 = () => ({\n  allowUnsort: true,\n  mode: \"single\"\n});\nconst _c4 = () => ({\n  checkboxOnly: true,\n  mode: \"multiple\"\n});\nconst _c5 = () => ({\n  filter: true\n});\nconst _c6 = () => ({\n  \"background-color\": \"#efefef !important\"\n});\nconst _c7 = () => ({\n  \"background-color\": \"#edf0f3\",\n  \"font-weight\": \"600\"\n});\nfunction RoleListComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"div\", 9)(3, \"span\", 10);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 11);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction RoleListComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"kendo-textbox\", 13);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RoleListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchData, $event) || (ctx_r2.searchData = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown\", function RoleListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchKeyDown($event));\n    })(\"valueChange\", function RoleListComponent_ng_template_4_Template_kendo_textbox_valueChange_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(2, \"kendo-grid-spacer\");\n    i0.ɵɵelementStart(3, \"div\", 14)(4, \"span\", 15);\n    i0.ɵɵtext(5, \"Total: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 16);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function RoleListComponent_ng_template_4_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.create());\n    });\n    i0.ɵɵelement(9, \"span\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function RoleListComponent_ng_template_4_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleExpand());\n    });\n    i0.ɵɵelement(11, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function RoleListComponent_ng_template_4_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.resetTable());\n    });\n    i0.ɵɵelement(13, \"i\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function RoleListComponent_ng_template_4_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.refreshGrid());\n    });\n    i0.ɵɵelement(15, \"i\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", 500, \"px\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchData);\n    i0.ɵɵproperty(\"clearButton\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.page.totalElements || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/arrows/arr075.svg\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"fa-expand\", !ctx_r2.isExpanded)(\"fa-compress\", ctx_r2.isExpanded);\n  }\n}\nfunction RoleListComponent_ng_template_5_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28)(3, \"label\", 29);\n    i0.ɵɵtext(4, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"kendo-dropdownlist\", 30);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RoleListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 31)(7, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function RoleListComponent_ng_template_5_div_0_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyAdvancedFilters());\n    });\n    i0.ɵɵelement(8, \"i\", 33);\n    i0.ɵɵtext(9, \" Apply Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function RoleListComponent_ng_template_5_div_0_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearAllFilters());\n    });\n    i0.ɵɵelement(11, \"i\", 35);\n    i0.ɵɵtext(12, \" Clear \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.status);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n  }\n}\nfunction RoleListComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, RoleListComponent_ng_template_5_div_0_Template, 13, 2, \"div\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showAdvancedFilters);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 43);\n    i0.ɵɵlistener(\"click\", function RoleListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template_a_click_0_listener() {\n      const dataItem_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.edit(dataItem_r6.roleId));\n    });\n    i0.ɵɵelement(1, \"span\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen055.svg\");\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 41);\n    i0.ɵɵtemplate(1, RoleListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template, 2, 1, \"ng-template\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(10, _c6));\n    i0.ɵɵproperty(\"width\", 90)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"action\"))(\"headerStyle\", i0.ɵɵpureFunction0(11, _c7))(\"includeInChooser\", false)(\"columnMenu\", false)(\"sortable\", false)(\"hidden\", ctx_r2.getHiddenField(\"action\"));\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 47);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r7.roleName, \" \");\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 48);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r8 = ctx.$implicit;\n    const column_r9 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r9)(\"filter\", filter_r8)(\"extra\", true);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 45);\n    i0.ɵɵtemplate(1, RoleListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template, 3, 1, \"ng-template\", 42)(2, RoleListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template, 6, 3, \"ng-template\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 200)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"roleName\"))(\"headerStyle\", i0.ɵɵpureFunction0(7, _c7))(\"includeInChooser\", false)(\"hidden\", ctx_r2.getHiddenField(\"roleName\"))(\"filterable\", true);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 50);\n  }\n  if (rf & 2) {\n    const dataItem_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.formatPermission(dataItem_r10.rolePermissions), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 48);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r11 = ctx.$implicit;\n    const column_r12 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r12)(\"filter\", filter_r11)(\"extra\", true);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 49);\n    i0.ɵɵtemplate(1, RoleListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template, 1, 1, \"ng-template\", 42)(2, RoleListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template, 6, 3, \"ng-template\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 400)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"rolePermissions\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"rolePermissions\"))(\"filterable\", true)(\"sortable\", false);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen037.svg\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r13.status, \" \");\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen040.svg\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r13.status, \" \");\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_span_0_Template, 2, 2, \"span\", 52)(1, RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_span_1_Template, 2, 2, \"span\", 53);\n  }\n  if (rf & 2) {\n    const dataItem_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", dataItem_r13.status === \"Active\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", dataItem_r13.status === \"Inactive\");\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 48);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r14 = ctx.$implicit;\n    const column_r15 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r15)(\"filter\", filter_r14)(\"extra\", true);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 51);\n    i0.ɵɵtemplate(1, RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template, 2, 2, \"ng-template\", 42)(2, RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template, 6, 3, \"ng-template\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 100)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"status\"))(\"headerStyle\", i0.ɵɵpureFunction0(5, _c7))(\"hidden\", ctx_r2.getHiddenField(\"status\"))(\"filterable\", true);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 57);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"br\");\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r16 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind2(3, 3, dataItem_r16.lastUpdatedDate, \"MM/dd/yyyy\"), \" \", i0.ɵɵpipeBind2(4, 6, dataItem_r16.lastUpdatedDate, \"hh:mm a\"), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(dataItem_r16.lastUpdatedByFullName);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 48);\n    i0.ɵɵelement(1, \"kendo-filter-gte-operator\")(2, \"kendo-filter-lte-operator\")(3, \"kendo-filter-eq-operator\")(4, \"kendo-filter-neq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r17 = ctx.$implicit;\n    const column_r18 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r18)(\"filter\", filter_r17)(\"extra\", true);\n  }\n}\nfunction RoleListComponent_ng_container_6_kendo_grid_column_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 56);\n    i0.ɵɵtemplate(1, RoleListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template, 8, 9, \"ng-template\", 42)(2, RoleListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template, 5, 3, \"ng-template\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 180)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"lastUpdatedDate\"))(\"headerStyle\", i0.ɵɵpureFunction0(5, _c7))(\"hidden\", ctx_r2.getHiddenField(\"lastUpdatedDate\"))(\"filterable\", true);\n  }\n}\nfunction RoleListComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, RoleListComponent_ng_container_6_kendo_grid_column_1_Template, 2, 12, \"kendo-grid-column\", 36)(2, RoleListComponent_ng_container_6_kendo_grid_column_2_Template, 3, 8, \"kendo-grid-column\", 37)(3, RoleListComponent_ng_container_6_kendo_grid_column_3_Template, 3, 7, \"kendo-grid-column\", 38)(4, RoleListComponent_ng_container_6_kendo_grid_column_4_Template, 3, 6, \"kendo-grid-column\", 39)(5, RoleListComponent_ng_container_6_kendo_grid_column_5_Template, 3, 6, \"kendo-grid-column\", 40);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const column_r19 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r19 === \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r19 === \"roleName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r19 === \"rolePermissions\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r19 === \"status\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r19 === \"lastUpdatedDate\");\n  }\n}\nfunction RoleListComponent_ng_template_7_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60);\n    i0.ɵɵelement(2, \"i\", 61);\n    i0.ɵɵelementStart(3, \"p\", 15);\n    i0.ɵɵtext(4, \"No roles found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function RoleListComponent_ng_template_7_div_0_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.loadTable());\n    });\n    i0.ɵɵelement(6, \"i\", 63);\n    i0.ɵɵtext(7, \"Refresh \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction RoleListComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, RoleListComponent_ng_template_7_div_0_Template, 8, 0, \"div\", 58);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading && !ctx_r2.isLoading);\n  }\n}\nexport class RoleListComponent {\n  cdr;\n  modalService;\n  exceljsService;\n  httpUtilService;\n  AppService;\n  layoutUtilService;\n  UserService;\n  roleService;\n  kendoColumnService;\n  grid;\n  // Data\n  serverSideRowData = [];\n  gridData = [];\n  IsListHasValue = false;\n  loading = false;\n  isLoading = false;\n  loginUser = {};\n  // Search\n  searchData = '';\n  searchTerms = new Subject();\n  searchSubscription;\n  // Enhanced Filters for Kendo UI\n  filter = {\n    logic: 'and',\n    filters: []\n  };\n  gridFilter = {\n    logic: 'and',\n    filters: []\n  };\n  activeFilters = [];\n  filterOptions = [{\n    text: 'All',\n    value: null\n  }, {\n    text: 'Active',\n    value: 'Active'\n  }, {\n    text: 'Inactive',\n    value: 'Inactive'\n  }];\n  // Advanced filter options\n  advancedFilterOptions = {\n    status: [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Active',\n      value: 'Active'\n    }, {\n      text: 'Inactive',\n      value: 'Inactive'\n    }]\n  };\n  // Filter state\n  showAdvancedFilters = false;\n  appliedFilters = {\n    status: null\n  };\n  // Kendo Grid properties\n  page = {\n    size: 10,\n    pageNumber: 0,\n    totalElements: 0,\n    totalPages: 0,\n    orderBy: 'lastUpdatedDate',\n    orderDir: 'desc'\n  };\n  skip = 0;\n  sort = [{\n    field: 'lastUpdatedDate',\n    dir: 'desc'\n  }];\n  // Column visibility system properties\n  kendoHide;\n  hiddenData = [];\n  kendoColOrder = [];\n  kendoInitColOrder = [];\n  hiddenFields = [];\n  // Column configuration for the new system\n  gridColumns = [];\n  defaultColumns = [];\n  fixedColumns = [];\n  draggableColumns = [];\n  normalGrid;\n  expandedGrid;\n  isExpanded = false;\n  // Enhanced Columns with Kendo UI features\n  gridColumnConfig = [{\n    field: 'action',\n    title: 'Action',\n    width: 80,\n    isFixed: true,\n    type: 'action',\n    order: 1\n  }, {\n    field: 'roleName',\n    title: 'Name',\n    width: 200,\n    isFixed: true,\n    type: 'text',\n    filterable: true,\n    order: 2\n  }, {\n    field: 'rolePermissions',\n    title: 'Permissions',\n    width: 300,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 3\n  }, {\n    field: 'status',\n    title: 'Status',\n    width: 120,\n    type: 'status',\n    isFixed: false,\n    filterable: true,\n    order: 4\n  }, {\n    field: 'lastUpdatedDate',\n    title: 'Updated Date',\n    width: 180,\n    isFixed: false,\n    type: 'date',\n    filterable: true,\n    order: 5\n  }];\n  // OLD SYSTEM - to be removed\n  columnData = [{\n    name: 'Name',\n    prop: 'roleName',\n    order: 'desc',\n    width: '17%',\n    sort: true\n  }, {\n    name: 'Permissions',\n    prop: 'rolePermissions',\n    order: 'desc',\n    width: '40%',\n    sort: true\n  }, {\n    name: 'Status',\n    prop: 'status',\n    order: 'desc',\n    width: '11%',\n    sort: true\n  }, {\n    name: 'Updated',\n    prop: 'lastUpdatedDate',\n    order: 'desc',\n    width: '18%',\n    sort: true\n  }, {\n    name: 'Action',\n    prop: 'Action',\n    order: 'desc',\n    width: '12%',\n    sort: false\n  }];\n  // Router subscription for saving state on navigation\n  routerSubscription;\n  // Storage key for state persistence\n  GRID_STATE_KEY = 'roles-grid-state';\n  // Export options\n  exportOptions = [{\n    text: 'Export All',\n    value: 'all'\n  }, {\n    text: 'Export Selected',\n    value: 'selected'\n  }, {\n    text: 'Export Filtered',\n    value: 'filtered'\n  }];\n  // Selection state\n  selectedRoles = [];\n  isAllSelected = false;\n  // Statistics\n  roleStatistics = {\n    activeRoles: 0,\n    inactiveRoles: 0,\n    totalRoles: 0\n  };\n  // Bulk operations\n  showBulkActions = false;\n  bulkActionStatus = 'Active';\n  // Legacy properties (keeping for backward compatibility)\n  pageSize = AppSettings.PAGE_SIZE;\n  pageSizeOptions = AppSettings.PAGE_SIZE_OPTIONS;\n  itemsPerPage = new FormControl(this.pageSize);\n  defaultOrder = 'desc';\n  defaultOrderBy = 'lastUpdatedDate';\n  defaultRoles = [];\n  statusData = false;\n  permissionArray = [];\n  selectedTab = 'All';\n  innerWidth;\n  displayMobile = false;\n  constructor(cdr, modalService, exceljsService, httpUtilService, AppService, layoutUtilService, UserService, roleService, kendoColumnService) {\n    this.cdr = cdr;\n    this.modalService = modalService;\n    this.exceljsService = exceljsService;\n    this.httpUtilService = httpUtilService;\n    this.AppService = AppService;\n    this.layoutUtilService = layoutUtilService;\n    this.UserService = UserService;\n    this.roleService = roleService;\n    this.kendoColumnService = kendoColumnService;\n    // set the default paging options\n    this.page.pageNumber = 0;\n    this.page.size = this.pageSize;\n    this.page.orderBy = 'LastUpdatedDate';\n    this.page.orderDir = 'desc';\n  }\n  ngOnInit() {\n    this.loginUser = this.AppService.getLoggedInUser();\n    console.log('Login user loaded:', this.loginUser);\n    this.innerWidth = window.innerWidth;\n    if (this.innerWidth >= 320 && this.innerWidth < 768) {\n      this.displayMobile = true;\n    } else {\n      this.displayMobile = false;\n    }\n    // Setup search with debounce\n    this.searchSubscription = this.searchTerms.pipe(debounceTime(500), distinctUntilChanged()).subscribe(searchTerm => {\n      console.log('Search triggered with term:', searchTerm);\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      // Set loading state for search\n      this.loading = true;\n      this.isLoading = true;\n      this.loadTable();\n    });\n    // Load roles for advanced filters\n    this.loadRoles();\n    // Initialize with default page load\n    this.onPageLoad();\n    // Initialize new column visibility system\n    this.initializeColumnVisibilitySystem();\n    // Load column configuration after a short delay to ensure loginUser is available\n    setTimeout(() => {\n      this.loadColumnConfigFromDatabase();\n    }, 100);\n    localStorage.removeItem('keyword');\n  }\n  /**\n   * Initialize the new column visibility system\n   */\n  initializeColumnVisibilitySystem() {\n    // Initialize default columns\n    this.defaultColumns = this.gridColumnConfig.map(col => col.field);\n    this.gridColumns = [...this.defaultColumns];\n    // Set fixed columns (first 2 columns)\n    this.fixedColumns = ['action', 'roleName'];\n    // Set draggable columns (all except fixed)\n    this.draggableColumns = this.defaultColumns.filter(col => !this.fixedColumns.includes(col));\n    // Initialize normal and expanded grid references\n    this.normalGrid = this.grid;\n    this.expandedGrid = this.grid;\n  }\n  ngAfterViewInit() {\n    // Load the table after the view is initialized\n    // Small delay to ensure the grid is properly rendered\n    setTimeout(() => {\n      this.loadTable();\n    }, 200);\n  }\n  // Method to handle initial page load\n  onPageLoad() {\n    // Initialize the component with default data\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.sort = [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.searchData = '';\n    // Load the data\n    this.loadTable();\n  }\n  // Load roles for advanced filters\n  loadRoles() {\n    this.UserService.getDefaultPermissions({}).subscribe(permissions => {\n      this.permissionArray = permissions.responseData;\n    });\n  }\n  // Method to handle when the component becomes visible\n  onTabActivated() {\n    // Refresh the data when the tab is activated\n    this.loadTable();\n  }\n  ngOnDestroy() {\n    // Clean up subscriptions\n    if (this.routerSubscription) {\n      this.routerSubscription.unsubscribe();\n    }\n    if (this.searchSubscription) {\n      this.searchSubscription.unsubscribe();\n    }\n    this.searchTerms.complete();\n  }\n  // function to get roles data from API\n  loadTable() {\n    // Use the new Kendo UI specific endpoint\n    this.loadTableWithKendoEndpoint();\n  }\n  // New method to load data using Kendo UI specific endpoint\n  loadTableWithKendoEndpoint() {\n    this.loading = true;\n    this.isLoading = true;\n    // Enable loader\n    this.httpUtilService.loadingSubject.next(true);\n    // Prepare state object for Kendo UI endpoint\n    console.log('=== BACKEND REQUEST DEBUG ===');\n    console.log('this.sort:', this.sort);\n    console.log('page.orderBy:', this.page.orderBy);\n    console.log('page.orderDir:', this.page.orderDir);\n    const state = {\n      take: this.page.size,\n      skip: this.skip,\n      sort: this.sort,\n      // Send exactly what Kendo has\n      filter: this.filter,\n      search: this.searchData,\n      loggedInUserId: this.loginUser.userId,\n      orderBy: this.page.orderBy,\n      orderDir: this.page.orderDir\n    };\n    console.log('Loading roles table with search term:', this.searchData);\n    console.log('Full state object:', state);\n    console.log('=== END BACKEND REQUEST DEBUG ===');\n    this.roleService.getRolesForKendoGrid(state).subscribe({\n      next: data => {\n        // Handle the new API response structure\n        if (data.isFault || data.responseData && data.responseData.errors && data.responseData.errors.length > 0) {\n          const errors = data.responseData?.errors || data.errors || [];\n          console.error('Kendo UI Grid errors:', errors);\n          this.handleEmptyResponse();\n        } else {\n          // Handle both old and new response structures\n          const responseData = data.responseData || data;\n          const roleData = responseData.data || [];\n          const total = responseData.total || 0;\n          this.IsListHasValue = roleData.length !== 0;\n          this.serverSideRowData = this.sortGridDataClientSide(roleData, this.sort);\n          this.gridData = this.serverSideRowData;\n          this.page.totalElements = total;\n          this.page.totalPages = Math.ceil(total / this.page.size);\n        }\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n        this.cdr.markForCheck();\n      },\n      error: error => {\n        console.error('Error loading data with Kendo UI endpoint:', error);\n        this.handleEmptyResponse();\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  // Handle empty response\n  handleEmptyResponse() {\n    this.IsListHasValue = false;\n    this.serverSideRowData = [];\n    this.gridData = [];\n    this.page.totalElements = 0;\n    this.page.totalPages = 0;\n    this.loading = false;\n    this.isLoading = false;\n  }\n  // Kendo Grid event handlers\n  pageChange(event) {\n    this.skip = event.skip;\n    this.page.size = event.take;\n    this.page.pageNumber = event.skip / event.take;\n    // Set loading state for pagination\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTableWithKendoEndpoint();\n  }\n  onSortChange(sort) {\n    // Check if this is the 3rd click (dir is undefined)\n    const isThirdClick = sort.length > 0 && sort[0] && sort[0].dir === undefined;\n    if (isThirdClick) {\n      // 3rd click - clear sort and use default\n      this.sort = [];\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n    } else if (sort.length > 0 && sort[0] && sort[0].dir) {\n      // Valid sort with direction\n      this.sort = sort;\n      const field = sort[0].field;\n      const dir = sort[0].dir;\n      // Normalize field names to match backend mapping\n      let normalizedField = field;\n      switch (field) {\n        case 'Name':\n          normalizedField = 'roleName';\n          break;\n        case 'Status':\n          normalizedField = 'status';\n          break;\n        case 'UpdatedDate':\n        case 'LastUpdatedDate':\n        case 'lastUpdatedByFullName':\n          normalizedField = 'lastUpdatedDate';\n          break;\n      }\n      this.page.orderBy = normalizedField;\n      this.page.orderDir = dir;\n    } else {\n      // Empty sort array or invalid sort\n      this.sort = [];\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n    }\n    // Reset to first page\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    // Set loading state for sorting\n    this.loading = true;\n    this.isLoading = true;\n    console.log('=== END SORT CHANGE DEBUG ===');\n    this.loadTableWithKendoEndpoint();\n  }\n  filterChange(event) {\n    this.filter = event.filter;\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    // Set loading state for filtering\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTableWithKendoEndpoint();\n  }\n  // Client-side fallback sorting to guarantee visible order\n  sortGridDataClientSide(data, sort) {\n    if (!Array.isArray(data) || !sort || sort.length === 0) return data;\n    const s = sort[0];\n    const field = s.field || 'lastUpdatedDate';\n    const dir = (s.dir || 'asc') === 'asc' ? 1 : -1;\n    const parseUsDate = val => {\n      // Supports formats like 8/21/25 1:58 PM\n      const m = val && val.match(/^(\\d{1,2})\\/(\\d{1,2})\\/(\\d{2,4})\\s+(\\d{1,2}):(\\d{2})\\s*(AM|PM)$/i);\n      if (!m) return NaN;\n      let month = parseInt(m[1], 10) - 1;\n      const day = parseInt(m[2], 10);\n      let year = parseInt(m[3], 10);\n      if (year < 100) year += 2000;\n      let hour = parseInt(m[4], 10);\n      const minute = parseInt(m[5], 10);\n      const ampm = m[6].toUpperCase();\n      if (ampm === 'PM' && hour < 12) hour += 12;\n      if (ampm === 'AM' && hour === 12) hour = 0;\n      return new Date(year, month, day, hour, minute).getTime();\n    };\n    const getVal = item => {\n      const v = item[field];\n      if (field === 'lastUpdatedDate') {\n        if (v instanceof Date) return v.getTime();\n        const ts = Date.parse(v);\n        if (!isNaN(ts)) return ts;\n        const us = parseUsDate(String(v));\n        return isNaN(us) ? 0 : us;\n      }\n      if (typeof v === 'string') return v.toLowerCase();\n      return v;\n    };\n    try {\n      const sorted = [...data].sort((a, b) => {\n        const av = getVal(a);\n        const bv = getVal(b);\n        if (av == null && bv == null) return 0;\n        if (av == null) return -1 * dir;\n        if (bv == null) return 1 * dir;\n        if (av > bv) return 1 * dir;\n        if (av < bv) return -1 * dir;\n        return 0;\n      });\n      return sorted;\n    } catch {\n      return data;\n    }\n  }\n  onSelectionChange(event) {\n    this.selectedRoles = event.selectedRows || [];\n    this.isAllSelected = this.selectedRoles.length === this.serverSideRowData.length;\n  }\n  // Search methods\n  onSearchKeyDown(event) {\n    if (event.key === 'Enter') {\n      this.searchTerms.next(this.searchData);\n    }\n  }\n  // Handle search input changes\n  onSearchInput() {\n    // Trigger search on every input change with debouncing\n    console.log('Search input changed:', this.searchData);\n    this.searchTerms.next(this.searchData);\n  }\n  // Handle search model changes\n  onSearchChange() {\n    // Trigger search when model changes\n    console.log('Search model changed:', this.searchData);\n    this.searchTerms.next(this.searchData);\n  }\n  clearSearch() {\n    // Clear search data and trigger search\n    this.searchData = '';\n    // Set loading state for clear search\n    this.loading = true;\n    this.isLoading = true;\n    this.searchTerms.next('');\n  }\n  // Advanced filter methods\n  toggleAdvancedFilters() {\n    this.showAdvancedFilters = !this.showAdvancedFilters;\n  }\n  applyAdvancedFilters() {\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.loadTableWithKendoEndpoint();\n  }\n  clearAllFilters() {\n    this.appliedFilters = {\n      status: null\n    };\n    this.activeFilters = [];\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.loadTableWithKendoEndpoint();\n  }\n  // Grid expansion methods\n  toggleExpand() {\n    // Find grid container element and toggle fullscreen class\n    const gridContainer = document.querySelector('.grid-container');\n    if (gridContainer) {\n      gridContainer.classList.toggle('fullscreen-grid');\n      this.isExpanded = !this.isExpanded;\n      // Refresh grid after resize to ensure proper rendering\n      if (this.grid) {\n        this.grid.refresh();\n      }\n    }\n  }\n  // Export methods\n  onExportClick(event) {\n    switch (event.item.value) {\n      case 'all':\n        this.exportall();\n        break;\n      case 'selected':\n        if (this.selectedRoles.length > 0) {\n          this.exportRowData(this.selectedRoles);\n        } else {\n          this.layoutUtilService.showError('Please select roles to export', '');\n        }\n        break;\n      case 'filtered':\n        this.exportRowData(this.serverSideRowData);\n        break;\n      default:\n        this.exportall();\n    }\n  }\n  // Column visibility methods\n  /**\n   * Saves the current state of column visibility and order in the grid.\n   */\n  saveHead() {\n    // Check if loginUser is available\n    if (!this.loginUser || !this.loginUser.userId) {\n      console.error('loginUser not available:', this.loginUser);\n      this.layoutUtilService.showError('User not logged in. Please refresh the page.', '');\n      return;\n    }\n    const nonHiddenColumns = [];\n    const hiddenColumns = [];\n    if (this.grid && this.grid.columns) {\n      this.grid.columns.forEach(column => {\n        if (!column.hidden) {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden\n          };\n          nonHiddenColumns.push(columnData);\n        } else {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden\n          };\n          hiddenColumns.push(columnData);\n        }\n      });\n    }\n    const draggableColumnsOrder = this.gridColumns.filter(col => !this.fixedColumns.includes(col)).map((field, index) => ({\n      field,\n      orderIndex: index\n    }));\n    // Prepare data for backend\n    const userData = {\n      pageName: 'Roles',\n      userID: this.loginUser.userId,\n      hiddenData: hiddenColumns,\n      kendoColOrder: draggableColumnsOrder,\n      LoggedId: this.loginUser.userId\n    };\n    // Show loading state\n    this.httpUtilService.loadingSubject.next(true);\n    // Save to backend\n    this.kendoColumnService.createHideFields(userData).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!res.isFault) {\n          // Update local state\n          this.hiddenData = hiddenColumns;\n          this.kendoColOrder = draggableColumnsOrder;\n          this.hiddenFields = this.hiddenData.map(col => col.field);\n          // Also save to localStorage as backup\n          this.kendoColumnService.saveToLocalStorage(userData);\n          this.layoutUtilService.showSuccess(res.message || 'Column settings saved successfully.', '');\n        } else {\n          this.layoutUtilService.showError(res.message || 'Failed to save column settings.', '');\n        }\n        this.cdr.markForCheck();\n      },\n      error: error => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error saving column settings:', error);\n        // Fallback to localStorage on error\n        this.kendoColumnService.saveToLocalStorage(userData);\n        // Update local state\n        this.hiddenData = hiddenColumns;\n        this.kendoColOrder = draggableColumnsOrder;\n        this.hiddenFields = this.hiddenData.map(col => col.field);\n        this.layoutUtilService.showError('Failed to save to server. Settings saved locally.', '');\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Reset the current state of column visibility and order in the grid to its original state.\n   */\n  resetTable() {\n    // Check if loginUser is available\n    if (!this.loginUser || !this.loginUser.userId) {\n      console.error('loginUser not available:', this.loginUser);\n      this.layoutUtilService.showError('User not logged in. Please refresh the page and try again.', '');\n      return;\n    }\n    // Double-check authentication token\n    const token = this.AppService.getLocalStorageItem('permitToken', true);\n    if (!token) {\n      console.error('Authentication token not found');\n      this.layoutUtilService.showError('Authentication token not found. Please login again.', '');\n      return;\n    }\n    // Reset all state variables\n    this.searchData = '';\n    this.activeFilters = [];\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.skip = 0;\n    this.page.pageNumber = 0;\n    this.gridColumns = [...this.defaultColumns];\n    // Reset sort state to default\n    this.sort = [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    this.page.orderBy = 'lastUpdatedDate';\n    this.page.orderDir = 'desc';\n    // Reset advanced filters\n    this.appliedFilters = {\n      status: null\n    };\n    // Reset advanced filters visibility\n    this.showAdvancedFilters = false;\n    // Reset column order index\n    if (this.grid && this.grid.columns) {\n      this.grid.columns.forEach(column => {\n        const index = this.gridColumns.indexOf(column.field);\n        if (index !== -1) {\n          column.orderIndex = index;\n        }\n        // Reset column visibility - show all columns\n        if (column.field && column.field !== 'action') {\n          column.hidden = false;\n        }\n      });\n    }\n    // Clear hidden columns\n    this.hiddenData = [];\n    this.kendoColOrder = [];\n    this.hiddenFields = [];\n    // Reset the Kendo Grid's internal state\n    if (this.grid) {\n      // Clear all filters\n      this.grid.filter = {\n        logic: 'and',\n        filters: []\n      };\n      // Reset sorting\n      this.grid.sort = [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      // Reset to first page\n      this.grid.skip = 0;\n      this.grid.pageSize = this.page.size;\n    }\n    // Prepare reset data\n    const userData = {\n      pageName: 'Roles',\n      userID: this.loginUser.userId,\n      hiddenData: [],\n      kendoColOrder: [],\n      LoggedId: this.loginUser.userId\n    };\n    // Only clear local settings; do not call server\n    this.kendoColumnService.clearFromLocalStorage('Roles');\n    // Show loader and refresh grid\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n    this.cdr.detectChanges();\n    // Force grid refresh to apply all changes\n    if (this.grid) {\n      setTimeout(() => {\n        this.grid.refresh();\n        this.grid.reset();\n      }, 100);\n    }\n    this.loadTable();\n  }\n  /**\n   * Refresh grid data\n   */\n  refreshGrid() {\n    // Set loading state to show full-screen loader\n    this.loading = true;\n    this.isLoading = true;\n    // Reset to first page and clear any applied filters\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.gridFilter = {\n      logic: 'and',\n      filters: []\n    };\n    this.activeFilters = [];\n    this.appliedFilters = {\n      status: null\n    };\n    // Clear search data\n    this.searchData = '';\n    // Load fresh data from API\n    this.loadTable();\n  }\n  /**\n   * Loads and applies the saved column order from the user preferences or configuration.\n   */\n  loadSavedColumnOrder(kendoColOrder) {\n    try {\n      const savedOrder = kendoColOrder;\n      if (savedOrder) {\n        const parsedOrder = savedOrder;\n        if (Array.isArray(parsedOrder) && parsedOrder.length > 0) {\n          // Get only the draggable columns from saved order\n          const savedDraggableColumns = parsedOrder.sort((a, b) => a.orderIndex - b.orderIndex).map(col => col.field).filter(field => !this.fixedColumns.includes(field));\n          // Add any missing draggable columns at the end\n          const missingColumns = this.draggableColumns.filter(col => !savedDraggableColumns.includes(col));\n          // Combine fixed columns with saved draggable columns\n          this.gridColumns = [...this.fixedColumns, ...savedDraggableColumns, ...missingColumns];\n        } else {\n          this.gridColumns = [...this.defaultColumns];\n        }\n      } else {\n        this.gridColumns = [...this.defaultColumns];\n      }\n    } catch (error) {\n      this.gridColumns = [...this.defaultColumns];\n    }\n  }\n  /**\n   * Checks if a given column is marked as hidden.\n   */\n  getHiddenField(columnName) {\n    return this.hiddenFields.indexOf(columnName) > -1;\n  }\n  /**\n   * Handles the column reordering event triggered when a column is moved by the user.\n   */\n  onColumnReorder(event) {\n    const {\n      columns,\n      newIndex,\n      oldIndex\n    } = event;\n    // Prevent reordering of fixed columns\n    if (this.fixedColumns.includes(columns[oldIndex].field) || this.fixedColumns.includes(columns[newIndex].field)) {\n      return;\n    }\n    // Update the gridColumns array\n    const reorderedColumns = [...this.gridColumns];\n    const [movedColumn] = reorderedColumns.splice(oldIndex, 1);\n    reorderedColumns.splice(newIndex, 0, movedColumn);\n    this.gridColumns = reorderedColumns;\n    this.cdr.markForCheck();\n  }\n  /**\n   * Handles column visibility changes from the Kendo Grid.\n   */\n  updateColumnVisibility(event) {\n    const {\n      column,\n      hidden\n    } = event;\n    // Update hiddenData array\n    const existingIndex = this.hiddenData.findIndex(item => item.field === column.field);\n    if (hidden && existingIndex === -1) {\n      // Add to hidden columns\n      this.hiddenData.push({\n        title: column.title,\n        field: column.field,\n        hidden: true\n      });\n    } else if (!hidden && existingIndex !== -1) {\n      // Remove from hidden columns\n      this.hiddenData.splice(existingIndex, 1);\n    }\n    // Update hiddenFields array\n    this.hiddenFields = this.hiddenData.map(col => col.field);\n    this.cdr.markForCheck();\n  }\n  /**\n   * Loads the saved column configuration from the backend or localStorage as fallback.\n   */\n  loadColumnConfigFromDatabase() {\n    try {\n      // First try to load from backend\n      if (this.loginUser && this.loginUser.userId) {\n        this.kendoColumnService.getHideFields({\n          pageName: 'Roles',\n          userID: this.loginUser.userId\n        }).subscribe({\n          next: res => {\n            if (!res.isFault && res.Data) {\n              this.kendoHide = res.Data;\n              this.hiddenData = res.Data.hideData ? JSON.parse(res.Data.hideData) : [];\n              this.kendoInitColOrder = res.Data.kendoColOrder ? JSON.parse(res.Data.kendoColOrder) : [];\n              this.hiddenFields = this.hiddenData.map(col => col.field);\n              // Update grid columns based on the hidden fields\n              if (this.grid && this.grid.columns) {\n                this.grid.columns.forEach(column => {\n                  if (this.hiddenData.some(item => item.title === column.title && item.hidden)) {\n                    column.includeInChooser = true;\n                    column.hidden = true;\n                  } else {\n                    column.hidden = false;\n                  }\n                });\n              }\n              // Load saved column order and update grid\n              this.loadSavedColumnOrder(this.kendoInitColOrder);\n              // Also save to localStorage as backup\n              this.kendoColumnService.saveToLocalStorage({\n                pageName: 'Roles',\n                userID: this.loginUser.userId,\n                hiddenData: this.hiddenData,\n                kendoColOrder: this.kendoInitColOrder\n              });\n            }\n          },\n          error: error => {\n            console.error('Error loading from backend, falling back to localStorage:', error);\n            this.loadFromLocalStorageFallback();\n          }\n        });\n      } else {\n        // Fallback to localStorage if no user ID\n        this.loadFromLocalStorageFallback();\n      }\n    } catch (error) {\n      console.error('Error loading column configuration:', error);\n      this.loadFromLocalStorageFallback();\n    }\n  }\n  /**\n   * Fallback method to load column configuration from localStorage\n   */\n  loadFromLocalStorageFallback() {\n    try {\n      const savedConfig = this.kendoColumnService.getFromLocalStorage('Roles', this.loginUser?.UserId || 0);\n      if (savedConfig) {\n        this.kendoHide = savedConfig;\n        this.hiddenData = savedConfig.hiddenData || [];\n        this.kendoInitColOrder = savedConfig.kendoColOrder || [];\n        this.hiddenFields = this.hiddenData.map(col => col.field);\n        // Update grid columns based on the hidden fields\n        if (this.grid && this.grid.columns) {\n          this.grid.columns.forEach(column => {\n            if (this.hiddenData.some(item => item.title === column.title && item.hidden)) {\n              column.includeInChooser = true;\n              column.hidden = true;\n            } else {\n              column.hidden = false;\n            }\n          });\n        }\n        // Load saved column order and update grid\n        this.loadSavedColumnOrder(this.kendoInitColOrder);\n      }\n    } catch (error) {\n      console.error('Error loading from localStorage fallback:', error);\n    }\n  }\n  //function to form permission from API\n  formatPermission(pemissions) {\n    let permArray = JSON.parse(pemissions);\n    let permData = '';\n    each(permArray, (r, i) => {\n      _.forEach(r, function (value, key) {\n        let rArray = [];\n        value.indexOf('Read') === -1 ? '' : rArray.push('Read');\n        value.indexOf('Write') === -1 ? '' : rArray.push('Write');\n        value.indexOf('Delete') === -1 ? '' : rArray.push('Delete');\n        let nIndex = value.length > 0 ? rArray.join(', ') : 'None';\n        permData += \"<span class='badge badge-light-primary me-2 mt-1'>\" + key + \" : \" + nIndex + \"</span>\";\n      });\n    });\n    return permData;\n  }\n  // function to search the data when there is a typing in the search box\n  search(event) {\n    this.page.pageNumber = 0;\n    this.loadTable();\n  }\n  //function to filter data from search\n  filterConfiguration() {\n    let filter = {};\n    let searchText;\n    if (this.searchData === null) {\n      searchText = ' ';\n    } else {\n      searchText = this.searchData;\n    }\n    filter.paginate = true;\n    filter.Category = this.selectedTab;\n    filter.search = searchText.trim();\n    return filter;\n  }\n  // function to get the roles data based on the page selection in Pagination\n  serverSideSetPage(event) {\n    this.page.pageNumber = event - 1;\n    this.loadTable();\n  }\n  // function to get the roles data based on the sort selection\n  // Params : Orderby - field to be sorted, OrderDir - asc/desc\n  changeOrder(Orderby, OrderDir) {\n    this.defaultOrder = OrderDir === 'desc' ? 'asc' : 'desc';\n    let indexColumn = this.columnData.findIndex(cd => cd.prop === Orderby);\n    this.columnData[indexColumn].order = this.defaultOrder;\n    this.cdr.markForCheck();\n    this.defaultOrderBy = Orderby;\n    this.page.orderDir = this.defaultOrder;\n    this.page.orderBy = Orderby;\n    this.loadTable();\n  }\n  // function to get the roles based on the items per page selection\n  pageLimit(num) {\n    this.pageSize = Number(num);\n    this.page.pageNumber = 0;\n    if (this.serverSideRowData) this.page.size = Number(num);\n    this.loadTable();\n  }\n  // function to create a new roles\n  create() {\n    this.edit(0);\n  }\n  // function to edit a particular role\n  edit(id) {\n    var NgbModalOptions = {\n      size: 'lg',\n      backdrop: 'static',\n      keyboard: false,\n      scrollable: true\n    };\n    const modalRef = this.modalService.open(RoleEditComponent, NgbModalOptions);\n    modalRef.componentInstance.id = id;\n    modalRef.componentInstance.permissions = this.permissionArray;\n    //get response from edit user modal\n    modalRef.componentInstance.passEntry.subscribe(receivedEntry => {\n      if (receivedEntry == true) {\n        this.loadTable();\n      }\n    });\n  }\n  //function to export particular page data\n  exportRowData(rowdata) {\n    if (rowdata !== undefined) {\n      if (rowdata.length > 0) {\n        // declare the title and header data for excel\n        const tableTitle = 'Roles';\n        const headerArray = ['Name', 'Permissions', 'Status', 'Description', 'Created Date', 'Created Time', 'Created By User', 'Modified Date', 'Modified Time', 'Modified By User'];\n        // get the data for excel in a array format\n        const respResult = [];\n        each(rowdata, rowdata => {\n          const respData = [];\n          respData.push(rowdata.Name);\n          respData.push(this.formatexcelPermission(rowdata.Permissions));\n          respData.push(rowdata.Status);\n          respData.push(rowdata.Description);\n          respData.push(this.AppService.unixDate(rowdata.CreatedDate));\n          respData.push(this.AppService.unixTime(rowdata.CreatedDate));\n          respData.push(rowdata.CreatedUserFullName);\n          respData.push(this.AppService.unixDate(rowdata.LastUpdatedDate));\n          respData.push(this.AppService.unixTime(rowdata.LastUpdatedDate));\n          respData.push(rowdata.LastUpdatedUserFullName);\n          respResult.push(respData);\n        });\n        // assign the width for each column\n        const colSize = [{\n          id: 1,\n          width: 30\n        }, {\n          id: 2,\n          width: 50\n        }, {\n          id: 3,\n          width: 40\n        }, {\n          id: 4,\n          width: 30\n        }, {\n          id: 5,\n          width: 30\n        }, {\n          id: 6,\n          width: 30\n        }, {\n          id: 7,\n          width: 30\n        }, {\n          id: 8,\n          width: 30\n        }, {\n          id: 9,\n          width: 30\n        }, {\n          id: 10,\n          width: 30\n        }];\n        this.cdr.markForCheck();\n        this.exceljsService.generateExcel(tableTitle, headerArray, respResult, colSize);\n      } else {\n        this.layoutUtilService.showError('There is no available data to export', '');\n      }\n    } else {\n      this.layoutUtilService.showError('There is no available data to export', '');\n    }\n  }\n  // function to get all the users data from api call and then export those in a excel file\n  exportall() {\n    const queryparams = {\n      paginate: false\n    };\n    this.httpUtilService.loadingSubject.next(true);\n    this.UserService.getAllRolesWithUserInfo(queryparams).pipe(map(data => data)).subscribe(data => {\n      if (!data.isFault) {\n        this.exportRowData(data.responseData);\n        this.cdr.markForCheck();\n        this.httpUtilService.loadingSubject.next(false);\n      } else {\n        this.httpUtilService.loadingSubject.next(false);\n      }\n    });\n  }\n  // function to display the popup to get confirmation to delete roles\n  deleteRoles(role) {\n    var NgbModalOptions = {\n      size: 'md',\n      backdrop: 'static',\n      keyboard: false,\n      scrollable: true\n    };\n    const modalRef = this.modalService.open(ConfirmationDialogComponent, NgbModalOptions);\n    modalRef.componentInstance.id = role.roleId;\n    modalRef.componentInstance.showClose = true;\n    modalRef.componentInstance.description = \"Are you sure to delete this role?\";\n    modalRef.componentInstance.actionButtonText = \"Yes\";\n    modalRef.componentInstance.cancelButtonText = \"Cancel\";\n    modalRef.componentInstance.title = \"Delete Role - \" + role.roleName;\n    modalRef.componentInstance.passEntry.subscribe(receivedEntry => {\n      console.log('receivedEntry ', receivedEntry);\n      if (receivedEntry.success == true) {\n        let reStart = {\n          roleId: role.roleId,\n          loggedInUserId: this.loginUser.userId\n        };\n        this.httpUtilService.loadingSubject.next(true);\n        this.UserService.deleteRole(reStart).subscribe(data => {\n          if (!data.isFault) {\n            this.httpUtilService.loadingSubject.next(false);\n            this.layoutUtilService.showSuccess(data.responseData.message, '');\n            this.ngOnInit();\n          }\n        });\n      }\n    });\n  }\n  //function to format permission for export excel\n  formatexcelPermission(pemissions) {\n    let permArray = JSON.parse(pemissions);\n    let permData = '';\n    each(permArray, (r, i) => {\n      _.forEach(r, function (value, key) {\n        let rArray = [];\n        value.indexOf('Read') === -1 ? '' : rArray.push('Read');\n        value.indexOf('Write') === -1 ? '' : rArray.push('Write');\n        value.indexOf('Delete') === -1 ? '' : rArray.push('Delete');\n        let nIndex = value.length > 0 ? rArray.join(', ') : 'None';\n        permData += key + \" : \" + nIndex + '\\n';\n      });\n    });\n    return permData;\n  }\n  static ɵfac = function RoleListComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RoleListComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.NgbModal), i0.ɵɵdirectiveInject(i2.ExceljsService), i0.ɵɵdirectiveInject(i3.HttpUtilsService), i0.ɵɵdirectiveInject(i4.AppService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.UserService), i0.ɵɵdirectiveInject(i7.RoleService), i0.ɵɵdirectiveInject(i8.KendoColumnService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: RoleListComponent,\n    selectors: [[\"app-role-list\"]],\n    viewQuery: function RoleListComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n      }\n    },\n    decls: 8,\n    vars: 21,\n    consts: [[\"normalGrid\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"grid-container\"], [2, \"width\", \"auto\", \"overflow-x\", \"auto\", 3, \"columnReorder\", \"selectionChange\", \"filterChange\", \"pageChange\", \"sortChange\", \"columnVisibilityChange\", \"data\", \"pageSize\", \"sort\", \"pageable\", \"sortable\", \"groupable\", \"selectable\", \"reorderable\", \"resizable\", \"height\", \"skip\", \"filter\", \"columnMenu\"], [\"kendoGridToolbarTemplate\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"kendoGridNoRecordsTemplate\", \"\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"fs-5\"], [1, \"d-flex\", \"align-items-center\", \"me-3\", \"search-section\"], [\"placeholder\", \"Search...\", 3, \"ngModelChange\", \"keydown\", \"valueChange\", \"ngModel\", \"clearButton\"], [1, \"d-flex\", \"align-items-center\", \"me-3\"], [1, \"text-muted\"], [1, \"fw-bold\", \"ms-1\"], [\"type\", \"button\", \"title\", \"Add Role\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", \"text-primary\", 3, \"inlineSVG\"], [\"type\", \"button\", \"title\", \"Toggle Grid Expansion\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"text-secondary\"], [\"type\", \"button\", \"title\", \"Reset to Default\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"text-warning\"], [\"type\", \"button\", \"title\", \"Refresh Grid Data\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\", \"text-info\"], [\"class\", \"advanced-filters-panel p-3 bg-light border-bottom\", 4, \"ngIf\"], [1, \"advanced-filters-panel\", \"p-3\", \"bg-light\", \"border-bottom\"], [1, \"row\"], [1, \"col-md-3\"], [1, \"form-label\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Status\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [1, \"col-md-3\", \"d-flex\", \"align-items-end\"], [\"kendoButton\", \"\", 1, \"btn-primary\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [\"kendoButton\", \"\", 1, \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"sortable\", \"style\", \"hidden\", 4, \"ngIf\"], [\"field\", \"roleName\", \"title\", \"Name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"rolePermissions\", \"title\", \"Permissions\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", \"sortable\", 4, \"ngIf\"], [\"field\", \"status\", \"title\", \"Status\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", \"format\", \"MM/dd/yyyy\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"sortable\", \"hidden\"], [\"kendoGridCellTemplate\", \"\"], [\"title\", \"Edit\", 1, \"btn\", \"btn-icon\", \"btn-sm\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-primary\", 3, \"inlineSVG\"], [\"field\", \"roleName\", \"title\", \"Name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\"], [\"kendoGridFilterMenuTemplate\", \"\"], [1, \"fw-bolder\", \"cursor-pointer\"], [3, \"column\", \"filter\", \"extra\"], [\"field\", \"rolePermissions\", \"title\", \"Permissions\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", \"sortable\"], [3, \"innerHTML\"], [\"field\", \"status\", \"title\", \"Status\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"ngbTooltip\", \"Active\", \"class\", \"svg-icon svg-icon-3 svg-icon-success\", 3, \"inlineSVG\", 4, \"ngIf\"], [\"ngbTooltip\", \"Inactive\", \"class\", \"svg-icon svg-icon-3 svg-icon-danger text-danger\", 3, \"inlineSVG\", 4, \"ngIf\"], [\"ngbTooltip\", \"Active\", 1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-success\", 3, \"inlineSVG\"], [\"ngbTooltip\", \"Inactive\", 1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-danger\", \"text-danger\", 3, \"inlineSVG\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", \"format\", \"MM/dd/yyyy\", 3, \"width\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [1, \"fw-bolder\"], [\"class\", \"custom-no-records\", 4, \"ngIf\"], [1, \"custom-no-records\"], [1, \"text-center\"], [1, \"fas\", \"fa-shield-alt\", \"text-muted\", \"mb-2\", 2, \"font-size\", \"2rem\"], [\"kendoButton\", \"\", 1, \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-refresh\", \"me-2\"]],\n    template: function RoleListComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵtemplate(0, RoleListComponent_div_0_Template, 7, 0, \"div\", 1);\n        i0.ɵɵelementStart(1, \"div\", 2)(2, \"kendo-grid\", 3, 0);\n        i0.ɵɵlistener(\"columnReorder\", function RoleListComponent_Template_kendo_grid_columnReorder_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onColumnReorder($event));\n        })(\"selectionChange\", function RoleListComponent_Template_kendo_grid_selectionChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSelectionChange($event));\n        })(\"filterChange\", function RoleListComponent_Template_kendo_grid_filterChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.filterChange($event));\n        })(\"pageChange\", function RoleListComponent_Template_kendo_grid_pageChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.pageChange($event));\n        })(\"sortChange\", function RoleListComponent_Template_kendo_grid_sortChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSortChange($event));\n        })(\"columnVisibilityChange\", function RoleListComponent_Template_kendo_grid_columnVisibilityChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.updateColumnVisibility($event));\n        });\n        i0.ɵɵtemplate(4, RoleListComponent_ng_template_4_Template, 16, 10, \"ng-template\", 4)(5, RoleListComponent_ng_template_5_Template, 1, 1, \"ng-template\", 4)(6, RoleListComponent_ng_container_6_Template, 6, 5, \"ng-container\", 5)(7, RoleListComponent_ng_template_7_Template, 1, 1, \"ng-template\", 6);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.loading || ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"data\", ctx.serverSideRowData)(\"pageSize\", ctx.page.size)(\"sort\", ctx.sort)(\"pageable\", i0.ɵɵpureFunction1(16, _c2, i0.ɵɵpureFunction0(15, _c1)))(\"sortable\", i0.ɵɵpureFunction0(18, _c3))(\"groupable\", false)(\"selectable\", i0.ɵɵpureFunction0(19, _c4))(\"reorderable\", true)(\"resizable\", false)(\"height\", 720)(\"skip\", ctx.skip)(\"filter\", ctx.filter)(\"columnMenu\", i0.ɵɵpureFunction0(20, _c5));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.gridColumns);\n      }\n    },\n    dependencies: [i9.NgForOf, i9.NgIf, i10.NgControlStatus, i10.NgModel, i1.NgbTooltip, i11.GridComponent, i11.ToolbarTemplateDirective, i11.GridSpacerComponent, i11.ColumnComponent, i11.CellTemplateDirective, i11.NoRecordsTemplateDirective, i11.ContainsFilterOperatorComponent, i11.EndsWithFilterOperatorComponent, i11.EqualFilterOperatorComponent, i11.NotEqualFilterOperatorComponent, i11.StartsWithFilterOperatorComponent, i11.GreaterOrEqualToFilterOperatorComponent, i11.LessOrEqualToFilterOperatorComponent, i11.StringFilterMenuComponent, i11.FilterMenuTemplateDirective, i11.DateFilterMenuComponent, i12.TextBoxComponent, i13.ButtonComponent, i14.InlineSVGDirective, i15.DropDownListComponent, i9.DatePipe],\n    styles: [\".grid-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  position: relative;\\n}\\n\\n.fullscreen-grid[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 9999;\\n  background: white;\\n  padding: 20px;\\n  overflow: auto;\\n}\\n\\n\\n\\n.grid-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n.grid-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 500;\\n}\\n.grid-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%] {\\n  width: 300px;\\n}\\n\\n.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  padding: 0.5rem 0.75rem;\\n  width: 80%;\\n  border: 2px solid #afc7dd;\\n  box-shadow: 0 0 6px rgba(59, 83, 135, 0.5);\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  padding: 0.75rem 1.25rem;\\n  min-width: 120px;\\n  background-color: #4c4e4f;\\n  color: white;\\n  font-weight: 500;\\n  transition: background 0.3s, transform 0.2s;\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  background-color: #4c4e4f;\\n  transform: scale(1.05);\\n}\\n\\n.grid-toolbar[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  align-items: center;\\n  margin-bottom: 10px;\\n}\\n\\n\\n\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 5px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  border-radius: 0.375rem;\\n  transition: all 0.15s ease-in-out;\\n  min-width: 40px;\\n  height: 40px;\\n  border: 1px solid transparent;\\n  background-color: transparent;\\n  \\n\\n  \\n\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn.btn-icon[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  min-width: 40px;\\n  width: 40px;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn.btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .k-grid-toolbar[_ngcontent-%COMP%]   .btn.btn-icon[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem !important;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:not(.btn-icon) {\\n  padding: 0.375rem 0.75rem;\\n  gap: 0.5rem;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%] {\\n  background-color: #198754;\\n  border-color: #198754;\\n  color: white;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]:hover {\\n  background-color: #157347;\\n  border-color: #146c43;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-warning[_ngcontent-%COMP%] {\\n  background-color: #ffc107;\\n  border-color: #ffc107;\\n  color: #000;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-warning[_ngcontent-%COMP%]:hover {\\n  background-color: #ffca2c;\\n  border-color: #ffc720;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-info[_ngcontent-%COMP%] {\\n  background-color: #0dcaf0;\\n  border-color: #0dcaf0;\\n  color: #000;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-info[_ngcontent-%COMP%]:hover {\\n  background-color: #31d2f2;\\n  border-color: #25cff2;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: white;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5c636a;\\n  border-color: #565e64;\\n}\\n\\n\\n\\n.total-count[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 0.875rem;\\n}\\n.total-count[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  color: #6c757d !important;\\n}\\n.total-count[_ngcontent-%COMP%]   .fw-bold[_ngcontent-%COMP%] {\\n  font-weight: 600 !important;\\n  color: #495057;\\n}\\n\\n.k-grid[_ngcontent-%COMP%] {\\n  border-radius: 6px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\\n}\\n\\n.no-data[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: 40px 0;\\n  font-size: 16px;\\n  color: #888;\\n  background-color: #f9f9f9;\\n  border-radius: 6px;\\n  margin-top: 20px;\\n}\\n\\n.detail-container[_ngcontent-%COMP%] {\\n  padding: 15px;\\n  background-color: #f9f9f9;\\n  border-radius: 4px;\\n}\\n\\n.detail-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 8px;\\n}\\n.detail-row[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n  width: 120px;\\n  font-weight: 500;\\n  color: #666;\\n}\\n\\n\\n\\n.status-active[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  background-color: #e8f5e9;\\n  color: #2e7d32;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n\\n.status-inactive[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.advanced-filters-panel[_ngcontent-%COMP%] {\\n  border-top: 1px solid #dee2e6;\\n  margin-top: 10px;\\n}\\n\\n\\n\\n.custom-dropdown[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: white;\\n}\\n.custom-dropdown[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  background-color: #5c636a;\\n  border-color: #565e64;\\n}\\n\\n\\n\\n.badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n  margin: 0.125rem;\\n  border-radius: 0.25rem;\\n}\\n\\n.badge-light-primary[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .grid-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .k-grid-toolbar[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n    gap: 5px;\\n  }\\n  .k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    min-width: 35px;\\n    height: 35px;\\n    font-size: 0.75rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["FormControl", "_", "each", "map", "Subject", "debounceTime", "distinctUntilChanged", "AppSettings", "ConfirmationDialogComponent", "RoleEditComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtwoWayListener", "RoleListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "searchData", "ɵɵresetView", "ɵɵlistener", "RoleListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener", "onSearchKeyDown", "RoleListComponent_ng_template_4_Template_kendo_textbox_valueChange_1_listener", "onSearchChange", "ɵɵelement", "RoleListComponent_ng_template_4_Template_button_click_8_listener", "create", "RoleListComponent_ng_template_4_Template_button_click_10_listener", "toggleExpand", "RoleListComponent_ng_template_4_Template_button_click_12_listener", "resetTable", "RoleListComponent_ng_template_4_Template_button_click_14_listener", "refreshGrid", "ɵɵadvance", "ɵɵstyleProp", "ɵɵtwoWayProperty", "ɵɵproperty", "ɵɵtextInterpolate", "page", "totalElements", "ɵɵclassProp", "isExpanded", "RoleListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener", "_r4", "appliedFilters", "status", "RoleListComponent_ng_template_5_div_0_Template_button_click_7_listener", "applyAdvancedFilters", "RoleListComponent_ng_template_5_div_0_Template_button_click_10_listener", "clearAllFilters", "advancedFilterOptions", "ɵɵtemplate", "RoleListComponent_ng_template_5_div_0_Template", "showAdvancedFilters", "RoleListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template_a_click_0_listener", "dataItem_r6", "_r5", "$implicit", "edit", "roleId", "RoleListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template", "ɵɵstyleMap", "ɵɵpureFunction0", "_c6", "fixedColumns", "includes", "_c7", "getHiddenField", "ɵɵtextInterpolate1", "dataItem_r7", "<PERSON><PERSON><PERSON>", "column_r9", "filter_r8", "RoleListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template", "RoleListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template", "formatPermission", "dataItem_r10", "rolePermissions", "ɵɵsanitizeHtml", "column_r12", "filter_r11", "RoleListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template", "RoleListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template", "dataItem_r13", "RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_span_0_Template", "RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_span_1_Template", "column_r15", "filter_r14", "RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template", "RoleListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template", "ɵɵtextInterpolate2", "ɵɵpipeBind2", "dataItem_r16", "lastUpdatedDate", "lastUpdatedByFullName", "column_r18", "filter_r17", "RoleListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template", "RoleListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template", "ɵɵelementContainerStart", "RoleListComponent_ng_container_6_kendo_grid_column_1_Template", "RoleListComponent_ng_container_6_kendo_grid_column_2_Template", "RoleListComponent_ng_container_6_kendo_grid_column_3_Template", "RoleListComponent_ng_container_6_kendo_grid_column_4_Template", "RoleListComponent_ng_container_6_kendo_grid_column_5_Template", "column_r19", "RoleListComponent_ng_template_7_div_0_Template_button_click_5_listener", "_r20", "loadTable", "RoleListComponent_ng_template_7_div_0_Template", "loading", "isLoading", "RoleListComponent", "cdr", "modalService", "exceljsService", "httpUtilService", "AppService", "layoutUtilService", "UserService", "roleService", "kendoColumnService", "grid", "serverSideRowData", "gridData", "IsListHasValue", "loginUser", "searchTerms", "searchSubscription", "filter", "logic", "filters", "gridFilter", "activeFilters", "filterOptions", "text", "value", "size", "pageNumber", "totalPages", "orderBy", "orderDir", "skip", "sort", "field", "dir", "kendoHide", "hiddenData", "kendoColOrder", "kendoInitColOrder", "hiddenFields", "gridColumns", "defaultColumns", "draggableColumns", "normalGrid", "expandedGrid", "gridColumnConfig", "title", "width", "isFixed", "type", "order", "filterable", "columnData", "name", "prop", "routerSubscription", "GRID_STATE_KEY", "exportOptions", "selectedRoles", "isAllSelected", "roleStatistics", "activeRoles", "inactiveRoles", "totalRoles", "showBulkActions", "bulkActionStatus", "pageSize", "PAGE_SIZE", "pageSizeOptions", "PAGE_SIZE_OPTIONS", "itemsPerPage", "defaultOrder", "defaultOrderBy", "defaultRoles", "statusData", "permissionArray", "selectedTab", "innerWidth", "displayMobile", "constructor", "ngOnInit", "getLoggedInUser", "console", "log", "window", "pipe", "subscribe", "searchTerm", "loadRoles", "onPageLoad", "initializeColumnVisibilitySystem", "setTimeout", "loadColumnConfigFromDatabase", "localStorage", "removeItem", "col", "ngAfterViewInit", "getDefaultPermissions", "permissions", "responseData", "onTabActivated", "ngOnDestroy", "unsubscribe", "complete", "loadTableWithKendoEndpoint", "loadingSubject", "next", "state", "take", "search", "loggedInUserId", "userId", "getRolesForKendoGrid", "data", "<PERSON><PERSON><PERSON>", "errors", "length", "error", "handleEmptyResponse", "roleData", "total", "sortGridDataClientSide", "Math", "ceil", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pageChange", "event", "onSortChange", "isThirdClick", "undefined", "normalizedField", "filterChange", "Array", "isArray", "s", "parseUsDate", "val", "m", "match", "NaN", "month", "parseInt", "day", "year", "hour", "minute", "ampm", "toUpperCase", "Date", "getTime", "getVal", "item", "v", "ts", "parse", "isNaN", "us", "String", "toLowerCase", "sorted", "a", "b", "av", "bv", "onSelectionChange", "selectedRows", "key", "onSearchInput", "clearSearch", "toggleAdvancedFilters", "gridContainer", "document", "querySelector", "classList", "toggle", "refresh", "onExportClick", "exportall", "exportRowData", "showError", "saveHead", "nonHiddenColumns", "hiddenColumns", "columns", "for<PERSON>ach", "column", "hidden", "push", "draggableColumnsOrder", "index", "orderIndex", "userData", "pageName", "userID", "LoggedId", "createHideFields", "res", "saveToLocalStorage", "showSuccess", "message", "token", "getLocalStorageItem", "indexOf", "clearFromLocalStorage", "detectChanges", "reset", "loadSavedColumnOrder", "savedOrder", "parsedOrder", "savedDraggableColumns", "missingColumns", "columnName", "onColumnReorder", "newIndex", "oldIndex", "reorderedColumns", "movedColumn", "splice", "updateColumnVisibility", "existingIndex", "findIndex", "getHideFields", "Data", "hideData", "JSON", "some", "includeInChooser", "loadFromLocalStorageFallback", "savedConfig", "getFromLocalStorage", "UserId", "pemissions", "permArray", "permData", "r", "i", "rArray", "nIndex", "join", "filterConfiguration", "searchText", "paginate", "Category", "trim", "serverSideSetPage", "changeOrder", "Orderby", "OrderDir", "indexColumn", "cd", "pageLimit", "num", "Number", "id", "NgbModalOptions", "backdrop", "keyboard", "scrollable", "modalRef", "open", "componentInstance", "passEntry", "receivedEntry", "rowdata", "tableTitle", "headerArray", "respResult", "respData", "Name", "formatexcelPermission", "Permissions", "Status", "Description", "unixDate", "CreatedDate", "unixTime", "CreatedUserFullName", "LastUpdatedDate", "LastUpdatedUserFullName", "colSize", "generateExcel", "queryparams", "getAllRolesWithUserInfo", "deleteRoles", "role", "showClose", "description", "actionButtonText", "cancelButtonText", "success", "reStart", "deleteRole", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "NgbModal", "i2", "ExceljsService", "i3", "HttpUtilsService", "i4", "i5", "CustomLayoutUtilsService", "i6", "i7", "RoleService", "i8", "KendoColumnService", "selectors", "viewQuery", "RoleListComponent_Query", "rf", "ctx", "RoleListComponent_div_0_Template", "RoleListComponent_Template_kendo_grid_columnReorder_2_listener", "_r1", "RoleListComponent_Template_kendo_grid_selectionChange_2_listener", "RoleListComponent_Template_kendo_grid_filterChange_2_listener", "RoleListComponent_Template_kendo_grid_pageChange_2_listener", "RoleListComponent_Template_kendo_grid_sortChange_2_listener", "RoleListComponent_Template_kendo_grid_columnVisibilityChange_2_listener", "RoleListComponent_ng_template_4_Template", "RoleListComponent_ng_template_5_Template", "RoleListComponent_ng_container_6_Template", "RoleListComponent_ng_template_7_Template", "ɵɵpureFunction1", "_c2", "_c1", "_c3", "_c4", "_c5"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\setting\\role-list\\role-list.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\setting\\role-list\\role-list.component.html"], "sourcesContent": ["import { ChangeDetector<PERSON><PERSON>, Component, ElementRef, ViewChild, OnInit, OnDestroy, AfterViewInit } from '@angular/core';\r\nimport { FormControl } from '@angular/forms';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport _, { each } from 'lodash';\r\nimport { map, Subject, debounceTime, distinctUntilChanged, Subscription } from 'rxjs';\r\nimport { AppSettings } from 'src/app/app.settings';\r\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\r\nimport { HttpUtilsService } from '../../services/http-utils.service';\r\nimport { ConfirmationDialogComponent } from '../../shared/confirmation-dialog/confirmation-dialog.component';\r\nimport { Page } from '../../shared/data/pagination.module';\r\nimport { AppService } from '../../services/app.service';\r\nimport { UserService } from '../../services/user.service';\r\nimport { RoleService } from '../../services/role.service';\r\nimport { RoleEditComponent } from '../role-edit/role-edit.component';\r\nimport { ExceljsService } from '../../services/exceljs.service';\r\nimport { KendoColumnService } from '../../services/kendo-column.service';\r\nimport { SortDescriptor } from '@progress/kendo-data-query';\r\nimport { FilterDescriptor, CompositeFilterDescriptor } from '@progress/kendo-data-query';\r\n\r\n// Type definitions\r\ninterface RoleData {\r\n  roleId: number;\r\n  Name: string;\r\n  Permissions: string;\r\n  Status: string;\r\n  Description: string;\r\n  LastUpdatedDate: string;\r\n  LastUpdatedUserFullName: string;\r\n  CreatedDate: string;\r\n  CreatedUserFullName: string;\r\n  HasUser: number;\r\n}\r\n\r\n// Type for page configuration\r\ninterface PageConfig {\r\n  size: number;\r\n  pageNumber: number;\r\n  totalElements: number;\r\n  totalPages: number;\r\n  orderBy: string;\r\n  orderDir: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-role-list',\r\n  templateUrl: './role-list.component.html',\r\n  styleUrl: './role-list.component.scss'\r\n})\r\nexport class RoleListComponent implements OnInit, OnDestroy, AfterViewInit {\r\n  @ViewChild('normalGrid') grid: any;\r\n\r\n  // Data\r\n  public serverSideRowData: RoleData[] = [];\r\n  public gridData: RoleData[] = [];\r\n  public IsListHasValue: boolean = false;\r\n\r\n  public loading: boolean = false;\r\n  public isLoading: boolean = false;\r\n\r\n  loginUser: Record<string, any> = {};\r\n\r\n  // Search\r\n  public searchData: string = '';\r\n  private searchTerms = new Subject<string>();\r\n  private searchSubscription: Subscription;\r\n\r\n  // Enhanced Filters for Kendo UI\r\n  public filter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\r\n  public gridFilter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\r\n  public activeFilters: Array<{\r\n    field: string;\r\n    operator: string;\r\n    value: any;\r\n  }> = [];\r\n\r\n  public filterOptions: Array<{ text: string; value: string | null }> = [\r\n    { text: 'All', value: null },\r\n    { text: 'Active', value: 'Active' },\r\n    { text: 'Inactive', value: 'Inactive' }\r\n  ];\r\n\r\n  // Advanced filter options\r\n  public advancedFilterOptions = {\r\n    status: [\r\n      { text: 'All', value: null },\r\n      { text: 'Active', value: 'Active' },\r\n      { text: 'Inactive', value: 'Inactive' }\r\n    ] as Array<{ text: string; value: string | null }>\r\n  };\r\n\r\n  // Filter state\r\n  public showAdvancedFilters = false;\r\n  public appliedFilters: {\r\n    status: string | null;\r\n  } = { status: null };\r\n\r\n  // Kendo Grid properties\r\n  public page: PageConfig = {\r\n    size: 10,\r\n    pageNumber: 0,\r\n    totalElements: 0,\r\n    totalPages: 0,\r\n    orderBy: 'lastUpdatedDate',\r\n    orderDir: 'desc'\r\n  };\r\n  public skip: number = 0;\r\n  public sort: SortDescriptor[] = [\r\n    { field: 'lastUpdatedDate', dir: 'desc' }\r\n  ];\r\n\r\n  // Column visibility system properties\r\n  public kendoHide: any;\r\n  public hiddenData: any = [];\r\n  public kendoColOrder: any = [];\r\n  public kendoInitColOrder: any = [];\r\n  public hiddenFields: any = [];\r\n\r\n  // Column configuration for the new system\r\n  public gridColumns: string[] = [];\r\n  public defaultColumns: string[] = [];\r\n  public fixedColumns: string[] = [];\r\n  public draggableColumns: string[] = [];\r\n  public normalGrid: any;\r\n  public expandedGrid: any;\r\n  public isExpanded = false;\r\n\r\n  // Enhanced Columns with Kendo UI features\r\n  public gridColumnConfig: Array<{\r\n    field: string;\r\n    title: string;\r\n    width: number;\r\n    isFixed: boolean;\r\n    type: string;\r\n    filterable?: boolean;\r\n    order: number;\r\n  }> = [\r\n    { field: 'action', title: 'Action', width: 80, isFixed: true, type: 'action', order: 1 },\r\n    { field: 'roleName', title: 'Name', width: 200, isFixed: true, type: 'text', filterable: true, order: 2 },\r\n    { field: 'rolePermissions', title: 'Permissions', width: 300, isFixed: false, type: 'text', filterable: true, order: 3 },\r\n    { field: 'status', title: 'Status', width: 120, type: 'status', isFixed: false, filterable: true, order: 4 },\r\n    { field: 'lastUpdatedDate', title: 'Updated Date', width: 180, isFixed: false, type: 'date', filterable: true, order: 5 }\r\n  ];\r\n\r\n  // OLD SYSTEM - to be removed\r\n  public columnData = [\r\n    { name: 'Name', prop: 'roleName', order: 'desc', width: '17%', sort: true },\r\n    { name: 'Permissions', prop: 'rolePermissions', order: 'desc', width: '40%', sort: true },\r\n    { name: 'Status', prop: 'status', order: 'desc', width: '11%', sort: true },\r\n    { name: 'Updated', prop: 'lastUpdatedDate', order: 'desc', width: '18%', sort: true },\r\n    { name: 'Action', prop: 'Action', order: 'desc', width: '12%', sort: false }\r\n  ];\r\n\r\n  // Router subscription for saving state on navigation\r\n  private routerSubscription: Subscription;\r\n\r\n  // Storage key for state persistence\r\n  private readonly GRID_STATE_KEY = 'roles-grid-state';\r\n\r\n  // Export options\r\n  public exportOptions: Array<{ text: string; value: string }> = [\r\n    { text: 'Export All', value: 'all' },\r\n    { text: 'Export Selected', value: 'selected' },\r\n    { text: 'Export Filtered', value: 'filtered' }\r\n  ];\r\n\r\n  // Selection state\r\n  public selectedRoles: RoleData[] = [];\r\n  public isAllSelected: boolean = false;\r\n\r\n  // Statistics\r\n  public roleStatistics: {\r\n    activeRoles: number;\r\n    inactiveRoles: number;\r\n    totalRoles: number;\r\n  } = {\r\n    activeRoles: 0,\r\n    inactiveRoles: 0,\r\n    totalRoles: 0\r\n  };\r\n\r\n  // Bulk operations\r\n  public showBulkActions = false;\r\n  public bulkActionStatus: string = 'Active';\r\n\r\n  // Legacy properties (keeping for backward compatibility)\r\n  pageSize: number = AppSettings.PAGE_SIZE;\r\n  pageSizeOptions: any = AppSettings.PAGE_SIZE_OPTIONS;\r\n  itemsPerPage = new FormControl(this.pageSize);\r\n  defaultOrder = 'desc';\r\n  defaultOrderBy = 'lastUpdatedDate';\r\n  defaultRoles: any = [];\r\n  statusData: boolean = false;\r\n  permissionArray: any = [];\r\n  selectedTab = 'All';\r\n  innerWidth: any;\r\n  displayMobile: boolean = false;\r\n\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    private modalService: NgbModal,\r\n    public exceljsService: ExceljsService,\r\n    private httpUtilService: HttpUtilsService,\r\n    public AppService: AppService,\r\n    private layoutUtilService: CustomLayoutUtilsService,\r\n    private UserService: UserService,\r\n    private roleService: RoleService,\r\n    private kendoColumnService: KendoColumnService,\r\n  ) {\r\n    // set the default paging options\r\n    this.page.pageNumber = 0;\r\n    this.page.size = this.pageSize;\r\n    this.page.orderBy = 'LastUpdatedDate';\r\n    this.page.orderDir = 'desc';\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loginUser = this.AppService.getLoggedInUser();\r\n    console.log('Login user loaded:', this.loginUser);\r\n\r\n    this.innerWidth = window.innerWidth;\r\n    if((this.innerWidth >= 320) && (this.innerWidth <768)){\r\n     this.displayMobile = true\r\n    }else{\r\n     this.displayMobile = false\r\n    }\r\n\r\n    // Setup search with debounce\r\n    this.searchSubscription = this.searchTerms.pipe(\r\n      debounceTime(500),\r\n      distinctUntilChanged()\r\n    ).subscribe((searchTerm) => {\r\n      console.log('Search triggered with term:', searchTerm);\r\n      this.page.pageNumber = 0;\r\n      this.skip = 0;\r\n      // Set loading state for search\r\n      this.loading = true;\r\n      this.isLoading = true;\r\n      this.loadTable();\r\n    });\r\n\r\n    // Load roles for advanced filters\r\n    this.loadRoles();\r\n\r\n    // Initialize with default page load\r\n    this.onPageLoad();\r\n\r\n    // Initialize new column visibility system\r\n    this.initializeColumnVisibilitySystem();\r\n\r\n    // Load column configuration after a short delay to ensure loginUser is available\r\n    setTimeout(() => {\r\n      this.loadColumnConfigFromDatabase();\r\n    }, 100);\r\n\r\n    localStorage.removeItem('keyword');\r\n  }\r\n\r\n  /**\r\n   * Initialize the new column visibility system\r\n   */\r\n  private initializeColumnVisibilitySystem(): void {\r\n    // Initialize default columns\r\n    this.defaultColumns = this.gridColumnConfig.map(col => col.field);\r\n    this.gridColumns = [...this.defaultColumns];\r\n\r\n    // Set fixed columns (first 2 columns)\r\n    this.fixedColumns = ['action', 'roleName'];\r\n\r\n    // Set draggable columns (all except fixed)\r\n    this.draggableColumns = this.defaultColumns.filter(col => !this.fixedColumns.includes(col));\r\n\r\n    // Initialize normal and expanded grid references\r\n    this.normalGrid = this.grid;\r\n    this.expandedGrid = this.grid;\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    // Load the table after the view is initialized\r\n    // Small delay to ensure the grid is properly rendered\r\n    setTimeout(() => {\r\n      this.loadTable();\r\n    }, 200);\r\n  }\r\n\r\n  // Method to handle initial page load\r\n  onPageLoad(): void {\r\n    // Initialize the component with default data\r\n    this.page.pageNumber = 0;\r\n    this.skip = 0;\r\n    this.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\r\n    this.filter = { logic: 'and', filters: [] };\r\n    this.searchData = '';\r\n\r\n    // Load the data\r\n    this.loadTable();\r\n  }\r\n\r\n  // Load roles for advanced filters\r\n  private loadRoles(): void {\r\n    this.UserService.getDefaultPermissions({}).subscribe((permissions: any) => {\r\n      this.permissionArray = permissions.responseData;\r\n    });\r\n  }\r\n\r\n  // Method to handle when the component becomes visible\r\n  onTabActivated(): void {\r\n    // Refresh the data when the tab is activated\r\n    this.loadTable();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Clean up subscriptions\r\n    if (this.routerSubscription) {\r\n      this.routerSubscription.unsubscribe();\r\n    }\r\n    if (this.searchSubscription) {\r\n      this.searchSubscription.unsubscribe();\r\n    }\r\n    this.searchTerms.complete();\r\n  }\r\n  // function to get roles data from API\r\n  loadTable(){\r\n    // Use the new Kendo UI specific endpoint\r\n    this.loadTableWithKendoEndpoint();\r\n  }\r\n\r\n  // New method to load data using Kendo UI specific endpoint\r\n  loadTableWithKendoEndpoint() {\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n\r\n    // Enable loader\r\n    this.httpUtilService.loadingSubject.next(true);\r\n\r\n    // Prepare state object for Kendo UI endpoint\r\n    console.log('=== BACKEND REQUEST DEBUG ===');\r\n    console.log('this.sort:', this.sort);\r\n    console.log('page.orderBy:', this.page.orderBy);\r\n    console.log('page.orderDir:', this.page.orderDir);\r\n\r\n    const state = {\r\n      take: this.page.size,\r\n      skip: this.skip,\r\n      sort: this.sort, // Send exactly what Kendo has\r\n      filter: this.filter,\r\n      search: this.searchData,\r\n      loggedInUserId: this.loginUser.userId,\r\n      orderBy: this.page.orderBy,\r\n      orderDir: this.page.orderDir\r\n    };\r\n\r\n    console.log('Loading roles table with search term:', this.searchData);\r\n    console.log('Full state object:', state);\r\n    console.log('=== END BACKEND REQUEST DEBUG ===');\r\n\r\n    this.roleService.getRolesForKendoGrid(state).subscribe({\r\n      next: (data: {\r\n        isFault?: boolean;\r\n        responseData?: {\r\n          data: any[];\r\n          total: number;\r\n          errors?: string[];\r\n        };\r\n        data?: any[];\r\n        total?: number;\r\n        errors?: string[];\r\n      }) => {\r\n        // Handle the new API response structure\r\n        if (data.isFault || (data.responseData && data.responseData.errors && data.responseData.errors.length > 0)) {\r\n          const errors = data.responseData?.errors || data.errors || [];\r\n          console.error('Kendo UI Grid errors:', errors);\r\n          this.handleEmptyResponse();\r\n        } else {\r\n          // Handle both old and new response structures\r\n          const responseData = data.responseData || data;\r\n          const roleData = responseData.data || [];\r\n          const total = responseData.total || 0;\r\n\r\n          this.IsListHasValue = roleData.length !== 0;\r\n          this.serverSideRowData = this.sortGridDataClientSide(roleData, this.sort);\r\n          this.gridData = this.serverSideRowData;\r\n          this.page.totalElements = total;\r\n          this.page.totalPages = Math.ceil(total / this.page.size);\r\n        }\r\n        this.loading = false;\r\n        this.isLoading = false;\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (error: unknown) => {\r\n        console.error('Error loading data with Kendo UI endpoint:', error);\r\n        this.handleEmptyResponse();\r\n        this.loading = false;\r\n        this.isLoading = false;\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  // Handle empty response\r\n  private handleEmptyResponse(): void {\r\n    this.IsListHasValue = false;\r\n    this.serverSideRowData = [];\r\n    this.gridData = [];\r\n    this.page.totalElements = 0;\r\n    this.page.totalPages = 0;\r\n    this.loading = false;\r\n    this.isLoading = false;\r\n  }\r\n\r\n  // Kendo Grid event handlers\r\n  public pageChange(event: { skip: number; take: number }): void {\r\n    this.skip = event.skip;\r\n    this.page.size = event.take;\r\n    this.page.pageNumber = event.skip / event.take;\r\n    // Set loading state for pagination\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.loadTableWithKendoEndpoint();\r\n  }\r\n\r\n  public onSortChange(sort: SortDescriptor[]): void {\r\n    // Check if this is the 3rd click (dir is undefined)\r\n    const isThirdClick = sort.length > 0 && sort[0] && sort[0].dir === undefined;\r\n\r\n    if (isThirdClick) {\r\n      // 3rd click - clear sort and use default\r\n      this.sort = [];\r\n      this.page.orderBy = 'lastUpdatedDate';\r\n      this.page.orderDir = 'desc';\r\n    } else if (sort.length > 0 && sort[0] && sort[0].dir) {\r\n      // Valid sort with direction\r\n      this.sort = sort;\r\n      const field = sort[0].field;\r\n      const dir = sort[0].dir;\r\n\r\n      // Normalize field names to match backend mapping\r\n      let normalizedField = field;\r\n      switch (field) {\r\n        case 'Name': normalizedField = 'roleName'; break;\r\n        case 'Status': normalizedField = 'status'; break;\r\n        case 'UpdatedDate':\r\n        case 'LastUpdatedDate':\r\n        case 'lastUpdatedByFullName':\r\n          normalizedField = 'lastUpdatedDate'; break;\r\n      }\r\n\r\n      this.page.orderBy = normalizedField;\r\n      this.page.orderDir = dir;\r\n    } else {\r\n      // Empty sort array or invalid sort\r\n      this.sort = [];\r\n      this.page.orderBy = 'lastUpdatedDate';\r\n      this.page.orderDir = 'desc';\r\n    }\r\n\r\n    // Reset to first page\r\n    this.page.pageNumber = 0;\r\n    this.skip = 0;\r\n    // Set loading state for sorting\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    console.log('=== END SORT CHANGE DEBUG ===');\r\n    this.loadTableWithKendoEndpoint();\r\n  }\r\n\r\n  public filterChange(event: any): void {\r\n    this.filter = event.filter;\r\n    this.page.pageNumber = 0;\r\n    this.skip = 0;\r\n    // Set loading state for filtering\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.loadTableWithKendoEndpoint();\r\n  }\r\n\r\n  // Client-side fallback sorting to guarantee visible order\r\n  private sortGridDataClientSide(data: any[], sort: SortDescriptor[] | undefined): any[] {\r\n    if (!Array.isArray(data) || !sort || sort.length === 0) return data;\r\n    const s = sort[0];\r\n    const field = (s.field as string) || 'lastUpdatedDate';\r\n    const dir = (s.dir || 'asc') === 'asc' ? 1 : -1;\r\n\r\n    const parseUsDate = (val: string) => {\r\n      // Supports formats like 8/21/25 1:58 PM\r\n      const m = val && val.match(/^(\\d{1,2})\\/(\\d{1,2})\\/(\\d{2,4})\\s+(\\d{1,2}):(\\d{2})\\s*(AM|PM)$/i);\r\n      if (!m) return NaN;\r\n      let month = parseInt(m[1], 10) - 1;\r\n      const day = parseInt(m[2], 10);\r\n      let year = parseInt(m[3], 10);\r\n      if (year < 100) year += 2000;\r\n      let hour = parseInt(m[4], 10);\r\n      const minute = parseInt(m[5], 10);\r\n      const ampm = m[6].toUpperCase();\r\n      if (ampm === 'PM' && hour < 12) hour += 12;\r\n      if (ampm === 'AM' && hour === 12) hour = 0;\r\n      return new Date(year, month, day, hour, minute).getTime();\r\n    };\r\n\r\n    const getVal = (item: any) => {\r\n      const v = item[field];\r\n      if (field === 'lastUpdatedDate') {\r\n        if (v instanceof Date) return v.getTime();\r\n        const ts = Date.parse(v);\r\n        if (!isNaN(ts)) return ts;\r\n        const us = parseUsDate(String(v));\r\n        return isNaN(us) ? 0 : us;\r\n      }\r\n      if (typeof v === 'string') return v.toLowerCase();\r\n      return v;\r\n    };\r\n\r\n    try {\r\n      const sorted = [...data].sort((a, b) => {\r\n        const av = getVal(a);\r\n        const bv = getVal(b);\r\n        if (av == null && bv == null) return 0;\r\n        if (av == null) return -1 * dir;\r\n        if (bv == null) return 1 * dir;\r\n        if (av > bv) return 1 * dir;\r\n        if (av < bv) return -1 * dir;\r\n        return 0;\r\n      });\r\n      return sorted;\r\n    } catch {\r\n      return data;\r\n    }\r\n  }\r\n\r\n  public onSelectionChange(event: any): void {\r\n    this.selectedRoles = event.selectedRows || [];\r\n    this.isAllSelected = this.selectedRoles.length === this.serverSideRowData.length;\r\n  }\r\n\r\n  // Search methods\r\n  public onSearchKeyDown(event: any): void {\r\n    if (event.key === 'Enter') {\r\n      this.searchTerms.next(this.searchData);\r\n    }\r\n  }\r\n\r\n  // Handle search input changes\r\n  onSearchInput(): void {\r\n    // Trigger search on every input change with debouncing\r\n    console.log('Search input changed:', this.searchData);\r\n    this.searchTerms.next(this.searchData);\r\n  }\r\n\r\n  // Handle search model changes\r\n  onSearchChange(): void {\r\n    // Trigger search when model changes\r\n    console.log('Search model changed:', this.searchData);\r\n    this.searchTerms.next(this.searchData);\r\n  }\r\n\r\n  public clearSearch(): void {\r\n    // Clear search data and trigger search\r\n    this.searchData = '';\r\n    // Set loading state for clear search\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.searchTerms.next('');\r\n  }\r\n\r\n  // Advanced filter methods\r\n  public toggleAdvancedFilters(): void {\r\n    this.showAdvancedFilters = !this.showAdvancedFilters;\r\n  }\r\n\r\n  public applyAdvancedFilters(): void {\r\n    this.page.pageNumber = 0;\r\n    this.skip = 0;\r\n    this.loadTableWithKendoEndpoint();\r\n  }\r\n\r\n  public clearAllFilters(): void {\r\n    this.appliedFilters = { status: null };\r\n    this.activeFilters = [];\r\n    this.filter = { logic: 'and', filters: [] };\r\n    this.page.pageNumber = 0;\r\n    this.skip = 0;\r\n    this.loadTableWithKendoEndpoint();\r\n  }\r\n\r\n  // Grid expansion methods\r\n  public toggleExpand(): void {\r\n    // Find grid container element and toggle fullscreen class\r\n    const gridContainer = document.querySelector('.grid-container') as HTMLElement;\r\n    if (gridContainer) {\r\n      gridContainer.classList.toggle('fullscreen-grid');\r\n      this.isExpanded = !this.isExpanded;\r\n      // Refresh grid after resize to ensure proper rendering\r\n      if (this.grid) {\r\n        this.grid.refresh();\r\n      }\r\n    }\r\n  }\r\n\r\n  // Export methods\r\n  public onExportClick(event: { item: { value: string } }): void {\r\n    switch (event.item.value) {\r\n      case 'all':\r\n        this.exportall();\r\n        break;\r\n      case 'selected':\r\n        if (this.selectedRoles.length > 0) {\r\n          this.exportRowData(this.selectedRoles);\r\n        } else {\r\n          this.layoutUtilService.showError('Please select roles to export', '');\r\n        }\r\n        break;\r\n      case 'filtered':\r\n        this.exportRowData(this.serverSideRowData);\r\n        break;\r\n      default:\r\n        this.exportall();\r\n    }\r\n  }\r\n\r\n  // Column visibility methods\r\n  /**\r\n   * Saves the current state of column visibility and order in the grid.\r\n   */\r\n  saveHead(): void {\r\n    // Check if loginUser is available\r\n    if (!this.loginUser || !this.loginUser.userId) {\r\n      console.error('loginUser not available:', this.loginUser);\r\n      this.layoutUtilService.showError('User not logged in. Please refresh the page.', '');\r\n      return;\r\n    }\r\n\r\n    const nonHiddenColumns: any[] = [];\r\n    const hiddenColumns: any[] = [];\r\n\r\n    if (this.grid && this.grid.columns) {\r\n      this.grid.columns.forEach((column: any) => {\r\n        if (!column.hidden) {\r\n          const columnData = {\r\n            title: column.title,\r\n            field: column.field,\r\n            hidden: column.hidden\r\n          };\r\n          nonHiddenColumns.push(columnData);\r\n        } else {\r\n          const columnData = {\r\n            title: column.title,\r\n            field: column.field,\r\n            hidden: column.hidden\r\n          };\r\n          hiddenColumns.push(columnData);\r\n        }\r\n      });\r\n    }\r\n\r\n    const draggableColumnsOrder = this.gridColumns\r\n      .filter(col => !this.fixedColumns.includes(col))\r\n      .map((field, index) => ({\r\n        field,\r\n        orderIndex: index\r\n      }));\r\n\r\n    // Prepare data for backend\r\n    const userData = {\r\n      pageName: 'Roles',\r\n      userID: this.loginUser.userId,\r\n      hiddenData: hiddenColumns,\r\n      kendoColOrder: draggableColumnsOrder,\r\n      LoggedId: this.loginUser.userId\r\n    };\r\n\r\n    // Show loading state\r\n    this.httpUtilService.loadingSubject.next(true);\r\n\r\n    // Save to backend\r\n    this.kendoColumnService.createHideFields(userData).subscribe({\r\n      next: (res) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        if (!res.isFault) {\r\n          // Update local state\r\n          this.hiddenData = hiddenColumns;\r\n          this.kendoColOrder = draggableColumnsOrder;\r\n          this.hiddenFields = this.hiddenData.map((col: any) => col.field);\r\n\r\n          // Also save to localStorage as backup\r\n          this.kendoColumnService.saveToLocalStorage(userData);\r\n\r\n          this.layoutUtilService.showSuccess(res.message || 'Column settings saved successfully.', '');\r\n        } else {\r\n          this.layoutUtilService.showError(res.message || 'Failed to save column settings.', '');\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (error) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        console.error('Error saving column settings:', error);\r\n\r\n        // Fallback to localStorage on error\r\n        this.kendoColumnService.saveToLocalStorage(userData);\r\n\r\n        // Update local state\r\n        this.hiddenData = hiddenColumns;\r\n        this.kendoColOrder = draggableColumnsOrder;\r\n        this.hiddenFields = this.hiddenData.map((col: any) => col.field);\r\n\r\n        this.layoutUtilService.showError('Failed to save to server. Settings saved locally.', '');\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Reset the current state of column visibility and order in the grid to its original state.\r\n   */\r\n  resetTable(): void {\r\n    // Check if loginUser is available\r\n    if (!this.loginUser || !this.loginUser.userId) {\r\n      console.error('loginUser not available:', this.loginUser);\r\n      this.layoutUtilService.showError('User not logged in. Please refresh the page and try again.', '');\r\n      return;\r\n    }\r\n\r\n    // Double-check authentication token\r\n    const token = this.AppService.getLocalStorageItem('permitToken', true);\r\n    if (!token) {\r\n      console.error('Authentication token not found');\r\n      this.layoutUtilService.showError('Authentication token not found. Please login again.', '');\r\n      return;\r\n    }\r\n\r\n    // Reset all state variables\r\n    this.searchData = '';\r\n    this.activeFilters = [];\r\n    this.filter = { logic: 'and', filters: [] };\r\n    this.skip = 0;\r\n    this.page.pageNumber = 0;\r\n    this.gridColumns = [...this.defaultColumns];\r\n\r\n    // Reset sort state to default\r\n    this.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\r\n    this.page.orderBy = 'lastUpdatedDate';\r\n    this.page.orderDir = 'desc';\r\n\r\n    // Reset advanced filters\r\n    this.appliedFilters = { status: null };\r\n\r\n    // Reset advanced filters visibility\r\n    this.showAdvancedFilters = false;\r\n\r\n    // Reset column order index\r\n    if (this.grid && this.grid.columns) {\r\n      this.grid.columns.forEach((column: any) => {\r\n        const index = this.gridColumns.indexOf(column.field);\r\n        if (index !== -1) {\r\n          column.orderIndex = index;\r\n        }\r\n        // Reset column visibility - show all columns\r\n        if (column.field && column.field !== 'action') {\r\n          column.hidden = false;\r\n        }\r\n      });\r\n    }\r\n\r\n    // Clear hidden columns\r\n    this.hiddenData = [];\r\n    this.kendoColOrder = [];\r\n    this.hiddenFields = [];\r\n\r\n    // Reset the Kendo Grid's internal state\r\n    if (this.grid) {\r\n      // Clear all filters\r\n      this.grid.filter = { logic: 'and', filters: [] };\r\n      \r\n      // Reset sorting\r\n      this.grid.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\r\n      \r\n      // Reset to first page\r\n      this.grid.skip = 0;\r\n      this.grid.pageSize = this.page.size;\r\n    }\r\n\r\n    // Prepare reset data\r\n    const userData = {\r\n      pageName: 'Roles',\r\n      userID: this.loginUser.userId,\r\n      hiddenData: [],\r\n      kendoColOrder: [],\r\n      LoggedId: this.loginUser.userId\r\n    };\r\n\r\n    // Only clear local settings; do not call server\r\n    this.kendoColumnService.clearFromLocalStorage('Roles');\r\n\r\n    // Show loader and refresh grid\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.cdr.detectChanges();\r\n\r\n    // Force grid refresh to apply all changes\r\n    if (this.grid) {\r\n      setTimeout(() => {\r\n        this.grid.refresh();\r\n        this.grid.reset();\r\n      }, 100);\r\n    }\r\n\r\n    this.loadTable();\r\n  }\r\n\r\n  /**\r\n   * Refresh grid data\r\n   */\r\n  refreshGrid(): void {\r\n    // Set loading state to show full-screen loader\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n\r\n    // Reset to first page and clear any applied filters\r\n    this.page.pageNumber = 0;\r\n    this.skip = 0;\r\n    this.filter = { logic: 'and', filters: [] };\r\n    this.gridFilter = { logic: 'and', filters: [] };\r\n    this.activeFilters = [];\r\n    this.appliedFilters = { status: null };\r\n\r\n    // Clear search data\r\n    this.searchData = '';\r\n\r\n    // Load fresh data from API\r\n    this.loadTable();\r\n  }\r\n\r\n  /**\r\n   * Loads and applies the saved column order from the user preferences or configuration.\r\n   */\r\n  loadSavedColumnOrder(kendoColOrder: any): void {\r\n    try {\r\n      const savedOrder = kendoColOrder;\r\n      if (savedOrder) {\r\n        const parsedOrder = savedOrder;\r\n        if (Array.isArray(parsedOrder) && parsedOrder.length > 0) {\r\n          // Get only the draggable columns from saved order\r\n          const savedDraggableColumns = parsedOrder\r\n            .sort((a, b) => a.orderIndex - b.orderIndex)\r\n            .map(col => col.field)\r\n            .filter(field => !this.fixedColumns.includes(field));\r\n\r\n          // Add any missing draggable columns at the end\r\n          const missingColumns = this.draggableColumns.filter(col => !savedDraggableColumns.includes(col));\r\n\r\n          // Combine fixed columns with saved draggable columns\r\n          this.gridColumns = [\r\n            ...this.fixedColumns,\r\n            ...savedDraggableColumns,\r\n            ...missingColumns\r\n          ];\r\n        } else {\r\n          this.gridColumns = [...this.defaultColumns];\r\n        }\r\n      } else {\r\n        this.gridColumns = [...this.defaultColumns];\r\n      }\r\n    } catch (error) {\r\n      this.gridColumns = [...this.defaultColumns];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Checks if a given column is marked as hidden.\r\n   */\r\n  getHiddenField(columnName: any): boolean {\r\n    return this.hiddenFields.indexOf(columnName) > -1;\r\n  }\r\n\r\n  /**\r\n   * Handles the column reordering event triggered when a column is moved by the user.\r\n   */\r\n  onColumnReorder(event: any): void {\r\n    const { columns, newIndex, oldIndex } = event;\r\n\r\n    // Prevent reordering of fixed columns\r\n    if (this.fixedColumns.includes(columns[oldIndex].field) || this.fixedColumns.includes(columns[newIndex].field)) {\r\n      return;\r\n    }\r\n\r\n    // Update the gridColumns array\r\n    const reorderedColumns = [...this.gridColumns];\r\n    const [movedColumn] = reorderedColumns.splice(oldIndex, 1);\r\n    reorderedColumns.splice(newIndex, 0, movedColumn);\r\n\r\n    this.gridColumns = reorderedColumns;\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  /**\r\n   * Handles column visibility changes from the Kendo Grid.\r\n   */\r\n  updateColumnVisibility(event: any): void {\r\n    const { column, hidden } = event;\r\n\r\n    // Update hiddenData array\r\n    const existingIndex = this.hiddenData.findIndex((item: any) => item.field === column.field);\r\n\r\n    if (hidden && existingIndex === -1) {\r\n      // Add to hidden columns\r\n      this.hiddenData.push({\r\n        title: column.title,\r\n        field: column.field,\r\n        hidden: true\r\n      });\r\n    } else if (!hidden && existingIndex !== -1) {\r\n      // Remove from hidden columns\r\n      this.hiddenData.splice(existingIndex, 1);\r\n    }\r\n\r\n    // Update hiddenFields array\r\n    this.hiddenFields = this.hiddenData.map((col: any) => col.field);\r\n\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  /**\r\n   * Loads the saved column configuration from the backend or localStorage as fallback.\r\n   */\r\n  private loadColumnConfigFromDatabase(): void {\r\n    try {\r\n      // First try to load from backend\r\n      if (this.loginUser && this.loginUser.userId) {\r\n        this.kendoColumnService.getHideFields({\r\n          pageName: 'Roles',\r\n          userID: this.loginUser.userId\r\n        }).subscribe({\r\n          next: (res) => {\r\n            if (!res.isFault && res.Data) {\r\n              this.kendoHide = res.Data;\r\n              this.hiddenData = res.Data.hideData ? JSON.parse(res.Data.hideData) : [];\r\n              this.kendoInitColOrder = res.Data.kendoColOrder ? JSON.parse(res.Data.kendoColOrder) : [];\r\n              this.hiddenFields = this.hiddenData.map((col: any) => col.field);\r\n\r\n              // Update grid columns based on the hidden fields\r\n              if (this.grid && this.grid.columns) {\r\n                this.grid.columns.forEach((column: any) => {\r\n                  if (this.hiddenData.some((item: any) => item.title === column.title && item.hidden)) {\r\n                    column.includeInChooser = true;\r\n                    column.hidden = true;\r\n                  } else {\r\n                    column.hidden = false;\r\n                  }\r\n                });\r\n              }\r\n\r\n              // Load saved column order and update grid\r\n              this.loadSavedColumnOrder(this.kendoInitColOrder);\r\n\r\n              // Also save to localStorage as backup\r\n              this.kendoColumnService.saveToLocalStorage({\r\n                pageName: 'Roles',\r\n                userID: this.loginUser.userId,\r\n                hiddenData: this.hiddenData,\r\n                kendoColOrder: this.kendoInitColOrder\r\n              });\r\n            }\r\n          },\r\n          error: (error) => {\r\n            console.error('Error loading from backend, falling back to localStorage:', error);\r\n            this.loadFromLocalStorageFallback();\r\n          }\r\n        });\r\n      } else {\r\n        // Fallback to localStorage if no user ID\r\n        this.loadFromLocalStorageFallback();\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading column configuration:', error);\r\n      this.loadFromLocalStorageFallback();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Fallback method to load column configuration from localStorage\r\n   */\r\n  private loadFromLocalStorageFallback(): void {\r\n    try {\r\n      const savedConfig = this.kendoColumnService.getFromLocalStorage('Roles', this.loginUser?.UserId || 0);\r\n      if (savedConfig) {\r\n        this.kendoHide = savedConfig;\r\n        this.hiddenData = savedConfig.hiddenData || [];\r\n        this.kendoInitColOrder = savedConfig.kendoColOrder || [];\r\n        this.hiddenFields = this.hiddenData.map((col: any) => col.field);\r\n\r\n        // Update grid columns based on the hidden fields\r\n        if (this.grid && this.grid.columns) {\r\n          this.grid.columns.forEach((column: any) => {\r\n            if (this.hiddenData.some((item: any) => item.title === column.title && item.hidden)) {\r\n              column.includeInChooser = true;\r\n              column.hidden = true;\r\n            } else {\r\n              column.hidden = false;\r\n            }\r\n          });\r\n        }\r\n\r\n        // Load saved column order and update grid\r\n        this.loadSavedColumnOrder(this.kendoInitColOrder);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading from localStorage fallback:', error);\r\n    }\r\n  }\r\n  //function to form permission from API\r\n  formatPermission(pemissions:any){\r\n    let permArray:any = JSON.parse(pemissions);\r\n    let permData= '';\r\n\r\n    each(permArray, (r:any, i:any)=>{\r\n      _.forEach(r, function (value, key) {\r\n      let rArray = [];\r\n      value.indexOf('Read') === -1?'':rArray.push('Read');\r\n      value.indexOf('Write') === -1?'':rArray.push('Write');\r\n      value.indexOf('Delete') === -1?'':rArray.push('Delete');\r\n        let nIndex =value.length>0?rArray.join(', '):'None';\r\n        permData +=\"<span class='badge badge-light-primary me-2 mt-1'>\"+key+\" : \"+ nIndex+\"</span>\"\r\n      });\r\n\r\n    });\r\n    return permData;\r\n  }\r\n\r\n  // function to search the data when there is a typing in the search box\r\n  search(event: any) {\r\n    this.page.pageNumber = 0;\r\n    this.loadTable();\r\n  }\r\n   //function to filter data from search\r\n  filterConfiguration(): any {\r\n    let filter: any = {};\r\n    let searchText: any\r\n    if(this.searchData === null){\r\n      searchText= ' ';\r\n    }else{\r\n      searchText= this.searchData;\r\n    }\r\n    filter.paginate = true;\r\n    filter.Category = this.selectedTab;\r\n    filter.search = searchText.trim();\r\n    return filter;\r\n  }\r\n\r\n  // function to get the roles data based on the page selection in Pagination\r\n  serverSideSetPage(event: any) {\r\n    this.page.pageNumber = event - 1;\r\n    this.loadTable();\r\n  }\r\n\r\n  // function to get the roles data based on the sort selection\r\n  // Params : Orderby - field to be sorted, OrderDir - asc/desc\r\n  changeOrder(Orderby: string, OrderDir: String) {\r\n    this.defaultOrder = OrderDir === 'desc' ? 'asc' : 'desc';\r\n    let indexColumn = this.columnData.findIndex(cd => cd.prop === Orderby);\r\n    this.columnData[indexColumn].order = this.defaultOrder;\r\n    this.cdr.markForCheck()\r\n    this.defaultOrderBy = Orderby;\r\n    this.page.orderDir = this.defaultOrder;\r\n    this.page.orderBy = Orderby;\r\n    this.loadTable();\r\n  }\r\n\r\n  // function to get the roles based on the items per page selection\r\n  pageLimit(num:any) {\r\n    this.pageSize = Number(num);\r\n    this.page.pageNumber = 0;\r\n    if (this.serverSideRowData) this.page.size = Number(num);\r\n    this.loadTable();\r\n  }\r\n\r\n\r\n // function to create a new roles\r\n  create() {\r\n    this.edit(0);\r\n  }\r\n  // function to edit a particular role\r\n  edit(id: number) {\r\n    var NgbModalOptions: any = {\r\n      size: 'lg', backdrop: 'static',\r\n      keyboard: false, scrollable: true\r\n    }\r\n    const modalRef = this.modalService.open(RoleEditComponent, NgbModalOptions);\r\n    modalRef.componentInstance.id = id;\r\n    modalRef.componentInstance.permissions = this.permissionArray;\r\n    //get response from edit user modal\r\n    modalRef.componentInstance.passEntry.subscribe((receivedEntry: any) => {\r\n      if (receivedEntry == true) {\r\n        this.loadTable();\r\n      }\r\n    })\r\n  }\r\n   //function to export particular page data\r\n  exportRowData(rowdata: any) {\r\n    if (rowdata !== undefined) {\r\n      if (rowdata.length > 0) {\r\n        // declare the title and header data for excel\r\n        const tableTitle = 'Roles';\r\n        const headerArray = [\r\n          'Name',\r\n          'Permissions',\r\n          'Status',\r\n          'Description',\r\n          'Created Date',\r\n           'Created Time',\r\n           'Created By User',\r\n           'Modified Date',\r\n           'Modified Time',\r\n           'Modified By User'\r\n        ];\r\n\r\n        // get the data for excel in a array format\r\n        const respResult: any = [];\r\n        each(rowdata, (rowdata: any) => {\r\n          const respData = [];\r\n            respData.push( rowdata.Name);\r\n            respData.push(this.formatexcelPermission(rowdata.Permissions));\r\n            respData.push( rowdata.Status);\r\n            respData.push( rowdata.Description);\r\n            respData.push(this.AppService.unixDate(rowdata.CreatedDate));\r\n            respData.push(this.AppService.unixTime(rowdata.CreatedDate));\r\n            respData.push( rowdata.CreatedUserFullName);\r\n            respData.push(this.AppService.unixDate(rowdata.LastUpdatedDate));\r\n            respData.push(this.AppService.unixTime(rowdata.LastUpdatedDate));\r\n            respData.push( rowdata.LastUpdatedUserFullName);\r\n            respResult.push(respData);\r\n        }\r\n        );\r\n        // assign the width for each column\r\n        const colSize = [\r\n          {\r\n            id: 1,\r\n            width: 30\r\n          }, {\r\n            id: 2,\r\n            width: 50\r\n          }, {\r\n            id: 3,\r\n            width: 40\r\n          }, {\r\n            id: 4,\r\n            width: 30\r\n          }, {\r\n            id: 5,\r\n            width: 30\r\n          }, {\r\n            id: 6,\r\n            width: 30\r\n          }, {\r\n            id: 7,\r\n            width: 30\r\n          }, {\r\n            id: 8,\r\n            width: 30\r\n          }, {\r\n            id: 9,\r\n            width: 30\r\n          }, {\r\n            id: 10,\r\n            width: 30\r\n          },];\r\n        this.cdr.markForCheck();\r\n        this.exceljsService.generateExcel(tableTitle, headerArray, respResult, colSize);\r\n      } else {\r\n        this.layoutUtilService.showError('There is no available data to export', '')\r\n      }\r\n    } else {\r\n      this.layoutUtilService.showError('There is no available data to export', '')\r\n    }\r\n  }\r\n  // function to get all the users data from api call and then export those in a excel file\r\n  exportall() {\r\n    const queryparams = {\r\n      paginate: false\r\n    };\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.UserService.getAllRolesWithUserInfo(queryparams).pipe(\r\n      map((data: any) => data as any)).subscribe((data:any) => {\r\n        if (!data.isFault) {\r\n          this.exportRowData(data.responseData);\r\n          this.cdr.markForCheck();\r\n          this.httpUtilService.loadingSubject.next(false);\r\n        } else {\r\n          this.httpUtilService.loadingSubject.next(false);\r\n        }\r\n      });\r\n\r\n  }\r\n  // function to display the popup to get confirmation to delete roles\r\n  deleteRoles(role:any){\r\n    var NgbModalOptions: any = {\r\n      size: 'md', backdrop: 'static',\r\n      keyboard: false, scrollable: true\r\n    }\r\n    const modalRef = this.modalService.open(ConfirmationDialogComponent, NgbModalOptions);\r\n    modalRef.componentInstance.id = role.roleId;\r\n    modalRef.componentInstance.showClose = true;\r\n    modalRef.componentInstance.description = \"Are you sure to delete this role?\"\r\n    modalRef.componentInstance.actionButtonText = \"Yes\"\r\n    modalRef.componentInstance.cancelButtonText = \"Cancel\"\r\n    modalRef.componentInstance.title = \"Delete Role - \"+ role.roleName\r\n\r\n    modalRef.componentInstance.passEntry.subscribe((receivedEntry: any) => {\r\n      console.log('receivedEntry ',receivedEntry)\r\n      if (receivedEntry.success == true) {\r\n        let reStart = {\r\n          roleId: role.roleId,\r\n          loggedInUserId:this.loginUser.userId\r\n        }\r\n        this.httpUtilService.loadingSubject.next(true);\r\n        this.UserService.deleteRole(reStart).subscribe((data: any) => {\r\n          if (!data.isFault) {\r\n            this.httpUtilService.loadingSubject.next(false);\r\n            this.layoutUtilService.showSuccess(data.responseData.message, '');\r\n            this.ngOnInit()\r\n          }\r\n        })\r\n      }\r\n    })\r\n  }\r\n  //function to format permission for export excel\r\n  formatexcelPermission(pemissions:any){\r\n    let permArray:any = JSON.parse(pemissions);\r\n    let permData= '';\r\n\r\n    each(permArray, (r:any, i:any)=>{\r\n      _.forEach(r, function (value, key) {\r\n      let rArray = [];\r\n      value.indexOf('Read') === -1?'':rArray.push('Read');\r\n      value.indexOf('Write') === -1?'':rArray.push('Write');\r\n      value.indexOf('Delete') === -1?'':rArray.push('Delete');\r\n        let nIndex =value.length>0?rArray.join(', '):'None';\r\n        permData +=key+\" : \"+ nIndex+'\\n';\r\n\r\n      });\r\n\r\n    });\r\n    return permData;\r\n  }\r\n\r\n}\r\n", "<!-- Full Screen Loading Overlay -->\r\n<div *ngIf=\"loading || isLoading\" class=\"fullscreen-loading-overlay\">\r\n  <div class=\"loading-content\">\r\n    <div class=\"custom-colored-spinner\" role=\"status\">\r\n      <span class=\"visually-hidden\">Loading...</span>\r\n    </div>\r\n    <div class=\"mt-4 fs-5\">Loading...</div>\r\n  </div>\r\n</div>\r\n\r\n<div class=\"grid-container\">\r\n  <kendo-grid\r\n    #normalGrid\r\n    [data]=\"serverSideRowData\"\r\n    [pageSize]=\"page.size\"\r\n    [sort]=\"sort\"\r\n    [pageable]=\"{\r\n      pageSizes: [10, 15, 20, 50, 100],\r\n      previousNext: true,\r\n      info: true,\r\n      type: 'numeric',\r\n      buttonCount: 5\r\n    }\"\r\n    [sortable]=\"{ allowUnsort: true, mode: 'single' }\"\r\n    [groupable]=\"false\"\r\n    [selectable]=\"{ checkboxOnly: true, mode: 'multiple' }\"\r\n    (columnReorder)=\"onColumnReorder($event)\"\r\n    (selectionChange)=\"onSelectionChange($event)\"\r\n    [reorderable]=\"true\"\r\n    style=\"width: auto; overflow-x: auto\"\r\n    [resizable]=\"false\"\r\n    [height]=\"720\"\r\n    [skip]=\"skip\"\r\n    [filter]=\"filter\"\r\n    [columnMenu]=\"{ filter: true }\"\r\n    (filterChange)=\"filterChange($event)\"\r\n    (pageChange)=\"pageChange($event)\"\r\n    (sortChange)=\"onSortChange($event)\"\r\n    (columnVisibilityChange)=\"updateColumnVisibility($event)\"\r\n  >\r\n    <ng-template kendoGridToolbarTemplate>\r\n      <!-- Search Section -->\r\n      <div class=\"d-flex align-items-center me-3 search-section\">\r\n        <kendo-textbox\r\n          [style.width.px]=\"500\"\r\n          placeholder=\"Search...\"\r\n          [(ngModel)]=\"searchData\"\r\n          [clearButton]=\"true\"\r\n          (keydown)=\"onSearchKeyDown($event)\"\r\n          (valueChange)=\"onSearchChange()\"\r\n        ></kendo-textbox>\r\n      </div>\r\n\r\n      <kendo-grid-spacer></kendo-grid-spacer>\r\n\r\n      <!-- Total Count - Repositioned to the right -->\r\n      <div class=\"d-flex align-items-center me-3\">\r\n        <span class=\"text-muted\">Total: </span>\r\n        <span class=\"fw-bold ms-1\">{{ page.totalElements || 0 }}</span>\r\n      </div>\r\n\r\n      <!-- Action Buttons -->\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-icon btn-sm me-2\"\r\n        (click)=\"create()\"\r\n        title=\"Add Role\"\r\n      >\r\n        <span\r\n          [inlineSVG]=\"'./assets/media/icons/duotune/arrows/arr075.svg'\"\r\n          class=\"svg-icon svg-icon-3 text-primary\"\r\n        ></span>\r\n      </button>\r\n\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-icon btn-sm me-2\"\r\n        (click)=\"toggleExpand()\"\r\n        title=\"Toggle Grid Expansion\"\r\n      >\r\n        <i\r\n          class=\"fas text-secondary\"\r\n          [class.fa-expand]=\"!isExpanded\"\r\n          [class.fa-compress]=\"isExpanded\"\r\n        ></i>\r\n      </button>\r\n\r\n      <!-- <kendo-dropdownbutton\r\n        text=\"Export Excel\"\r\n        iconClass=\"fas fa-file-excel\"\r\n        [data]=\"exportOptions\"\r\n        class=\"custom-dropdown\"\r\n        (itemClick)=\"onExportClick($event)\"\r\n        title=\"Export\"\r\n      >\r\n      </kendo-dropdownbutton> -->\r\n\r\n      <!-- Save Column Settings Button -->\r\n      <!-- <button\r\n        type=\"button\"\r\n        class=\"btn btn-icon btn-sm me-2\"\r\n        (click)=\"saveHead()\"\r\n        title=\"Save Column Settings\"\r\n      >\r\n        <i class=\"fas fa-save text-success\"></i>\r\n      </button> -->\r\n\r\n      <!-- Reset Button -->\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-icon btn-sm me-2\"\r\n        (click)=\"resetTable()\"\r\n        title=\"Reset to Default\"\r\n      >\r\n        <i class=\"fas fa-undo text-warning\"></i>\r\n      </button>\r\n\r\n      <!-- Refresh Button -->\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-icon btn-sm me-2\"\r\n        (click)=\"refreshGrid()\"\r\n        title=\"Refresh Grid Data\"\r\n      >\r\n        <i class=\"fas fa-sync-alt text-info\"></i>\r\n      </button>\r\n    </ng-template>\r\n\r\n    <!-- Advanced Filters Panel -->\r\n    <ng-template kendoGridToolbarTemplate>\r\n      <div\r\n        *ngIf=\"showAdvancedFilters\"\r\n        class=\"advanced-filters-panel p-3 bg-light border-bottom\"\r\n      >\r\n        <div class=\"row\">\r\n          <div class=\"col-md-3\">\r\n            <label class=\"form-label\">Status</label>\r\n            <kendo-dropdownlist\r\n              [data]=\"advancedFilterOptions.status\"\r\n              [(ngModel)]=\"appliedFilters.status\"\r\n              textField=\"text\"\r\n              valueField=\"value\"\r\n              placeholder=\"Select Status\"\r\n            >\r\n            </kendo-dropdownlist>\r\n          </div>\r\n          <div class=\"col-md-3 d-flex align-items-end\">\r\n            <button\r\n              kendoButton\r\n              (click)=\"applyAdvancedFilters()\"\r\n              class=\"btn-primary me-2\"\r\n            >\r\n              <i class=\"fas fa-check\"></i> Apply Filters\r\n            </button>\r\n            <button\r\n              kendoButton\r\n              (click)=\"clearAllFilters()\"\r\n              class=\"btn-secondary\"\r\n            >\r\n              <i class=\"fas fa-times\"></i> Clear\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </ng-template>\r\n\r\n    <ng-container *ngFor=\"let column of gridColumns\">\r\n      <!-- Action Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'action'\"\r\n        title=\"Actions\"\r\n        [width]=\"90\"\r\n        [sticky]=\"true\"\r\n        [reorderable]=\"!fixedColumns.includes('action')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [includeInChooser]=\"false\"\r\n        [columnMenu]=\"false\"\r\n        [sortable]=\"false\"\r\n        [style]=\"{ 'background-color': '#efefef !important' }\"\r\n        [hidden]=\"getHiddenField('action')\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <a\r\n            title=\"Edit\"\r\n            class=\"btn btn-icon btn-sm\"\r\n            (click)=\"edit(dataItem.roleId)\"\r\n          >\r\n            <span\r\n              [inlineSVG]=\"'./assets/media/icons/duotune/general/gen055.svg'\"\r\n              class=\"svg-icon svg-icon-3 svg-icon-primary\"\r\n            >\r\n            </span>\r\n          </a>\r\n          <!-- Delete button hidden -->\r\n          <!-- <a\r\n            title=\"Delete\"\r\n            class=\"btn btn-icon btn-sm mx-1\"\r\n            *ngIf=\"dataItem.isDeletable === true\"\r\n            (click)=\"deleteRoles(dataItem)\"\r\n          >\r\n            <span\r\n              [inlineSVG]=\"'./assets/media/icons/duotune/general/gen027.svg'\"\r\n              class=\"svg-icon svg-icon-3 svg-icon-danger\"\r\n            >\r\n            </span>\r\n          </a> -->\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Name Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'roleName'\"\r\n        field=\"roleName\"\r\n        title=\"Name\"\r\n        [width]=\"200\"\r\n        [sticky]=\"true\"\r\n        [reorderable]=\"!fixedColumns.includes('roleName')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [includeInChooser]=\"false\"\r\n        [hidden]=\"getHiddenField('roleName')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span class=\"fw-bolder cursor-pointer\">\r\n              {{ dataItem.roleName }}\r\n            </span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"true\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\r\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Permissions Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'rolePermissions'\"\r\n        field=\"rolePermissions\"\r\n        title=\"Permissions\"\r\n        [width]=\"400\"\r\n        [reorderable]=\"!fixedColumns.includes('rolePermissions')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('rolePermissions')\"\r\n        [filterable]=\"true\"\r\n        [sortable]=\"false\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div [innerHTML]=\"formatPermission(dataItem.rolePermissions)\"></div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"true\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\r\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Status Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'status'\"\r\n        field=\"status\"\r\n        title=\"Status\"\r\n        [width]=\"100\"\r\n        [reorderable]=\"!fixedColumns.includes('status')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('status')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <span\r\n            *ngIf=\"dataItem.status === 'Active'\"\r\n            ngbTooltip=\"Active\"\r\n            [inlineSVG]=\"'./assets/media/icons/duotune/general/gen037.svg'\"\r\n            class=\"svg-icon svg-icon-3 svg-icon-success\"\r\n          >\r\n            {{ dataItem.status }}\r\n          </span>\r\n          <span\r\n            *ngIf=\"dataItem.status === 'Inactive'\"\r\n            ngbTooltip=\"Inactive\"\r\n            [inlineSVG]=\"'./assets/media/icons/duotune/general/gen040.svg'\"\r\n            class=\"svg-icon svg-icon-3 svg-icon-danger text-danger\"\r\n          >\r\n            {{ dataItem.status }}\r\n          </span>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"true\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\r\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Last Updated Date Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'lastUpdatedDate'\"\r\n        field=\"lastUpdatedDate\"\r\n        title=\"Updated Date\"\r\n        [width]=\"180\"\r\n        [reorderable]=\"!fixedColumns.includes('lastUpdatedDate')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('lastUpdatedDate')\"\r\n        [filterable]=\"true\"\r\n        format=\"MM/dd/yyyy\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span class=\"fw-bolder\"\r\n              >{{ dataItem.lastUpdatedDate | date : \"MM/dd/yyyy\" }}\r\n              {{ dataItem.lastUpdatedDate | date : \"hh:mm a\" }}</span\r\n            ><br />\r\n            <span>{{ dataItem.lastUpdatedByFullName }}</span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-date-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"true\"\r\n          >\r\n            <kendo-filter-gte-operator></kendo-filter-gte-operator>\r\n            <kendo-filter-lte-operator></kendo-filter-lte-operator>\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n          </kendo-grid-date-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n    </ng-container>\r\n\r\n    <!-- No Data Template -->\r\n    <ng-template kendoGridNoRecordsTemplate>\r\n      <div class=\"custom-no-records\" *ngIf=\"!loading && !isLoading\">\r\n        <div class=\"text-center\">\r\n          <i\r\n            class=\"fas fa-shield-alt text-muted mb-2\"\r\n            style=\"font-size: 2rem\"\r\n          ></i>\r\n          <p class=\"text-muted\">No roles found</p>\r\n          <button kendoButton (click)=\"loadTable()\" class=\"btn-primary\">\r\n            <i class=\"fas fa-refresh me-2\"></i>Refresh\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </ng-template>\r\n  </kendo-grid>\r\n</div>\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,gBAAgB;AAE5C,OAAOC,CAAC,IAAIC,IAAI,QAAQ,QAAQ;AAChC,SAASC,GAAG,EAAEC,OAAO,EAAEC,YAAY,EAAEC,oBAAoB,QAAsB,MAAM;AACrF,SAASC,WAAW,QAAQ,sBAAsB;AAGlD,SAASC,2BAA2B,QAAQ,gEAAgE;AAK5G,SAASC,iBAAiB,QAAQ,kCAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICT9DC,EAHN,CAAAC,cAAA,aAAqE,aACtC,aACuB,eAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAErCF,EAFqC,CAAAG,YAAA,EAAM,EACnC,EACF;;;;;;IAmCEH,EADF,CAAAC,cAAA,cAA2D,wBAQxD;IAJCD,EAAA,CAAAI,gBAAA,2BAAAC,gFAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAG,UAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,UAAA,GAAAN,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAwB;IAGxBN,EADA,CAAAc,UAAA,qBAAAC,0EAAAT,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAWJ,MAAA,CAAAO,eAAA,CAAAV,MAAA,CAAuB;IAAA,EAAC,yBAAAW,8EAAA;MAAAjB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CACpBJ,MAAA,CAAAS,cAAA,EAAgB;IAAA,EAAC;IAEpClB,EADG,CAAAG,YAAA,EAAgB,EACb;IAENH,EAAA,CAAAmB,SAAA,wBAAuC;IAIrCnB,EADF,CAAAC,cAAA,cAA4C,eACjB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;IAGNH,EAAA,CAAAC,cAAA,iBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAM,iEAAA;MAAApB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAY,MAAA,EAAQ;IAAA,EAAC;IAGlBrB,EAAA,CAAAmB,SAAA,eAGQ;IACVnB,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAQ,kEAAA;MAAAtB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAc,YAAA,EAAc;IAAA,EAAC;IAGxBvB,EAAA,CAAAmB,SAAA,aAIK;IACPnB,EAAA,CAAAG,YAAA,EAAS;IAuBTH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAU,kEAAA;MAAAxB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAgB,UAAA,EAAY;IAAA,EAAC;IAGtBzB,EAAA,CAAAmB,SAAA,aAAwC;IAC1CnB,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAY,kEAAA;MAAA1B,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAkB,WAAA,EAAa;IAAA,EAAC;IAGvB3B,EAAA,CAAAmB,SAAA,aAAyC;IAC3CnB,EAAA,CAAAG,YAAA,EAAS;;;;IAjFLH,EAAA,CAAA4B,SAAA,EAAsB;IAAtB5B,EAAA,CAAA6B,WAAA,oBAAsB;IAEtB7B,EAAA,CAAA8B,gBAAA,YAAArB,MAAA,CAAAG,UAAA,CAAwB;IACxBZ,EAAA,CAAA+B,UAAA,qBAAoB;IAWK/B,EAAA,CAAA4B,SAAA,GAA6B;IAA7B5B,EAAA,CAAAgC,iBAAA,CAAAvB,MAAA,CAAAwB,IAAA,CAAAC,aAAA,MAA6B;IAWtDlC,EAAA,CAAA4B,SAAA,GAA8D;IAA9D5B,EAAA,CAAA+B,UAAA,+DAA8D;IAa9D/B,EAAA,CAAA4B,SAAA,GAA+B;IAC/B5B,EADA,CAAAmC,WAAA,eAAA1B,MAAA,CAAA2B,UAAA,CAA+B,gBAAA3B,MAAA,CAAA2B,UAAA,CACC;;;;;;IAqD9BpC,EANN,CAAAC,cAAA,cAGC,cACkB,cACO,gBACM;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxCH,EAAA,CAAAC,cAAA,6BAMC;IAJCD,EAAA,CAAAI,gBAAA,2BAAAiC,2FAAA/B,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAA8B,cAAA,CAAAC,MAAA,EAAAlC,MAAA,MAAAG,MAAA,CAAA8B,cAAA,CAAAC,MAAA,GAAAlC,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAmC;IAMvCN,EADE,CAAAG,YAAA,EAAqB,EACjB;IAEJH,EADF,CAAAC,cAAA,cAA6C,iBAK1C;IAFCD,EAAA,CAAAc,UAAA,mBAAA2B,uEAAA;MAAAzC,EAAA,CAAAO,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAiC,oBAAA,EAAsB;IAAA,EAAC;IAGhC1C,EAAA,CAAAmB,SAAA,YAA4B;IAACnB,EAAA,CAAAE,MAAA,sBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAc,UAAA,mBAAA6B,wEAAA;MAAA3C,EAAA,CAAAO,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAmC,eAAA,EAAiB;IAAA,EAAC;IAG3B5C,EAAA,CAAAmB,SAAA,aAA4B;IAACnB,EAAA,CAAAE,MAAA,eAC/B;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IAzBEH,EAAA,CAAA4B,SAAA,GAAqC;IAArC5B,EAAA,CAAA+B,UAAA,SAAAtB,MAAA,CAAAoC,qBAAA,CAAAL,MAAA,CAAqC;IACrCxC,EAAA,CAAA8B,gBAAA,YAAArB,MAAA,CAAA8B,cAAA,CAAAC,MAAA,CAAmC;;;;;IAT3CxC,EAAA,CAAA8C,UAAA,IAAAC,8CAAA,mBAGC;;;;IAFE/C,EAAA,CAAA+B,UAAA,SAAAtB,MAAA,CAAAuC,mBAAA,CAAyB;;;;;;IAmDxBhD,EAAA,CAAAC,cAAA,YAIC;IADCD,EAAA,CAAAc,UAAA,mBAAAmC,+FAAA;MAAA,MAAAC,WAAA,GAAAlD,EAAA,CAAAO,aAAA,CAAA4C,GAAA,EAAAC,SAAA;MAAA,MAAA3C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAA4C,IAAA,CAAAH,WAAA,CAAAI,MAAA,CAAqB;IAAA,EAAC;IAE/BtD,EAAA,CAAAmB,SAAA,eAIO;IACTnB,EAAA,CAAAG,YAAA,EAAI;;;IAJAH,EAAA,CAAA4B,SAAA,EAA+D;IAA/D5B,EAAA,CAAA+B,UAAA,gEAA+D;;;;;IApBvE/B,EAAA,CAAAC,cAAA,4BAYC;IACCD,EAAA,CAAA8C,UAAA,IAAAS,2EAAA,0BAAgD;IA0BlDvD,EAAA,CAAAG,YAAA,EAAoB;;;;IA7BlBH,EAAA,CAAAwD,UAAA,CAAAxD,EAAA,CAAAyD,eAAA,KAAAC,GAAA,EAAsD;IACtD1D,EARA,CAAA+B,UAAA,aAAY,gBACG,iBAAAtB,MAAA,CAAAkD,YAAA,CAAAC,QAAA,WACiC,gBAAA5D,EAAA,CAAAyD,eAAA,KAAAI,GAAA,EACuB,2BAC7C,qBACN,mBACF,WAAApD,MAAA,CAAAqD,cAAA,WAEiB;;;;;IA6C/B9D,EADF,CAAAC,cAAA,UAAK,eACoC;IACrCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAFFH,EAAA,CAAA4B,SAAA,GACF;IADE5B,EAAA,CAAA+D,kBAAA,MAAAC,WAAA,CAAAC,QAAA,MACF;;;;;IAIFjE,EAAA,CAAAC,cAAA,wCAIC;IAKCD,EAJA,CAAAmB,SAAA,qCAAiE,+BACZ,gCACE,uCACc,qCACJ;IACnEnB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAA+B,UAAA,WAAAmC,SAAA,CAAiB,WAAAC,SAAA,CACA,eACH;;;;;IAvBpBnE,EAAA,CAAAC,cAAA,4BAWC;IAQCD,EAPA,CAAA8C,UAAA,IAAAsB,2EAAA,0BAAgD,IAAAC,2EAAA,0BAOwB;IAa1ErE,EAAA,CAAAG,YAAA,EAAoB;;;;IAtBlBH,EANA,CAAA+B,UAAA,cAAa,gBACE,iBAAAtB,MAAA,CAAAkD,YAAA,CAAAC,QAAA,aACmC,gBAAA5D,EAAA,CAAAyD,eAAA,IAAAI,GAAA,EACqB,2BAC7C,WAAApD,MAAA,CAAAqD,cAAA,aACW,oBAClB;;;;;IAqCjB9D,EAAA,CAAAmB,SAAA,cAAoE;;;;;IAA/DnB,EAAA,CAAA+B,UAAA,cAAAtB,MAAA,CAAA6D,gBAAA,CAAAC,YAAA,CAAAC,eAAA,GAAAxE,EAAA,CAAAyE,cAAA,CAAwD;;;;;IAG7DzE,EAAA,CAAAC,cAAA,wCAIC;IAKCD,EAJA,CAAAmB,SAAA,qCAAiE,+BACZ,gCACE,uCACc,qCACJ;IACnEnB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAA+B,UAAA,WAAA2C,UAAA,CAAiB,WAAAC,UAAA,CACA,eACH;;;;;IAlBpB3E,EAAA,CAAAC,cAAA,4BAUC;IAICD,EAHA,CAAA8C,UAAA,IAAA8B,2EAAA,0BAAgD,IAAAC,2EAAA,0BAGwB;IAa1E7E,EAAA,CAAAG,YAAA,EAAoB;;;;IAlBlBH,EALA,CAAA+B,UAAA,cAAa,iBAAAtB,MAAA,CAAAkD,YAAA,CAAAC,QAAA,oBAC4C,gBAAA5D,EAAA,CAAAyD,eAAA,IAAAI,GAAA,EACc,WAAApD,MAAA,CAAAqD,cAAA,oBAC3B,oBACzB,mBACD;;;;;IAgChB9D,EAAA,CAAAC,cAAA,eAKC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAJLH,EAAA,CAAA+B,UAAA,gEAA+D;IAG/D/B,EAAA,CAAA4B,SAAA,EACF;IADE5B,EAAA,CAAA+D,kBAAA,MAAAe,YAAA,CAAAtC,MAAA,MACF;;;;;IACAxC,EAAA,CAAAC,cAAA,eAKC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAJLH,EAAA,CAAA+B,UAAA,gEAA+D;IAG/D/B,EAAA,CAAA4B,SAAA,EACF;IADE5B,EAAA,CAAA+D,kBAAA,MAAAe,YAAA,CAAAtC,MAAA,MACF;;;;;IAPAxC,EARA,CAAA8C,UAAA,IAAAiC,kFAAA,mBAKC,IAAAC,kFAAA,mBAQA;;;;IAZEhF,EAAA,CAAA+B,UAAA,SAAA+C,YAAA,CAAAtC,MAAA,cAAkC;IAQlCxC,EAAA,CAAA4B,SAAA,EAAoC;IAApC5B,EAAA,CAAA+B,UAAA,SAAA+C,YAAA,CAAAtC,MAAA,gBAAoC;;;;;IASvCxC,EAAA,CAAAC,cAAA,wCAIC;IAKCD,EAJA,CAAAmB,SAAA,qCAAiE,+BACZ,gCACE,uCACc,qCACJ;IACnEnB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAA+B,UAAA,WAAAkD,UAAA,CAAiB,WAAAC,UAAA,CACA,eACH;;;;;IAhCpBlF,EAAA,CAAAC,cAAA,4BASC;IAmBCD,EAlBA,CAAA8C,UAAA,IAAAqC,2EAAA,0BAAgD,IAAAC,2EAAA,0BAkBwB;IAa1EpF,EAAA,CAAAG,YAAA,EAAoB;;;;IAjClBH,EAJA,CAAA+B,UAAA,cAAa,iBAAAtB,MAAA,CAAAkD,YAAA,CAAAC,QAAA,WACmC,gBAAA5D,EAAA,CAAAyD,eAAA,IAAAI,GAAA,EACuB,WAAApD,MAAA,CAAAqD,cAAA,WACpC,oBAChB;;;;;IAiDf9D,EADF,CAAAC,cAAA,UAAK,eAEA;IAAAD,EAAA,CAAAE,MAAA,GACgD;;;IAAAF,EAAA,CAAAG,YAAA,EAClD;IAAAH,EAAA,CAAAmB,SAAA,SAAM;IACPnB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IAC5CF,EAD4C,CAAAG,YAAA,EAAO,EAC7C;;;;IAJDH,EAAA,CAAA4B,SAAA,GACgD;IADhD5B,EAAA,CAAAqF,kBAAA,KAAArF,EAAA,CAAAsF,WAAA,OAAAC,YAAA,CAAAC,eAAA,sBAAAxF,EAAA,CAAAsF,WAAA,OAAAC,YAAA,CAAAC,eAAA,iBACgD;IAE7CxF,EAAA,CAAA4B,SAAA,GAAoC;IAApC5B,EAAA,CAAAgC,iBAAA,CAAAuD,YAAA,CAAAE,qBAAA,CAAoC;;;;;IAI5CzF,EAAA,CAAAC,cAAA,sCAIC;IAICD,EAHA,CAAAmB,SAAA,gCAAuD,gCACA,+BACF,gCACE;IACzDnB,EAAA,CAAAG,YAAA,EAA8B;;;;;IAN5BH,EAFA,CAAA+B,UAAA,WAAA2D,UAAA,CAAiB,WAAAC,UAAA,CACA,eACH;;;;;IAxBpB3F,EAAA,CAAAC,cAAA,4BAUC;IAUCD,EATA,CAAA8C,UAAA,IAAA8C,2EAAA,0BAAgD,IAAAC,2EAAA,0BASwB;IAY1E7F,EAAA,CAAAG,YAAA,EAAoB;;;;IAxBlBH,EAJA,CAAA+B,UAAA,cAAa,iBAAAtB,MAAA,CAAAkD,YAAA,CAAAC,QAAA,oBAC4C,gBAAA5D,EAAA,CAAAyD,eAAA,IAAAI,GAAA,EACc,WAAApD,MAAA,CAAAqD,cAAA,oBAC3B,oBACzB;;;;;IAjKvB9D,EAAA,CAAA8F,uBAAA,GAAiD;IAyJ/C9F,EAvJA,CAAA8C,UAAA,IAAAiD,6DAAA,iCAYC,IAAAC,6DAAA,gCAyCA,IAAAC,6DAAA,gCAkCA,IAAAC,6DAAA,gCA6BA,IAAAC,6DAAA,gCA6CA;;;;;IAhKEnG,EAAA,CAAA4B,SAAA,EAAyB;IAAzB5B,EAAA,CAAA+B,UAAA,SAAAqE,UAAA,cAAyB;IA0CzBpG,EAAA,CAAA4B,SAAA,EAA2B;IAA3B5B,EAAA,CAAA+B,UAAA,SAAAqE,UAAA,gBAA2B;IAmC3BpG,EAAA,CAAA4B,SAAA,EAAkC;IAAlC5B,EAAA,CAAA+B,UAAA,SAAAqE,UAAA,uBAAkC;IA8BlCpG,EAAA,CAAA4B,SAAA,EAAyB;IAAzB5B,EAAA,CAAA+B,UAAA,SAAAqE,UAAA,cAAyB;IA4CzBpG,EAAA,CAAA4B,SAAA,EAAkC;IAAlC5B,EAAA,CAAA+B,UAAA,SAAAqE,UAAA,uBAAkC;;;;;;IAqCnCpG,EADF,CAAAC,cAAA,cAA8D,cACnC;IACvBD,EAAA,CAAAmB,SAAA,YAGK;IACLnB,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAC,cAAA,iBAA8D;IAA1CD,EAAA,CAAAc,UAAA,mBAAAuF,uEAAA;MAAArG,EAAA,CAAAO,aAAA,CAAA+F,IAAA;MAAA,MAAA7F,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAA8F,SAAA,EAAW;IAAA,EAAC;IACvCvG,EAAA,CAAAmB,SAAA,YAAmC;IAAAnB,EAAA,CAAAE,MAAA,eACrC;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IAXNH,EAAA,CAAA8C,UAAA,IAAA0D,8CAAA,kBAA8D;;;;IAA9BxG,EAAA,CAAA+B,UAAA,UAAAtB,MAAA,CAAAgG,OAAA,KAAAhG,MAAA,CAAAiG,SAAA,CAA4B;;;ADpTlE,OAAM,MAAOC,iBAAiB;EAsJlBC,GAAA;EACAC,YAAA;EACDC,cAAA;EACCC,eAAA;EACDC,UAAA;EACCC,iBAAA;EACAC,WAAA;EACAC,WAAA;EACAC,kBAAA;EA7JeC,IAAI;EAE7B;EACOC,iBAAiB,GAAe,EAAE;EAClCC,QAAQ,GAAe,EAAE;EACzBC,cAAc,GAAY,KAAK;EAE/Bf,OAAO,GAAY,KAAK;EACxBC,SAAS,GAAY,KAAK;EAEjCe,SAAS,GAAwB,EAAE;EAEnC;EACO7G,UAAU,GAAW,EAAE;EACtB8G,WAAW,GAAG,IAAIhI,OAAO,EAAU;EACnCiI,kBAAkB;EAE1B;EACOC,MAAM,GAA8B;IAAEC,KAAK,EAAE,KAAK;IAAEC,OAAO,EAAE;EAAE,CAAE;EACjEC,UAAU,GAA8B;IAAEF,KAAK,EAAE,KAAK;IAAEC,OAAO,EAAE;EAAE,CAAE;EACrEE,aAAa,GAIf,EAAE;EAEAC,aAAa,GAAkD,CACpE;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAI,CAAE,EAC5B;IAAED,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAQ,CAAE,EACnC;IAAED,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAE,CACxC;EAED;EACOtF,qBAAqB,GAAG;IAC7BL,MAAM,EAAE,CACN;MAAE0F,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAI,CAAE,EAC5B;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACnC;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAU,CAAE;GAE1C;EAED;EACOnF,mBAAmB,GAAG,KAAK;EAC3BT,cAAc,GAEjB;IAAEC,MAAM,EAAE;EAAI,CAAE;EAEpB;EACOP,IAAI,GAAe;IACxBmG,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,CAAC;IACbnG,aAAa,EAAE,CAAC;IAChBoG,UAAU,EAAE,CAAC;IACbC,OAAO,EAAE,iBAAiB;IAC1BC,QAAQ,EAAE;GACX;EACMC,IAAI,GAAW,CAAC;EAChBC,IAAI,GAAqB,CAC9B;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,GAAG,EAAE;EAAM,CAAE,CAC1C;EAED;EACOC,SAAS;EACTC,UAAU,GAAQ,EAAE;EACpBC,aAAa,GAAQ,EAAE;EACvBC,iBAAiB,GAAQ,EAAE;EAC3BC,YAAY,GAAQ,EAAE;EAE7B;EACOC,WAAW,GAAa,EAAE;EAC1BC,cAAc,GAAa,EAAE;EAC7BxF,YAAY,GAAa,EAAE;EAC3ByF,gBAAgB,GAAa,EAAE;EAC/BC,UAAU;EACVC,YAAY;EACZlH,UAAU,GAAG,KAAK;EAEzB;EACOmH,gBAAgB,GAQlB,CACH;IAAEZ,KAAK,EAAE,QAAQ;IAAEa,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE,EAAE;IAAEC,OAAO,EAAE,IAAI;IAAEC,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAC,CAAE,EACxF;IAAEjB,KAAK,EAAE,UAAU;IAAEa,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE,GAAG;IAAEC,OAAO,EAAE,IAAI;IAAEC,IAAI,EAAE,MAAM;IAAEE,UAAU,EAAE,IAAI;IAAED,KAAK,EAAE;EAAC,CAAE,EACzG;IAAEjB,KAAK,EAAE,iBAAiB;IAAEa,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE,GAAG;IAAEC,OAAO,EAAE,KAAK;IAAEC,IAAI,EAAE,MAAM;IAAEE,UAAU,EAAE,IAAI;IAAED,KAAK,EAAE;EAAC,CAAE,EACxH;IAAEjB,KAAK,EAAE,QAAQ;IAAEa,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE,GAAG;IAAEE,IAAI,EAAE,QAAQ;IAAED,OAAO,EAAE,KAAK;IAAEG,UAAU,EAAE,IAAI;IAAED,KAAK,EAAE;EAAC,CAAE,EAC5G;IAAEjB,KAAK,EAAE,iBAAiB;IAAEa,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE,GAAG;IAAEC,OAAO,EAAE,KAAK;IAAEC,IAAI,EAAE,MAAM;IAAEE,UAAU,EAAE,IAAI;IAAED,KAAK,EAAE;EAAC,CAAE,CAC1H;EAED;EACOE,UAAU,GAAG,CAClB;IAAEC,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,UAAU;IAAEJ,KAAK,EAAE,MAAM;IAAEH,KAAK,EAAE,KAAK;IAAEf,IAAI,EAAE;EAAI,CAAE,EAC3E;IAAEqB,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAE,iBAAiB;IAAEJ,KAAK,EAAE,MAAM;IAAEH,KAAK,EAAE,KAAK;IAAEf,IAAI,EAAE;EAAI,CAAE,EACzF;IAAEqB,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE,QAAQ;IAAEJ,KAAK,EAAE,MAAM;IAAEH,KAAK,EAAE,KAAK;IAAEf,IAAI,EAAE;EAAI,CAAE,EAC3E;IAAEqB,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,iBAAiB;IAAEJ,KAAK,EAAE,MAAM;IAAEH,KAAK,EAAE,KAAK;IAAEf,IAAI,EAAE;EAAI,CAAE,EACrF;IAAEqB,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE,QAAQ;IAAEJ,KAAK,EAAE,MAAM;IAAEH,KAAK,EAAE,KAAK;IAAEf,IAAI,EAAE;EAAK,CAAE,CAC7E;EAED;EACQuB,kBAAkB;EAE1B;EACiBC,cAAc,GAAG,kBAAkB;EAEpD;EACOC,aAAa,GAA2C,CAC7D;IAAEjC,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAK,CAAE,EACpC;IAAED,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAU,CAAE,EAC9C;IAAED,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAU,CAAE,CAC/C;EAED;EACOiC,aAAa,GAAe,EAAE;EAC9BC,aAAa,GAAY,KAAK;EAErC;EACOC,cAAc,GAIjB;IACFC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,UAAU,EAAE;GACb;EAED;EACOC,eAAe,GAAG,KAAK;EACvBC,gBAAgB,GAAW,QAAQ;EAE1C;EACAC,QAAQ,GAAW/K,WAAW,CAACgL,SAAS;EACxCC,eAAe,GAAQjL,WAAW,CAACkL,iBAAiB;EACpDC,YAAY,GAAG,IAAI1L,WAAW,CAAC,IAAI,CAACsL,QAAQ,CAAC;EAC7CK,YAAY,GAAG,MAAM;EACrBC,cAAc,GAAG,iBAAiB;EAClCC,YAAY,GAAQ,EAAE;EACtBC,UAAU,GAAY,KAAK;EAC3BC,eAAe,GAAQ,EAAE;EACzBC,WAAW,GAAG,KAAK;EACnBC,UAAU;EACVC,aAAa,GAAY,KAAK;EAE9BC,YACU7E,GAAsB,EACtBC,YAAsB,EACvBC,cAA8B,EAC7BC,eAAiC,EAClCC,UAAsB,EACrBC,iBAA2C,EAC3CC,WAAwB,EACxBC,WAAwB,EACxBC,kBAAsC;IARtC,KAAAR,GAAG,GAAHA,GAAG;IACH,KAAAC,YAAY,GAAZA,YAAY;IACb,KAAAC,cAAc,GAAdA,cAAc;IACb,KAAAC,eAAe,GAAfA,eAAe;IAChB,KAAAC,UAAU,GAAVA,UAAU;IACT,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAE1B;IACA,IAAI,CAACnF,IAAI,CAACoG,UAAU,GAAG,CAAC;IACxB,IAAI,CAACpG,IAAI,CAACmG,IAAI,GAAG,IAAI,CAACwC,QAAQ;IAC9B,IAAI,CAAC3I,IAAI,CAACsG,OAAO,GAAG,iBAAiB;IACrC,IAAI,CAACtG,IAAI,CAACuG,QAAQ,GAAG,MAAM;EAC7B;EAEAkD,QAAQA,CAAA;IACN,IAAI,CAACjE,SAAS,GAAG,IAAI,CAACT,UAAU,CAAC2E,eAAe,EAAE;IAClDC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACpE,SAAS,CAAC;IAEjD,IAAI,CAAC8D,UAAU,GAAGO,MAAM,CAACP,UAAU;IACnC,IAAI,IAAI,CAACA,UAAU,IAAI,GAAG,IAAM,IAAI,CAACA,UAAU,GAAE,GAAI,EAAC;MACrD,IAAI,CAACC,aAAa,GAAG,IAAI;IAC1B,CAAC,MAAI;MACJ,IAAI,CAACA,aAAa,GAAG,KAAK;IAC3B;IAEA;IACA,IAAI,CAAC7D,kBAAkB,GAAG,IAAI,CAACD,WAAW,CAACqE,IAAI,CAC7CpM,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,CACvB,CAACoM,SAAS,CAAEC,UAAU,IAAI;MACzBL,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEI,UAAU,CAAC;MACtD,IAAI,CAAChK,IAAI,CAACoG,UAAU,GAAG,CAAC;MACxB,IAAI,CAACI,IAAI,GAAG,CAAC;MACb;MACA,IAAI,CAAChC,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACH,SAAS,EAAE;IAClB,CAAC,CAAC;IAEF;IACA,IAAI,CAAC2F,SAAS,EAAE;IAEhB;IACA,IAAI,CAACC,UAAU,EAAE;IAEjB;IACA,IAAI,CAACC,gCAAgC,EAAE;IAEvC;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,4BAA4B,EAAE;IACrC,CAAC,EAAE,GAAG,CAAC;IAEPC,YAAY,CAACC,UAAU,CAAC,SAAS,CAAC;EACpC;EAEA;;;EAGQJ,gCAAgCA,CAAA;IACtC;IACA,IAAI,CAACjD,cAAc,GAAG,IAAI,CAACI,gBAAgB,CAAC9J,GAAG,CAACgN,GAAG,IAAIA,GAAG,CAAC9D,KAAK,CAAC;IACjE,IAAI,CAACO,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;IAE3C;IACA,IAAI,CAACxF,YAAY,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC;IAE1C;IACA,IAAI,CAACyF,gBAAgB,GAAG,IAAI,CAACD,cAAc,CAACvB,MAAM,CAAC6E,GAAG,IAAI,CAAC,IAAI,CAAC9I,YAAY,CAACC,QAAQ,CAAC6I,GAAG,CAAC,CAAC;IAE3F;IACA,IAAI,CAACpD,UAAU,GAAG,IAAI,CAAChC,IAAI;IAC3B,IAAI,CAACiC,YAAY,GAAG,IAAI,CAACjC,IAAI;EAC/B;EAEAqF,eAAeA,CAAA;IACb;IACA;IACAL,UAAU,CAAC,MAAK;MACd,IAAI,CAAC9F,SAAS,EAAE;IAClB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACA4F,UAAUA,CAAA;IACR;IACA,IAAI,CAAClK,IAAI,CAACoG,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAACC,IAAI,GAAG,CAAC;MAAEC,KAAK,EAAE,iBAAiB;MAAEC,GAAG,EAAE;IAAM,CAAE,CAAC;IACvD,IAAI,CAAChB,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAAClH,UAAU,GAAG,EAAE;IAEpB;IACA,IAAI,CAAC2F,SAAS,EAAE;EAClB;EAEA;EACQ2F,SAASA,CAAA;IACf,IAAI,CAAChF,WAAW,CAACyF,qBAAqB,CAAC,EAAE,CAAC,CAACX,SAAS,CAAEY,WAAgB,IAAI;MACxE,IAAI,CAACvB,eAAe,GAAGuB,WAAW,CAACC,YAAY;IACjD,CAAC,CAAC;EACJ;EAEA;EACAC,cAAcA,CAAA;IACZ;IACA,IAAI,CAACvG,SAAS,EAAE;EAClB;EAEAwG,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAAC9C,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAAC+C,WAAW,EAAE;IACvC;IACA,IAAI,IAAI,CAACrF,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACqF,WAAW,EAAE;IACvC;IACA,IAAI,CAACtF,WAAW,CAACuF,QAAQ,EAAE;EAC7B;EACA;EACA1G,SAASA,CAAA;IACP;IACA,IAAI,CAAC2G,0BAA0B,EAAE;EACnC;EAEA;EACAA,0BAA0BA,CAAA;IACxB,IAAI,CAACzG,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACK,eAAe,CAACoG,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C;IACAxB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5CD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACnD,IAAI,CAAC;IACpCkD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC5J,IAAI,CAACsG,OAAO,CAAC;IAC/CqD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC5J,IAAI,CAACuG,QAAQ,CAAC;IAEjD,MAAM6E,KAAK,GAAG;MACZC,IAAI,EAAE,IAAI,CAACrL,IAAI,CAACmG,IAAI;MACpBK,IAAI,EAAE,IAAI,CAACA,IAAI;MACfC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAE;MACjBd,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB2F,MAAM,EAAE,IAAI,CAAC3M,UAAU;MACvB4M,cAAc,EAAE,IAAI,CAAC/F,SAAS,CAACgG,MAAM;MACrClF,OAAO,EAAE,IAAI,CAACtG,IAAI,CAACsG,OAAO;MAC1BC,QAAQ,EAAE,IAAI,CAACvG,IAAI,CAACuG;KACrB;IAEDoD,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAACjL,UAAU,CAAC;IACrEgL,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEwB,KAAK,CAAC;IACxCzB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAEhD,IAAI,CAAC1E,WAAW,CAACuG,oBAAoB,CAACL,KAAK,CAAC,CAACrB,SAAS,CAAC;MACrDoB,IAAI,EAAGO,IAUN,IAAI;QACH;QACA,IAAIA,IAAI,CAACC,OAAO,IAAKD,IAAI,CAACd,YAAY,IAAIc,IAAI,CAACd,YAAY,CAACgB,MAAM,IAAIF,IAAI,CAACd,YAAY,CAACgB,MAAM,CAACC,MAAM,GAAG,CAAE,EAAE;UAC1G,MAAMD,MAAM,GAAGF,IAAI,CAACd,YAAY,EAAEgB,MAAM,IAAIF,IAAI,CAACE,MAAM,IAAI,EAAE;UAC7DjC,OAAO,CAACmC,KAAK,CAAC,uBAAuB,EAAEF,MAAM,CAAC;UAC9C,IAAI,CAACG,mBAAmB,EAAE;QAC5B,CAAC,MAAM;UACL;UACA,MAAMnB,YAAY,GAAGc,IAAI,CAACd,YAAY,IAAIc,IAAI;UAC9C,MAAMM,QAAQ,GAAGpB,YAAY,CAACc,IAAI,IAAI,EAAE;UACxC,MAAMO,KAAK,GAAGrB,YAAY,CAACqB,KAAK,IAAI,CAAC;UAErC,IAAI,CAAC1G,cAAc,GAAGyG,QAAQ,CAACH,MAAM,KAAK,CAAC;UAC3C,IAAI,CAACxG,iBAAiB,GAAG,IAAI,CAAC6G,sBAAsB,CAACF,QAAQ,EAAE,IAAI,CAACvF,IAAI,CAAC;UACzE,IAAI,CAACnB,QAAQ,GAAG,IAAI,CAACD,iBAAiB;UACtC,IAAI,CAACrF,IAAI,CAACC,aAAa,GAAGgM,KAAK;UAC/B,IAAI,CAACjM,IAAI,CAACqG,UAAU,GAAG8F,IAAI,CAACC,IAAI,CAACH,KAAK,GAAG,IAAI,CAACjM,IAAI,CAACmG,IAAI,CAAC;QAC1D;QACA,IAAI,CAAC3B,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACK,eAAe,CAACoG,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAACxG,GAAG,CAAC0H,YAAY,EAAE;MACzB,CAAC;MACDP,KAAK,EAAGA,KAAc,IAAI;QACxBnC,OAAO,CAACmC,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClE,IAAI,CAACC,mBAAmB,EAAE;QAC1B,IAAI,CAACvH,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACK,eAAe,CAACoG,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAACxG,GAAG,CAAC0H,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;EACQN,mBAAmBA,CAAA;IACzB,IAAI,CAACxG,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACF,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACtF,IAAI,CAACC,aAAa,GAAG,CAAC;IAC3B,IAAI,CAACD,IAAI,CAACqG,UAAU,GAAG,CAAC;IACxB,IAAI,CAAC7B,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,SAAS,GAAG,KAAK;EACxB;EAEA;EACO6H,UAAUA,CAACC,KAAqC;IACrD,IAAI,CAAC/F,IAAI,GAAG+F,KAAK,CAAC/F,IAAI;IACtB,IAAI,CAACxG,IAAI,CAACmG,IAAI,GAAGoG,KAAK,CAAClB,IAAI;IAC3B,IAAI,CAACrL,IAAI,CAACoG,UAAU,GAAGmG,KAAK,CAAC/F,IAAI,GAAG+F,KAAK,CAAClB,IAAI;IAC9C;IACA,IAAI,CAAC7G,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACwG,0BAA0B,EAAE;EACnC;EAEOuB,YAAYA,CAAC/F,IAAsB;IACxC;IACA,MAAMgG,YAAY,GAAGhG,IAAI,CAACoF,MAAM,GAAG,CAAC,IAAIpF,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACE,GAAG,KAAK+F,SAAS;IAE5E,IAAID,YAAY,EAAE;MAChB;MACA,IAAI,CAAChG,IAAI,GAAG,EAAE;MACd,IAAI,CAACzG,IAAI,CAACsG,OAAO,GAAG,iBAAiB;MACrC,IAAI,CAACtG,IAAI,CAACuG,QAAQ,GAAG,MAAM;IAC7B,CAAC,MAAM,IAAIE,IAAI,CAACoF,MAAM,GAAG,CAAC,IAAIpF,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACE,GAAG,EAAE;MACpD;MACA,IAAI,CAACF,IAAI,GAAGA,IAAI;MAChB,MAAMC,KAAK,GAAGD,IAAI,CAAC,CAAC,CAAC,CAACC,KAAK;MAC3B,MAAMC,GAAG,GAAGF,IAAI,CAAC,CAAC,CAAC,CAACE,GAAG;MAEvB;MACA,IAAIgG,eAAe,GAAGjG,KAAK;MAC3B,QAAQA,KAAK;QACX,KAAK,MAAM;UAAEiG,eAAe,GAAG,UAAU;UAAE;QAC3C,KAAK,QAAQ;UAAEA,eAAe,GAAG,QAAQ;UAAE;QAC3C,KAAK,aAAa;QAClB,KAAK,iBAAiB;QACtB,KAAK,uBAAuB;UAC1BA,eAAe,GAAG,iBAAiB;UAAE;MACzC;MAEA,IAAI,CAAC3M,IAAI,CAACsG,OAAO,GAAGqG,eAAe;MACnC,IAAI,CAAC3M,IAAI,CAACuG,QAAQ,GAAGI,GAAG;IAC1B,CAAC,MAAM;MACL;MACA,IAAI,CAACF,IAAI,GAAG,EAAE;MACd,IAAI,CAACzG,IAAI,CAACsG,OAAO,GAAG,iBAAiB;MACrC,IAAI,CAACtG,IAAI,CAACuG,QAAQ,GAAG,MAAM;IAC7B;IAEA;IACA,IAAI,CAACvG,IAAI,CAACoG,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb;IACA,IAAI,CAAChC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrBkF,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5C,IAAI,CAACqB,0BAA0B,EAAE;EACnC;EAEO2B,YAAYA,CAACL,KAAU;IAC5B,IAAI,CAAC5G,MAAM,GAAG4G,KAAK,CAAC5G,MAAM;IAC1B,IAAI,CAAC3F,IAAI,CAACoG,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb;IACA,IAAI,CAAChC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACwG,0BAA0B,EAAE;EACnC;EAEA;EACQiB,sBAAsBA,CAACR,IAAW,EAAEjF,IAAkC;IAC5E,IAAI,CAACoG,KAAK,CAACC,OAAO,CAACpB,IAAI,CAAC,IAAI,CAACjF,IAAI,IAAIA,IAAI,CAACoF,MAAM,KAAK,CAAC,EAAE,OAAOH,IAAI;IACnE,MAAMqB,CAAC,GAAGtG,IAAI,CAAC,CAAC,CAAC;IACjB,MAAMC,KAAK,GAAIqG,CAAC,CAACrG,KAAgB,IAAI,iBAAiB;IACtD,MAAMC,GAAG,GAAG,CAACoG,CAAC,CAACpG,GAAG,IAAI,KAAK,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;IAE/C,MAAMqG,WAAW,GAAIC,GAAW,IAAI;MAClC;MACA,MAAMC,CAAC,GAAGD,GAAG,IAAIA,GAAG,CAACE,KAAK,CAAC,kEAAkE,CAAC;MAC9F,IAAI,CAACD,CAAC,EAAE,OAAOE,GAAG;MAClB,IAAIC,KAAK,GAAGC,QAAQ,CAACJ,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;MAClC,MAAMK,GAAG,GAAGD,QAAQ,CAACJ,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC9B,IAAIM,IAAI,GAAGF,QAAQ,CAACJ,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC7B,IAAIM,IAAI,GAAG,GAAG,EAAEA,IAAI,IAAI,IAAI;MAC5B,IAAIC,IAAI,GAAGH,QAAQ,CAACJ,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC7B,MAAMQ,MAAM,GAAGJ,QAAQ,CAACJ,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACjC,MAAMS,IAAI,GAAGT,CAAC,CAAC,CAAC,CAAC,CAACU,WAAW,EAAE;MAC/B,IAAID,IAAI,KAAK,IAAI,IAAIF,IAAI,GAAG,EAAE,EAAEA,IAAI,IAAI,EAAE;MAC1C,IAAIE,IAAI,KAAK,IAAI,IAAIF,IAAI,KAAK,EAAE,EAAEA,IAAI,GAAG,CAAC;MAC1C,OAAO,IAAII,IAAI,CAACL,IAAI,EAAEH,KAAK,EAAEE,GAAG,EAAEE,IAAI,EAAEC,MAAM,CAAC,CAACI,OAAO,EAAE;IAC3D,CAAC;IAED,MAAMC,MAAM,GAAIC,IAAS,IAAI;MAC3B,MAAMC,CAAC,GAAGD,IAAI,CAACtH,KAAK,CAAC;MACrB,IAAIA,KAAK,KAAK,iBAAiB,EAAE;QAC/B,IAAIuH,CAAC,YAAYJ,IAAI,EAAE,OAAOI,CAAC,CAACH,OAAO,EAAE;QACzC,MAAMI,EAAE,GAAGL,IAAI,CAACM,KAAK,CAACF,CAAC,CAAC;QACxB,IAAI,CAACG,KAAK,CAACF,EAAE,CAAC,EAAE,OAAOA,EAAE;QACzB,MAAMG,EAAE,GAAGrB,WAAW,CAACsB,MAAM,CAACL,CAAC,CAAC,CAAC;QACjC,OAAOG,KAAK,CAACC,EAAE,CAAC,GAAG,CAAC,GAAGA,EAAE;MAC3B;MACA,IAAI,OAAOJ,CAAC,KAAK,QAAQ,EAAE,OAAOA,CAAC,CAACM,WAAW,EAAE;MACjD,OAAON,CAAC;IACV,CAAC;IAED,IAAI;MACF,MAAMO,MAAM,GAAG,CAAC,GAAG9C,IAAI,CAAC,CAACjF,IAAI,CAAC,CAACgI,CAAC,EAAEC,CAAC,KAAI;QACrC,MAAMC,EAAE,GAAGZ,MAAM,CAACU,CAAC,CAAC;QACpB,MAAMG,EAAE,GAAGb,MAAM,CAACW,CAAC,CAAC;QACpB,IAAIC,EAAE,IAAI,IAAI,IAAIC,EAAE,IAAI,IAAI,EAAE,OAAO,CAAC;QACtC,IAAID,EAAE,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC,GAAGhI,GAAG;QAC/B,IAAIiI,EAAE,IAAI,IAAI,EAAE,OAAO,CAAC,GAAGjI,GAAG;QAC9B,IAAIgI,EAAE,GAAGC,EAAE,EAAE,OAAO,CAAC,GAAGjI,GAAG;QAC3B,IAAIgI,EAAE,GAAGC,EAAE,EAAE,OAAO,CAAC,CAAC,GAAGjI,GAAG;QAC5B,OAAO,CAAC;MACV,CAAC,CAAC;MACF,OAAO6H,MAAM;IACf,CAAC,CAAC,MAAM;MACN,OAAO9C,IAAI;IACb;EACF;EAEOmD,iBAAiBA,CAACtC,KAAU;IACjC,IAAI,CAACpE,aAAa,GAAGoE,KAAK,CAACuC,YAAY,IAAI,EAAE;IAC7C,IAAI,CAAC1G,aAAa,GAAG,IAAI,CAACD,aAAa,CAAC0D,MAAM,KAAK,IAAI,CAACxG,iBAAiB,CAACwG,MAAM;EAClF;EAEA;EACO9M,eAAeA,CAACwN,KAAU;IAC/B,IAAIA,KAAK,CAACwC,GAAG,KAAK,OAAO,EAAE;MACzB,IAAI,CAACtJ,WAAW,CAAC0F,IAAI,CAAC,IAAI,CAACxM,UAAU,CAAC;IACxC;EACF;EAEA;EACAqQ,aAAaA,CAAA;IACX;IACArF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACjL,UAAU,CAAC;IACrD,IAAI,CAAC8G,WAAW,CAAC0F,IAAI,CAAC,IAAI,CAACxM,UAAU,CAAC;EACxC;EAEA;EACAM,cAAcA,CAAA;IACZ;IACA0K,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACjL,UAAU,CAAC;IACrD,IAAI,CAAC8G,WAAW,CAAC0F,IAAI,CAAC,IAAI,CAACxM,UAAU,CAAC;EACxC;EAEOsQ,WAAWA,CAAA;IAChB;IACA,IAAI,CAACtQ,UAAU,GAAG,EAAE;IACpB;IACA,IAAI,CAAC6F,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACgB,WAAW,CAAC0F,IAAI,CAAC,EAAE,CAAC;EAC3B;EAEA;EACO+D,qBAAqBA,CAAA;IAC1B,IAAI,CAACnO,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEON,oBAAoBA,CAAA;IACzB,IAAI,CAACT,IAAI,CAACoG,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAACyE,0BAA0B,EAAE;EACnC;EAEOtK,eAAeA,CAAA;IACpB,IAAI,CAACL,cAAc,GAAG;MAAEC,MAAM,EAAE;IAAI,CAAE;IACtC,IAAI,CAACwF,aAAa,GAAG,EAAE;IACvB,IAAI,CAACJ,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAAC7F,IAAI,CAACoG,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAACyE,0BAA0B,EAAE;EACnC;EAEA;EACO3L,YAAYA,CAAA;IACjB;IACA,MAAM6P,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,iBAAiB,CAAgB;IAC9E,IAAIF,aAAa,EAAE;MACjBA,aAAa,CAACG,SAAS,CAACC,MAAM,CAAC,iBAAiB,CAAC;MACjD,IAAI,CAACpP,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;MAClC;MACA,IAAI,IAAI,CAACiF,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACoK,OAAO,EAAE;MACrB;IACF;EACF;EAEA;EACOC,aAAaA,CAAClD,KAAkC;IACrD,QAAQA,KAAK,CAACyB,IAAI,CAAC9H,KAAK;MACtB,KAAK,KAAK;QACR,IAAI,CAACwJ,SAAS,EAAE;QAChB;MACF,KAAK,UAAU;QACb,IAAI,IAAI,CAACvH,aAAa,CAAC0D,MAAM,GAAG,CAAC,EAAE;UACjC,IAAI,CAAC8D,aAAa,CAAC,IAAI,CAACxH,aAAa,CAAC;QACxC,CAAC,MAAM;UACL,IAAI,CAACnD,iBAAiB,CAAC4K,SAAS,CAAC,+BAA+B,EAAE,EAAE,CAAC;QACvE;QACA;MACF,KAAK,UAAU;QACb,IAAI,CAACD,aAAa,CAAC,IAAI,CAACtK,iBAAiB,CAAC;QAC1C;MACF;QACE,IAAI,CAACqK,SAAS,EAAE;IACpB;EACF;EAEA;EACA;;;EAGAG,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAACrK,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACgG,MAAM,EAAE;MAC7C7B,OAAO,CAACmC,KAAK,CAAC,0BAA0B,EAAE,IAAI,CAACtG,SAAS,CAAC;MACzD,IAAI,CAACR,iBAAiB,CAAC4K,SAAS,CAAC,8CAA8C,EAAE,EAAE,CAAC;MACpF;IACF;IAEA,MAAME,gBAAgB,GAAU,EAAE;IAClC,MAAMC,aAAa,GAAU,EAAE;IAE/B,IAAI,IAAI,CAAC3K,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC4K,OAAO,EAAE;MAClC,IAAI,CAAC5K,IAAI,CAAC4K,OAAO,CAACC,OAAO,CAAEC,MAAW,IAAI;QACxC,IAAI,CAACA,MAAM,CAACC,MAAM,EAAE;UAClB,MAAMtI,UAAU,GAAG;YACjBN,KAAK,EAAE2I,MAAM,CAAC3I,KAAK;YACnBb,KAAK,EAAEwJ,MAAM,CAACxJ,KAAK;YACnByJ,MAAM,EAAED,MAAM,CAACC;WAChB;UACDL,gBAAgB,CAACM,IAAI,CAACvI,UAAU,CAAC;QACnC,CAAC,MAAM;UACL,MAAMA,UAAU,GAAG;YACjBN,KAAK,EAAE2I,MAAM,CAAC3I,KAAK;YACnBb,KAAK,EAAEwJ,MAAM,CAACxJ,KAAK;YACnByJ,MAAM,EAAED,MAAM,CAACC;WAChB;UACDJ,aAAa,CAACK,IAAI,CAACvI,UAAU,CAAC;QAChC;MACF,CAAC,CAAC;IACJ;IAEA,MAAMwI,qBAAqB,GAAG,IAAI,CAACpJ,WAAW,CAC3CtB,MAAM,CAAC6E,GAAG,IAAI,CAAC,IAAI,CAAC9I,YAAY,CAACC,QAAQ,CAAC6I,GAAG,CAAC,CAAC,CAC/ChN,GAAG,CAAC,CAACkJ,KAAK,EAAE4J,KAAK,MAAM;MACtB5J,KAAK;MACL6J,UAAU,EAAED;KACb,CAAC,CAAC;IAEL;IACA,MAAME,QAAQ,GAAG;MACfC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,IAAI,CAAClL,SAAS,CAACgG,MAAM;MAC7B3E,UAAU,EAAEkJ,aAAa;MACzBjJ,aAAa,EAAEuJ,qBAAqB;MACpCM,QAAQ,EAAE,IAAI,CAACnL,SAAS,CAACgG;KAC1B;IAED;IACA,IAAI,CAAC1G,eAAe,CAACoG,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,IAAI,CAAChG,kBAAkB,CAACyL,gBAAgB,CAACJ,QAAQ,CAAC,CAACzG,SAAS,CAAC;MAC3DoB,IAAI,EAAG0F,GAAG,IAAI;QACZ,IAAI,CAAC/L,eAAe,CAACoG,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAAC0F,GAAG,CAAClF,OAAO,EAAE;UAChB;UACA,IAAI,CAAC9E,UAAU,GAAGkJ,aAAa;UAC/B,IAAI,CAACjJ,aAAa,GAAGuJ,qBAAqB;UAC1C,IAAI,CAACrJ,YAAY,GAAG,IAAI,CAACH,UAAU,CAACrJ,GAAG,CAAEgN,GAAQ,IAAKA,GAAG,CAAC9D,KAAK,CAAC;UAEhE;UACA,IAAI,CAACvB,kBAAkB,CAAC2L,kBAAkB,CAACN,QAAQ,CAAC;UAEpD,IAAI,CAACxL,iBAAiB,CAAC+L,WAAW,CAACF,GAAG,CAACG,OAAO,IAAI,qCAAqC,EAAE,EAAE,CAAC;QAC9F,CAAC,MAAM;UACL,IAAI,CAAChM,iBAAiB,CAAC4K,SAAS,CAACiB,GAAG,CAACG,OAAO,IAAI,iCAAiC,EAAE,EAAE,CAAC;QACxF;QACA,IAAI,CAACrM,GAAG,CAAC0H,YAAY,EAAE;MACzB,CAAC;MACDP,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChH,eAAe,CAACoG,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/CxB,OAAO,CAACmC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QAErD;QACA,IAAI,CAAC3G,kBAAkB,CAAC2L,kBAAkB,CAACN,QAAQ,CAAC;QAEpD;QACA,IAAI,CAAC3J,UAAU,GAAGkJ,aAAa;QAC/B,IAAI,CAACjJ,aAAa,GAAGuJ,qBAAqB;QAC1C,IAAI,CAACrJ,YAAY,GAAG,IAAI,CAACH,UAAU,CAACrJ,GAAG,CAAEgN,GAAQ,IAAKA,GAAG,CAAC9D,KAAK,CAAC;QAEhE,IAAI,CAAC1B,iBAAiB,CAAC4K,SAAS,CAAC,mDAAmD,EAAE,EAAE,CAAC;QACzF,IAAI,CAACjL,GAAG,CAAC0H,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;EAGA7M,UAAUA,CAAA;IACR;IACA,IAAI,CAAC,IAAI,CAACgG,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACgG,MAAM,EAAE;MAC7C7B,OAAO,CAACmC,KAAK,CAAC,0BAA0B,EAAE,IAAI,CAACtG,SAAS,CAAC;MACzD,IAAI,CAACR,iBAAiB,CAAC4K,SAAS,CAAC,4DAA4D,EAAE,EAAE,CAAC;MAClG;IACF;IAEA;IACA,MAAMqB,KAAK,GAAG,IAAI,CAAClM,UAAU,CAACmM,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC;IACtE,IAAI,CAACD,KAAK,EAAE;MACVtH,OAAO,CAACmC,KAAK,CAAC,gCAAgC,CAAC;MAC/C,IAAI,CAAC9G,iBAAiB,CAAC4K,SAAS,CAAC,qDAAqD,EAAE,EAAE,CAAC;MAC3F;IACF;IAEA;IACA,IAAI,CAACjR,UAAU,GAAG,EAAE;IACpB,IAAI,CAACoH,aAAa,GAAG,EAAE;IACvB,IAAI,CAACJ,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAACW,IAAI,GAAG,CAAC;IACb,IAAI,CAACxG,IAAI,CAACoG,UAAU,GAAG,CAAC;IACxB,IAAI,CAACa,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;IAE3C;IACA,IAAI,CAACT,IAAI,GAAG,CAAC;MAAEC,KAAK,EAAE,iBAAiB;MAAEC,GAAG,EAAE;IAAM,CAAE,CAAC;IACvD,IAAI,CAAC3G,IAAI,CAACsG,OAAO,GAAG,iBAAiB;IACrC,IAAI,CAACtG,IAAI,CAACuG,QAAQ,GAAG,MAAM;IAE3B;IACA,IAAI,CAACjG,cAAc,GAAG;MAAEC,MAAM,EAAE;IAAI,CAAE;IAEtC;IACA,IAAI,CAACQ,mBAAmB,GAAG,KAAK;IAEhC;IACA,IAAI,IAAI,CAACqE,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC4K,OAAO,EAAE;MAClC,IAAI,CAAC5K,IAAI,CAAC4K,OAAO,CAACC,OAAO,CAAEC,MAAW,IAAI;QACxC,MAAMI,KAAK,GAAG,IAAI,CAACrJ,WAAW,CAACkK,OAAO,CAACjB,MAAM,CAACxJ,KAAK,CAAC;QACpD,IAAI4J,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBJ,MAAM,CAACK,UAAU,GAAGD,KAAK;QAC3B;QACA;QACA,IAAIJ,MAAM,CAACxJ,KAAK,IAAIwJ,MAAM,CAACxJ,KAAK,KAAK,QAAQ,EAAE;UAC7CwJ,MAAM,CAACC,MAAM,GAAG,KAAK;QACvB;MACF,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,CAACtJ,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACE,YAAY,GAAG,EAAE;IAEtB;IACA,IAAI,IAAI,CAAC5B,IAAI,EAAE;MACb;MACA,IAAI,CAACA,IAAI,CAACO,MAAM,GAAG;QAAEC,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAE,CAAE;MAEhD;MACA,IAAI,CAACT,IAAI,CAACqB,IAAI,GAAG,CAAC;QAAEC,KAAK,EAAE,iBAAiB;QAAEC,GAAG,EAAE;MAAM,CAAE,CAAC;MAE5D;MACA,IAAI,CAACvB,IAAI,CAACoB,IAAI,GAAG,CAAC;MAClB,IAAI,CAACpB,IAAI,CAACuD,QAAQ,GAAG,IAAI,CAAC3I,IAAI,CAACmG,IAAI;IACrC;IAEA;IACA,MAAMqK,QAAQ,GAAG;MACfC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,IAAI,CAAClL,SAAS,CAACgG,MAAM;MAC7B3E,UAAU,EAAE,EAAE;MACdC,aAAa,EAAE,EAAE;MACjB6J,QAAQ,EAAE,IAAI,CAACnL,SAAS,CAACgG;KAC1B;IAED;IACA,IAAI,CAACrG,kBAAkB,CAACiM,qBAAqB,CAAC,OAAO,CAAC;IAEtD;IACA,IAAI,CAAC5M,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACK,eAAe,CAACoG,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACxG,GAAG,CAAC0M,aAAa,EAAE;IAExB;IACA,IAAI,IAAI,CAACjM,IAAI,EAAE;MACbgF,UAAU,CAAC,MAAK;QACd,IAAI,CAAChF,IAAI,CAACoK,OAAO,EAAE;QACnB,IAAI,CAACpK,IAAI,CAACkM,KAAK,EAAE;MACnB,CAAC,EAAE,GAAG,CAAC;IACT;IAEA,IAAI,CAAChN,SAAS,EAAE;EAClB;EAEA;;;EAGA5E,WAAWA,CAAA;IACT;IACA,IAAI,CAAC8E,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACzE,IAAI,CAACoG,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAACb,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAACC,UAAU,GAAG;MAAEF,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC/C,IAAI,CAACE,aAAa,GAAG,EAAE;IACvB,IAAI,CAACzF,cAAc,GAAG;MAAEC,MAAM,EAAE;IAAI,CAAE;IAEtC;IACA,IAAI,CAAC5B,UAAU,GAAG,EAAE;IAEpB;IACA,IAAI,CAAC2F,SAAS,EAAE;EAClB;EAEA;;;EAGAiN,oBAAoBA,CAACzK,aAAkB;IACrC,IAAI;MACF,MAAM0K,UAAU,GAAG1K,aAAa;MAChC,IAAI0K,UAAU,EAAE;QACd,MAAMC,WAAW,GAAGD,UAAU;QAC9B,IAAI3E,KAAK,CAACC,OAAO,CAAC2E,WAAW,CAAC,IAAIA,WAAW,CAAC5F,MAAM,GAAG,CAAC,EAAE;UACxD;UACA,MAAM6F,qBAAqB,GAAGD,WAAW,CACtChL,IAAI,CAAC,CAACgI,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC8B,UAAU,GAAG7B,CAAC,CAAC6B,UAAU,CAAC,CAC3C/S,GAAG,CAACgN,GAAG,IAAIA,GAAG,CAAC9D,KAAK,CAAC,CACrBf,MAAM,CAACe,KAAK,IAAI,CAAC,IAAI,CAAChF,YAAY,CAACC,QAAQ,CAAC+E,KAAK,CAAC,CAAC;UAEtD;UACA,MAAMiL,cAAc,GAAG,IAAI,CAACxK,gBAAgB,CAACxB,MAAM,CAAC6E,GAAG,IAAI,CAACkH,qBAAqB,CAAC/P,QAAQ,CAAC6I,GAAG,CAAC,CAAC;UAEhG;UACA,IAAI,CAACvD,WAAW,GAAG,CACjB,GAAG,IAAI,CAACvF,YAAY,EACpB,GAAGgQ,qBAAqB,EACxB,GAAGC,cAAc,CAClB;QACH,CAAC,MAAM;UACL,IAAI,CAAC1K,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;QAC7C;MACF,CAAC,MAAM;QACL,IAAI,CAACD,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;MAC7C;IACF,CAAC,CAAC,OAAO4E,KAAK,EAAE;MACd,IAAI,CAAC7E,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;IAC7C;EACF;EAEA;;;EAGArF,cAAcA,CAAC+P,UAAe;IAC5B,OAAO,IAAI,CAAC5K,YAAY,CAACmK,OAAO,CAACS,UAAU,CAAC,GAAG,CAAC,CAAC;EACnD;EAEA;;;EAGAC,eAAeA,CAACtF,KAAU;IACxB,MAAM;MAAEyD,OAAO;MAAE8B,QAAQ;MAAEC;IAAQ,CAAE,GAAGxF,KAAK;IAE7C;IACA,IAAI,IAAI,CAAC7K,YAAY,CAACC,QAAQ,CAACqO,OAAO,CAAC+B,QAAQ,CAAC,CAACrL,KAAK,CAAC,IAAI,IAAI,CAAChF,YAAY,CAACC,QAAQ,CAACqO,OAAO,CAAC8B,QAAQ,CAAC,CAACpL,KAAK,CAAC,EAAE;MAC9G;IACF;IAEA;IACA,MAAMsL,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC/K,WAAW,CAAC;IAC9C,MAAM,CAACgL,WAAW,CAAC,GAAGD,gBAAgB,CAACE,MAAM,CAACH,QAAQ,EAAE,CAAC,CAAC;IAC1DC,gBAAgB,CAACE,MAAM,CAACJ,QAAQ,EAAE,CAAC,EAAEG,WAAW,CAAC;IAEjD,IAAI,CAAChL,WAAW,GAAG+K,gBAAgB;IACnC,IAAI,CAACrN,GAAG,CAAC0H,YAAY,EAAE;EACzB;EAEA;;;EAGA8F,sBAAsBA,CAAC5F,KAAU;IAC/B,MAAM;MAAE2D,MAAM;MAAEC;IAAM,CAAE,GAAG5D,KAAK;IAEhC;IACA,MAAM6F,aAAa,GAAG,IAAI,CAACvL,UAAU,CAACwL,SAAS,CAAErE,IAAS,IAAKA,IAAI,CAACtH,KAAK,KAAKwJ,MAAM,CAACxJ,KAAK,CAAC;IAE3F,IAAIyJ,MAAM,IAAIiC,aAAa,KAAK,CAAC,CAAC,EAAE;MAClC;MACA,IAAI,CAACvL,UAAU,CAACuJ,IAAI,CAAC;QACnB7I,KAAK,EAAE2I,MAAM,CAAC3I,KAAK;QACnBb,KAAK,EAAEwJ,MAAM,CAACxJ,KAAK;QACnByJ,MAAM,EAAE;OACT,CAAC;IACJ,CAAC,MAAM,IAAI,CAACA,MAAM,IAAIiC,aAAa,KAAK,CAAC,CAAC,EAAE;MAC1C;MACA,IAAI,CAACvL,UAAU,CAACqL,MAAM,CAACE,aAAa,EAAE,CAAC,CAAC;IAC1C;IAEA;IACA,IAAI,CAACpL,YAAY,GAAG,IAAI,CAACH,UAAU,CAACrJ,GAAG,CAAEgN,GAAQ,IAAKA,GAAG,CAAC9D,KAAK,CAAC;IAEhE,IAAI,CAAC/B,GAAG,CAAC0H,YAAY,EAAE;EACzB;EAEA;;;EAGQhC,4BAA4BA,CAAA;IAClC,IAAI;MACF;MACA,IAAI,IAAI,CAAC7E,SAAS,IAAI,IAAI,CAACA,SAAS,CAACgG,MAAM,EAAE;QAC3C,IAAI,CAACrG,kBAAkB,CAACmN,aAAa,CAAC;UACpC7B,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE,IAAI,CAAClL,SAAS,CAACgG;SACxB,CAAC,CAACzB,SAAS,CAAC;UACXoB,IAAI,EAAG0F,GAAG,IAAI;YACZ,IAAI,CAACA,GAAG,CAAClF,OAAO,IAAIkF,GAAG,CAAC0B,IAAI,EAAE;cAC5B,IAAI,CAAC3L,SAAS,GAAGiK,GAAG,CAAC0B,IAAI;cACzB,IAAI,CAAC1L,UAAU,GAAGgK,GAAG,CAAC0B,IAAI,CAACC,QAAQ,GAAGC,IAAI,CAACtE,KAAK,CAAC0C,GAAG,CAAC0B,IAAI,CAACC,QAAQ,CAAC,GAAG,EAAE;cACxE,IAAI,CAACzL,iBAAiB,GAAG8J,GAAG,CAAC0B,IAAI,CAACzL,aAAa,GAAG2L,IAAI,CAACtE,KAAK,CAAC0C,GAAG,CAAC0B,IAAI,CAACzL,aAAa,CAAC,GAAG,EAAE;cACzF,IAAI,CAACE,YAAY,GAAG,IAAI,CAACH,UAAU,CAACrJ,GAAG,CAAEgN,GAAQ,IAAKA,GAAG,CAAC9D,KAAK,CAAC;cAEhE;cACA,IAAI,IAAI,CAACtB,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC4K,OAAO,EAAE;gBAClC,IAAI,CAAC5K,IAAI,CAAC4K,OAAO,CAACC,OAAO,CAAEC,MAAW,IAAI;kBACxC,IAAI,IAAI,CAACrJ,UAAU,CAAC6L,IAAI,CAAE1E,IAAS,IAAKA,IAAI,CAACzG,KAAK,KAAK2I,MAAM,CAAC3I,KAAK,IAAIyG,IAAI,CAACmC,MAAM,CAAC,EAAE;oBACnFD,MAAM,CAACyC,gBAAgB,GAAG,IAAI;oBAC9BzC,MAAM,CAACC,MAAM,GAAG,IAAI;kBACtB,CAAC,MAAM;oBACLD,MAAM,CAACC,MAAM,GAAG,KAAK;kBACvB;gBACF,CAAC,CAAC;cACJ;cAEA;cACA,IAAI,CAACoB,oBAAoB,CAAC,IAAI,CAACxK,iBAAiB,CAAC;cAEjD;cACA,IAAI,CAAC5B,kBAAkB,CAAC2L,kBAAkB,CAAC;gBACzCL,QAAQ,EAAE,OAAO;gBACjBC,MAAM,EAAE,IAAI,CAAClL,SAAS,CAACgG,MAAM;gBAC7B3E,UAAU,EAAE,IAAI,CAACA,UAAU;gBAC3BC,aAAa,EAAE,IAAI,CAACC;eACrB,CAAC;YACJ;UACF,CAAC;UACD+E,KAAK,EAAGA,KAAK,IAAI;YACfnC,OAAO,CAACmC,KAAK,CAAC,2DAA2D,EAAEA,KAAK,CAAC;YACjF,IAAI,CAAC8G,4BAA4B,EAAE;UACrC;SACD,CAAC;MACJ,CAAC,MAAM;QACL;QACA,IAAI,CAACA,4BAA4B,EAAE;MACrC;IACF,CAAC,CAAC,OAAO9G,KAAK,EAAE;MACdnC,OAAO,CAACmC,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,IAAI,CAAC8G,4BAA4B,EAAE;IACrC;EACF;EAEA;;;EAGQA,4BAA4BA,CAAA;IAClC,IAAI;MACF,MAAMC,WAAW,GAAG,IAAI,CAAC1N,kBAAkB,CAAC2N,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACtN,SAAS,EAAEuN,MAAM,IAAI,CAAC,CAAC;MACrG,IAAIF,WAAW,EAAE;QACf,IAAI,CAACjM,SAAS,GAAGiM,WAAW;QAC5B,IAAI,CAAChM,UAAU,GAAGgM,WAAW,CAAChM,UAAU,IAAI,EAAE;QAC9C,IAAI,CAACE,iBAAiB,GAAG8L,WAAW,CAAC/L,aAAa,IAAI,EAAE;QACxD,IAAI,CAACE,YAAY,GAAG,IAAI,CAACH,UAAU,CAACrJ,GAAG,CAAEgN,GAAQ,IAAKA,GAAG,CAAC9D,KAAK,CAAC;QAEhE;QACA,IAAI,IAAI,CAACtB,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC4K,OAAO,EAAE;UAClC,IAAI,CAAC5K,IAAI,CAAC4K,OAAO,CAACC,OAAO,CAAEC,MAAW,IAAI;YACxC,IAAI,IAAI,CAACrJ,UAAU,CAAC6L,IAAI,CAAE1E,IAAS,IAAKA,IAAI,CAACzG,KAAK,KAAK2I,MAAM,CAAC3I,KAAK,IAAIyG,IAAI,CAACmC,MAAM,CAAC,EAAE;cACnFD,MAAM,CAACyC,gBAAgB,GAAG,IAAI;cAC9BzC,MAAM,CAACC,MAAM,GAAG,IAAI;YACtB,CAAC,MAAM;cACLD,MAAM,CAACC,MAAM,GAAG,KAAK;YACvB;UACF,CAAC,CAAC;QACJ;QAEA;QACA,IAAI,CAACoB,oBAAoB,CAAC,IAAI,CAACxK,iBAAiB,CAAC;MACnD;IACF,CAAC,CAAC,OAAO+E,KAAK,EAAE;MACdnC,OAAO,CAACmC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;IACnE;EACF;EACA;EACAzJ,gBAAgBA,CAAC2Q,UAAc;IAC7B,IAAIC,SAAS,GAAOR,IAAI,CAACtE,KAAK,CAAC6E,UAAU,CAAC;IAC1C,IAAIE,QAAQ,GAAE,EAAE;IAEhB3V,IAAI,CAAC0V,SAAS,EAAE,CAACE,CAAK,EAAEC,CAAK,KAAG;MAC9B9V,CAAC,CAAC2S,OAAO,CAACkD,CAAC,EAAE,UAAUjN,KAAK,EAAE6I,GAAG;QACjC,IAAIsE,MAAM,GAAG,EAAE;QACfnN,KAAK,CAACiL,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAC,EAAE,GAACkC,MAAM,CAACjD,IAAI,CAAC,MAAM,CAAC;QACnDlK,KAAK,CAACiL,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAC,EAAE,GAACkC,MAAM,CAACjD,IAAI,CAAC,OAAO,CAAC;QACrDlK,KAAK,CAACiL,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAC,EAAE,GAACkC,MAAM,CAACjD,IAAI,CAAC,QAAQ,CAAC;QACrD,IAAIkD,MAAM,GAAEpN,KAAK,CAAC2F,MAAM,GAAC,CAAC,GAACwH,MAAM,CAACE,IAAI,CAAC,IAAI,CAAC,GAAC,MAAM;QACnDL,QAAQ,IAAG,oDAAoD,GAACnE,GAAG,GAAC,KAAK,GAAEuE,MAAM,GAAC,SAAS;MAC7F,CAAC,CAAC;IAEJ,CAAC,CAAC;IACF,OAAOJ,QAAQ;EACjB;EAEA;EACA5H,MAAMA,CAACiB,KAAU;IACf,IAAI,CAACvM,IAAI,CAACoG,UAAU,GAAG,CAAC;IACxB,IAAI,CAAC9B,SAAS,EAAE;EAClB;EACC;EACDkP,mBAAmBA,CAAA;IACjB,IAAI7N,MAAM,GAAQ,EAAE;IACpB,IAAI8N,UAAe;IACnB,IAAG,IAAI,CAAC9U,UAAU,KAAK,IAAI,EAAC;MAC1B8U,UAAU,GAAE,GAAG;IACjB,CAAC,MAAI;MACHA,UAAU,GAAE,IAAI,CAAC9U,UAAU;IAC7B;IACAgH,MAAM,CAAC+N,QAAQ,GAAG,IAAI;IACtB/N,MAAM,CAACgO,QAAQ,GAAG,IAAI,CAACtK,WAAW;IAClC1D,MAAM,CAAC2F,MAAM,GAAGmI,UAAU,CAACG,IAAI,EAAE;IACjC,OAAOjO,MAAM;EACf;EAEA;EACAkO,iBAAiBA,CAACtH,KAAU;IAC1B,IAAI,CAACvM,IAAI,CAACoG,UAAU,GAAGmG,KAAK,GAAG,CAAC;IAChC,IAAI,CAACjI,SAAS,EAAE;EAClB;EAEA;EACA;EACAwP,WAAWA,CAACC,OAAe,EAAEC,QAAgB;IAC3C,IAAI,CAAChL,YAAY,GAAGgL,QAAQ,KAAK,MAAM,GAAG,KAAK,GAAG,MAAM;IACxD,IAAIC,WAAW,GAAG,IAAI,CAACpM,UAAU,CAACwK,SAAS,CAAC6B,EAAE,IAAIA,EAAE,CAACnM,IAAI,KAAKgM,OAAO,CAAC;IACtE,IAAI,CAAClM,UAAU,CAACoM,WAAW,CAAC,CAACtM,KAAK,GAAG,IAAI,CAACqB,YAAY;IACtD,IAAI,CAACrE,GAAG,CAAC0H,YAAY,EAAE;IACvB,IAAI,CAACpD,cAAc,GAAG8K,OAAO;IAC7B,IAAI,CAAC/T,IAAI,CAACuG,QAAQ,GAAG,IAAI,CAACyC,YAAY;IACtC,IAAI,CAAChJ,IAAI,CAACsG,OAAO,GAAGyN,OAAO;IAC3B,IAAI,CAACzP,SAAS,EAAE;EAClB;EAEA;EACA6P,SAASA,CAACC,GAAO;IACf,IAAI,CAACzL,QAAQ,GAAG0L,MAAM,CAACD,GAAG,CAAC;IAC3B,IAAI,CAACpU,IAAI,CAACoG,UAAU,GAAG,CAAC;IACxB,IAAI,IAAI,CAACf,iBAAiB,EAAE,IAAI,CAACrF,IAAI,CAACmG,IAAI,GAAGkO,MAAM,CAACD,GAAG,CAAC;IACxD,IAAI,CAAC9P,SAAS,EAAE;EAClB;EAGD;EACClF,MAAMA,CAAA;IACJ,IAAI,CAACgC,IAAI,CAAC,CAAC,CAAC;EACd;EACA;EACAA,IAAIA,CAACkT,EAAU;IACb,IAAIC,eAAe,GAAQ;MACzBpO,IAAI,EAAE,IAAI;MAAEqO,QAAQ,EAAE,QAAQ;MAC9BC,QAAQ,EAAE,KAAK;MAAEC,UAAU,EAAE;KAC9B;IACD,MAAMC,QAAQ,GAAG,IAAI,CAAC/P,YAAY,CAACgQ,IAAI,CAAC9W,iBAAiB,EAAEyW,eAAe,CAAC;IAC3EI,QAAQ,CAACE,iBAAiB,CAACP,EAAE,GAAGA,EAAE;IAClCK,QAAQ,CAACE,iBAAiB,CAAClK,WAAW,GAAG,IAAI,CAACvB,eAAe;IAC7D;IACAuL,QAAQ,CAACE,iBAAiB,CAACC,SAAS,CAAC/K,SAAS,CAAEgL,aAAkB,IAAI;MACpE,IAAIA,aAAa,IAAI,IAAI,EAAE;QACzB,IAAI,CAACzQ,SAAS,EAAE;MAClB;IACF,CAAC,CAAC;EACJ;EACC;EACDqL,aAAaA,CAACqF,OAAY;IACxB,IAAIA,OAAO,KAAKtI,SAAS,EAAE;MACzB,IAAIsI,OAAO,CAACnJ,MAAM,GAAG,CAAC,EAAE;QACtB;QACA,MAAMoJ,UAAU,GAAG,OAAO;QAC1B,MAAMC,WAAW,GAAG,CAClB,MAAM,EACN,aAAa,EACb,QAAQ,EACR,aAAa,EACb,cAAc,EACb,cAAc,EACd,iBAAiB,EACjB,eAAe,EACf,eAAe,EACf,kBAAkB,CACpB;QAED;QACA,MAAMC,UAAU,GAAQ,EAAE;QAC1B5X,IAAI,CAACyX,OAAO,EAAGA,OAAY,IAAI;UAC7B,MAAMI,QAAQ,GAAG,EAAE;UACjBA,QAAQ,CAAChF,IAAI,CAAE4E,OAAO,CAACK,IAAI,CAAC;UAC5BD,QAAQ,CAAChF,IAAI,CAAC,IAAI,CAACkF,qBAAqB,CAACN,OAAO,CAACO,WAAW,CAAC,CAAC;UAC9DH,QAAQ,CAAChF,IAAI,CAAE4E,OAAO,CAACQ,MAAM,CAAC;UAC9BJ,QAAQ,CAAChF,IAAI,CAAE4E,OAAO,CAACS,WAAW,CAAC;UACnCL,QAAQ,CAAChF,IAAI,CAAC,IAAI,CAACrL,UAAU,CAAC2Q,QAAQ,CAACV,OAAO,CAACW,WAAW,CAAC,CAAC;UAC5DP,QAAQ,CAAChF,IAAI,CAAC,IAAI,CAACrL,UAAU,CAAC6Q,QAAQ,CAACZ,OAAO,CAACW,WAAW,CAAC,CAAC;UAC5DP,QAAQ,CAAChF,IAAI,CAAE4E,OAAO,CAACa,mBAAmB,CAAC;UAC3CT,QAAQ,CAAChF,IAAI,CAAC,IAAI,CAACrL,UAAU,CAAC2Q,QAAQ,CAACV,OAAO,CAACc,eAAe,CAAC,CAAC;UAChEV,QAAQ,CAAChF,IAAI,CAAC,IAAI,CAACrL,UAAU,CAAC6Q,QAAQ,CAACZ,OAAO,CAACc,eAAe,CAAC,CAAC;UAChEV,QAAQ,CAAChF,IAAI,CAAE4E,OAAO,CAACe,uBAAuB,CAAC;UAC/CZ,UAAU,CAAC/E,IAAI,CAACgF,QAAQ,CAAC;QAC7B,CAAC,CACA;QACD;QACA,MAAMY,OAAO,GAAG,CACd;UACE1B,EAAE,EAAE,CAAC;UACL9M,KAAK,EAAE;SACR,EAAE;UACD8M,EAAE,EAAE,CAAC;UACL9M,KAAK,EAAE;SACR,EAAE;UACD8M,EAAE,EAAE,CAAC;UACL9M,KAAK,EAAE;SACR,EAAE;UACD8M,EAAE,EAAE,CAAC;UACL9M,KAAK,EAAE;SACR,EAAE;UACD8M,EAAE,EAAE,CAAC;UACL9M,KAAK,EAAE;SACR,EAAE;UACD8M,EAAE,EAAE,CAAC;UACL9M,KAAK,EAAE;SACR,EAAE;UACD8M,EAAE,EAAE,CAAC;UACL9M,KAAK,EAAE;SACR,EAAE;UACD8M,EAAE,EAAE,CAAC;UACL9M,KAAK,EAAE;SACR,EAAE;UACD8M,EAAE,EAAE,CAAC;UACL9M,KAAK,EAAE;SACR,EAAE;UACD8M,EAAE,EAAE,EAAE;UACN9M,KAAK,EAAE;SACR,CAAE;QACL,IAAI,CAAC7C,GAAG,CAAC0H,YAAY,EAAE;QACvB,IAAI,CAACxH,cAAc,CAACoR,aAAa,CAAChB,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAEa,OAAO,CAAC;MACjF,CAAC,MAAM;QACL,IAAI,CAAChR,iBAAiB,CAAC4K,SAAS,CAAC,sCAAsC,EAAE,EAAE,CAAC;MAC9E;IACF,CAAC,MAAM;MACL,IAAI,CAAC5K,iBAAiB,CAAC4K,SAAS,CAAC,sCAAsC,EAAE,EAAE,CAAC;IAC9E;EACF;EACA;EACAF,SAASA,CAAA;IACP,MAAMwG,WAAW,GAAG;MAClBxC,QAAQ,EAAE;KACX;IACD,IAAI,CAAC5O,eAAe,CAACoG,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAAClG,WAAW,CAACkR,uBAAuB,CAACD,WAAW,CAAC,CAACpM,IAAI,CACxDtM,GAAG,CAAEkO,IAAS,IAAKA,IAAW,CAAC,CAAC,CAAC3B,SAAS,CAAE2B,IAAQ,IAAI;MACtD,IAAI,CAACA,IAAI,CAACC,OAAO,EAAE;QACjB,IAAI,CAACgE,aAAa,CAACjE,IAAI,CAACd,YAAY,CAAC;QACrC,IAAI,CAACjG,GAAG,CAAC0H,YAAY,EAAE;QACvB,IAAI,CAACvH,eAAe,CAACoG,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MACjD,CAAC,MAAM;QACL,IAAI,CAACrG,eAAe,CAACoG,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MACjD;IACF,CAAC,CAAC;EAEN;EACA;EACAiL,WAAWA,CAACC,IAAQ;IAClB,IAAI9B,eAAe,GAAQ;MACzBpO,IAAI,EAAE,IAAI;MAAEqO,QAAQ,EAAE,QAAQ;MAC9BC,QAAQ,EAAE,KAAK;MAAEC,UAAU,EAAE;KAC9B;IACD,MAAMC,QAAQ,GAAG,IAAI,CAAC/P,YAAY,CAACgQ,IAAI,CAAC/W,2BAA2B,EAAE0W,eAAe,CAAC;IACrFI,QAAQ,CAACE,iBAAiB,CAACP,EAAE,GAAG+B,IAAI,CAAChV,MAAM;IAC3CsT,QAAQ,CAACE,iBAAiB,CAACyB,SAAS,GAAG,IAAI;IAC3C3B,QAAQ,CAACE,iBAAiB,CAAC0B,WAAW,GAAG,mCAAmC;IAC5E5B,QAAQ,CAACE,iBAAiB,CAAC2B,gBAAgB,GAAG,KAAK;IACnD7B,QAAQ,CAACE,iBAAiB,CAAC4B,gBAAgB,GAAG,QAAQ;IACtD9B,QAAQ,CAACE,iBAAiB,CAACtN,KAAK,GAAG,gBAAgB,GAAE8O,IAAI,CAACrU,QAAQ;IAElE2S,QAAQ,CAACE,iBAAiB,CAACC,SAAS,CAAC/K,SAAS,CAAEgL,aAAkB,IAAI;MACpEpL,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAACmL,aAAa,CAAC;MAC3C,IAAIA,aAAa,CAAC2B,OAAO,IAAI,IAAI,EAAE;QACjC,IAAIC,OAAO,GAAG;UACZtV,MAAM,EAAEgV,IAAI,CAAChV,MAAM;UACnBkK,cAAc,EAAC,IAAI,CAAC/F,SAAS,CAACgG;SAC/B;QACD,IAAI,CAAC1G,eAAe,CAACoG,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;QAC9C,IAAI,CAAClG,WAAW,CAAC2R,UAAU,CAACD,OAAO,CAAC,CAAC5M,SAAS,CAAE2B,IAAS,IAAI;UAC3D,IAAI,CAACA,IAAI,CAACC,OAAO,EAAE;YACjB,IAAI,CAAC7G,eAAe,CAACoG,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;YAC/C,IAAI,CAACnG,iBAAiB,CAAC+L,WAAW,CAACrF,IAAI,CAACd,YAAY,CAACoG,OAAO,EAAE,EAAE,CAAC;YACjE,IAAI,CAACvH,QAAQ,EAAE;UACjB;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EACA;EACA6L,qBAAqBA,CAACtC,UAAc;IAClC,IAAIC,SAAS,GAAOR,IAAI,CAACtE,KAAK,CAAC6E,UAAU,CAAC;IAC1C,IAAIE,QAAQ,GAAE,EAAE;IAEhB3V,IAAI,CAAC0V,SAAS,EAAE,CAACE,CAAK,EAAEC,CAAK,KAAG;MAC9B9V,CAAC,CAAC2S,OAAO,CAACkD,CAAC,EAAE,UAAUjN,KAAK,EAAE6I,GAAG;QACjC,IAAIsE,MAAM,GAAG,EAAE;QACfnN,KAAK,CAACiL,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAC,EAAE,GAACkC,MAAM,CAACjD,IAAI,CAAC,MAAM,CAAC;QACnDlK,KAAK,CAACiL,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAC,EAAE,GAACkC,MAAM,CAACjD,IAAI,CAAC,OAAO,CAAC;QACrDlK,KAAK,CAACiL,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAC,EAAE,GAACkC,MAAM,CAACjD,IAAI,CAAC,QAAQ,CAAC;QACrD,IAAIkD,MAAM,GAAEpN,KAAK,CAAC2F,MAAM,GAAC,CAAC,GAACwH,MAAM,CAACE,IAAI,CAAC,IAAI,CAAC,GAAC,MAAM;QACnDL,QAAQ,IAAGnE,GAAG,GAAC,KAAK,GAAEuE,MAAM,GAAC,IAAI;MAEnC,CAAC,CAAC;IAEJ,CAAC,CAAC;IACF,OAAOJ,QAAQ;EACjB;;qCA3qCWxO,iBAAiB,EAAA3G,EAAA,CAAA8Y,iBAAA,CAAA9Y,EAAA,CAAA+Y,iBAAA,GAAA/Y,EAAA,CAAA8Y,iBAAA,CAAAE,EAAA,CAAAC,QAAA,GAAAjZ,EAAA,CAAA8Y,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAnZ,EAAA,CAAA8Y,iBAAA,CAAAM,EAAA,CAAAC,gBAAA,GAAArZ,EAAA,CAAA8Y,iBAAA,CAAAQ,EAAA,CAAAtS,UAAA,GAAAhH,EAAA,CAAA8Y,iBAAA,CAAAS,EAAA,CAAAC,wBAAA,GAAAxZ,EAAA,CAAA8Y,iBAAA,CAAAW,EAAA,CAAAvS,WAAA,GAAAlH,EAAA,CAAA8Y,iBAAA,CAAAY,EAAA,CAAAC,WAAA,GAAA3Z,EAAA,CAAA8Y,iBAAA,CAAAc,EAAA,CAAAC,kBAAA;EAAA;;UAAjBlT,iBAAiB;IAAAmT,SAAA;IAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;QC/C9Bja,EAAA,CAAA8C,UAAA,IAAAqX,gCAAA,iBAAqE;QAUnEna,EADF,CAAAC,cAAA,aAA4B,uBA6BzB;QADCD,EAZA,CAAAc,UAAA,2BAAAsZ,+DAAA9Z,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA8Z,GAAA;UAAA,OAAAra,EAAA,CAAAa,WAAA,CAAiBqZ,GAAA,CAAApG,eAAA,CAAAxT,MAAA,CAAuB;QAAA,EAAC,6BAAAga,iEAAAha,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA8Z,GAAA;UAAA,OAAAra,EAAA,CAAAa,WAAA,CACtBqZ,GAAA,CAAApJ,iBAAA,CAAAxQ,MAAA,CAAyB;QAAA,EAAC,0BAAAia,8DAAAja,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA8Z,GAAA;UAAA,OAAAra,EAAA,CAAAa,WAAA,CAQ7BqZ,GAAA,CAAArL,YAAA,CAAAvO,MAAA,CAAoB;QAAA,EAAC,wBAAAka,4DAAAla,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA8Z,GAAA;UAAA,OAAAra,EAAA,CAAAa,WAAA,CACvBqZ,GAAA,CAAA3L,UAAA,CAAAjO,MAAA,CAAkB;QAAA,EAAC,wBAAAma,4DAAAna,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA8Z,GAAA;UAAA,OAAAra,EAAA,CAAAa,WAAA,CACnBqZ,GAAA,CAAAzL,YAAA,CAAAnO,MAAA,CAAoB;QAAA,EAAC,oCAAAoa,wEAAApa,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA8Z,GAAA;UAAA,OAAAra,EAAA,CAAAa,WAAA,CACTqZ,GAAA,CAAA9F,sBAAA,CAAA9T,MAAA,CAA8B;QAAA,EAAC;QA6TzDN,EA3TA,CAAA8C,UAAA,IAAA6X,wCAAA,2BAAsC,IAAAC,wCAAA,yBAyFA,IAAAC,yCAAA,0BAqCW,IAAAC,wCAAA,yBA6LT;QAe5C9a,EADE,CAAAG,YAAA,EAAa,EACT;;;QAjXAH,EAAA,CAAA+B,UAAA,SAAAmY,GAAA,CAAAzT,OAAA,IAAAyT,GAAA,CAAAxT,SAAA,CAA0B;QAY5B1G,EAAA,CAAA4B,SAAA,GAA0B;QAqB1B5B,EArBA,CAAA+B,UAAA,SAAAmY,GAAA,CAAA5S,iBAAA,CAA0B,aAAA4S,GAAA,CAAAjY,IAAA,CAAAmG,IAAA,CACJ,SAAA8R,GAAA,CAAAxR,IAAA,CACT,aAAA1I,EAAA,CAAA+a,eAAA,KAAAC,GAAA,EAAAhb,EAAA,CAAAyD,eAAA,KAAAwX,GAAA,GAOX,aAAAjb,EAAA,CAAAyD,eAAA,KAAAyX,GAAA,EACgD,oBAC/B,eAAAlb,EAAA,CAAAyD,eAAA,KAAA0X,GAAA,EACoC,qBAGnC,oBAED,eACL,SAAAjB,GAAA,CAAAzR,IAAA,CACD,WAAAyR,GAAA,CAAAtS,MAAA,CACI,eAAA5H,EAAA,CAAAyD,eAAA,KAAA2X,GAAA,EACc;QAoIEpb,EAAA,CAAA4B,SAAA,GAAc;QAAd5B,EAAA,CAAA+B,UAAA,YAAAmY,GAAA,CAAAhR,WAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}