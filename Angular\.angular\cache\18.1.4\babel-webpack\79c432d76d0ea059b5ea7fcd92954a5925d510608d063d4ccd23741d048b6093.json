{"ast": null, "code": "import { Subscription, combineLatest } from 'rxjs';\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/app.service\";\nimport * as i3 from \"../../services/projects.service\";\nimport * as i4 from \"../../services/permits.service\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction ProjectViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"span\", 6);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 7);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectViewComponent_div_2_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editProject());\n    });\n    i0.ɵɵelement(1, \"i\", 27);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 28)(2, \"div\", 29)(3, \"div\", 30)(4, \"label\");\n    i0.ɵɵtext(5, \"Start date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 31);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 30)(10, \"label\");\n    i0.ɵɵtext(11, \"End date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 31);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 32)(16, \"label\");\n    i0.ɵɵtext(17, \"Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 31);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 30)(21, \"label\");\n    i0.ɵɵtext(22, \"External project manager\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 31);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 32)(26, \"label\");\n    i0.ɵɵtext(27, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\", 31);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectStartDate ? i0.ɵɵpipeBind2(8, 5, ctx_r1.project.projectStartDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectEndDate ? i0.ɵɵpipeBind2(14, 8, ctx_r1.project.projectEndDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectLocation || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.externalPMNames || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectDescription || \"\");\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵelement(2, \"i\", 37);\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No permits found for this project.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"a\", 42);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template_a_click_2_listener() {\n      const permit_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.viewPermit(permit_r5.permitId));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\")(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\")(10, \"select\", 44);\n    i0.ɵɵlistener(\"change\", function ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template_select_change_10_listener($event) {\n      const permit_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onStatusChange(permit_r5, $event.target.value));\n    });\n    i0.ɵɵelementStart(11, \"option\", 45);\n    i0.ɵɵtext(12, \"Select status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"option\", 46);\n    i0.ɵɵtext(14, \"Approved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"option\", 47);\n    i0.ɵɵtext(16, \"Pacifica Verification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"option\", 48);\n    i0.ɵɵtext(18, \"Dis-Approved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"option\", 49);\n    i0.ɵɵtext(20, \"Pending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"option\", 50);\n    i0.ɵɵtext(22, \"Not Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"option\", 51);\n    i0.ɵɵtext(24, \"In Review\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"option\", 52);\n    i0.ɵɵtext(26, \"1 Cycle Completed\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"td\")(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"td\", 53)(32, \"span\", 54);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const permit_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", permit_r5.permitName || \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", permit_r5.permitReviewType || \"\", \"\\n\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(permit_r5.permitNumber || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", permit_r5.internalReviewStatus || \"\")(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", \"\");\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate(permit_r5.permitAppliedDate ? i0.ɵɵpipeBind2(30, 8, permit_r5.permitAppliedDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(permit_r5.attentionReason || \"\");\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_25_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"table\", 39)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Permit Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Permit #\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Submitted Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 40);\n    i0.ɵɵtext(13, \"Ball in Court\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template, 34, 11, \"tr\", 41);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.projectPermits);\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProjectViewComponent_div_2_ng_container_25_div_1_Template, 5, 0, \"div\", 33)(2, ProjectViewComponent_div_2_ng_container_25_div_2_Template, 16, 1, \"div\", 34);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.projectPermits.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.projectPermits.length > 0);\n  }\n}\nfunction ProjectViewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10)(3, \"div\", 11)(4, \"div\", 12)(5, \"span\", 13);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 14);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 15)(10, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelement(11, \"i\", 17);\n    i0.ɵɵtext(12, \" Back \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 18)(14, \"ul\", 19)(15, \"li\", 20)(16, \"a\", 21);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_a_click_16_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTab(\"details\", $event));\n    });\n    i0.ɵɵtext(17, \" Project details \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"li\", 20)(19, \"a\", 21);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_a_click_19_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTab(\"permits\", $event));\n    });\n    i0.ɵɵtext(20, \" Permits list \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 22);\n    i0.ɵɵtemplate(22, ProjectViewComponent_div_2_button_22_Template, 2, 0, \"button\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 24);\n    i0.ɵɵtemplate(24, ProjectViewComponent_div_2_ng_container_24_Template, 30, 11, \"ng-container\", 25)(25, ProjectViewComponent_div_2_ng_container_25_Template, 3, 2, \"ng-container\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"Project # \", ctx_r1.project.internalProjectNumber || \"\", \" - \", ctx_r1.project.projectName || \"\", \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(ctx_r1.project.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.project.status || \"Current\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c0, ctx_r1.selectedTab === \"details\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c0, ctx_r1.selectedTab === \"permits\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab === \"details\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"details\" && ctx_r1.project);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"permits\");\n  }\n}\nexport class ProjectViewComponent {\n  route;\n  router;\n  cdr;\n  appService;\n  projectsService;\n  permitsService;\n  modalService;\n  projectId = null;\n  project = null;\n  isLoading = false;\n  selectedTab = 'details';\n  projectPermits = [];\n  loginUser = {};\n  routeSubscription = new Subscription();\n  constructor(route, router, cdr, appService, projectsService, permitsService, modalService) {\n    this.route = route;\n    this.router = router;\n    this.cdr = cdr;\n    this.appService = appService;\n    this.projectsService = projectsService;\n    this.permitsService = permitsService;\n    this.modalService = modalService;\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    // Combine route params and query params to handle both together\n    this.routeSubscription = combineLatest([this.route.paramMap, this.route.queryParams]).subscribe(([paramMap, queryParams]) => {\n      const idParam = paramMap.get('id');\n      this.projectId = idParam ? Number(idParam) : null;\n      console.log('Project view - received params:', {\n        projectId: this.projectId,\n        queryParams\n      });\n      // Handle active tab from query params\n      const activeTab = queryParams['activeTab'];\n      if (activeTab && (activeTab === 'permits' || activeTab === 'details')) {\n        this.selectedTab = activeTab;\n        console.log('Setting selectedTab from query params:', activeTab);\n      } else {\n        console.log('No valid activeTab found, keeping default:', this.selectedTab);\n      }\n      if (this.projectId) {\n        this.fetchProjectDetails();\n        this.fetchProjectPermits();\n      }\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnDestroy() {\n    if (this.routeSubscription) {\n      this.routeSubscription.unsubscribe();\n    }\n  }\n  fetchProjectDetails() {\n    if (!this.projectId) {\n      return;\n    }\n    this.isLoading = true;\n    this.projectsService.getProject({\n      projectId: this.projectId\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        console.log('Project API Response:', res);\n        if (!res?.isFault) {\n          // Try different response structures\n          this.project = res.responseData?.Project || res.responseData?.data || res.responseData || null;\n          console.log('Project data assigned:', this.project);\n          console.log('Project fields available:', Object.keys(this.project || {}));\n          // Don't override selectedTab here - let query params handle it\n        } else {\n          console.error('API returned fault:', res.faultMessage);\n          this.project = null;\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        console.error('Error fetching project details:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  fetchProjectPermits() {\n    if (!this.projectId) {\n      return;\n    }\n    this.isLoading = true;\n    // Get permits for this specific project\n    this.permitsService.getPermitsForKendoGrid({\n      take: 100,\n      skip: 0,\n      sort: [],\n      filter: {\n        logic: 'and',\n        filters: [{\n          field: 'projectId',\n          operator: 'eq',\n          value: this.projectId\n        }]\n      },\n      search: '',\n      loggedInUserId: this.loginUser.userId\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        console.log('Project permits API response:', res);\n        if (res?.isFault) {\n          console.error('Failed to load project permits:', res.faultMessage);\n          this.projectPermits = [];\n        } else {\n          const rawPermits = res.responseData?.data || res.data || [];\n          // Client-side guard: ensure only permits for this project are shown\n          this.projectPermits = (rawPermits || []).filter(p => {\n            const permitProjectId = p?.projectId ?? p?.projectID ?? p?.project_id ?? p?.ProjectId ?? p?.ProjectID;\n            return this.projectId != null ? Number(permitProjectId) === Number(this.projectId) : true;\n          });\n          console.log('Project permits assigned (filtered):', this.projectPermits);\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        console.error('Error loading project permits:', err);\n        this.projectPermits = [];\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  goBack() {\n    this.router.navigate(['/projects/list']);\n  }\n  editProject() {\n    if (!this.projectId) {\n      return;\n    }\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the ProjectPopup\n    const modalRef = this.modalService.open(ProjectPopupComponent, NgbModalOptions);\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = this.projectId;\n    modalRef.componentInstance.project = this.project;\n    // Subscribe to the modal event when it closes\n    modalRef.result.then(result => {\n      // Handle successful edit\n      if (result) {\n        console.log('Project edited successfully:', result);\n        // Refresh project details\n        this.fetchProjectDetails();\n      }\n    }, reason => {\n      // Handle modal dismissal\n      console.log('Modal dismissed:', reason);\n    });\n  }\n  viewPermit(permitId) {\n    this.router.navigate(['/permits/view', permitId], {\n      queryParams: {\n        from: 'project',\n        projectId: this.projectId\n      }\n    });\n  }\n  onStatusChange(permit, newStatus) {\n    if (!permit?.permitId || !newStatus) {\n      return;\n    }\n    const allowed = ['Approved', 'Pacifica Verification', 'Dis-Approved', 'Pending', 'Not Required', 'In Review', '1 Cycle Completed'];\n    if (!allowed.includes(newStatus)) {\n      return;\n    }\n    const previous = permit.internalReviewStatus;\n    permit.internalReviewStatus = newStatus;\n    this.isLoading = true;\n    this.cdr.markForCheck();\n    this.permitsService.updatePermitInternalReviewStatus({\n      permitId: permit.permitId,\n      internalReviewStatus: newStatus\n    }).subscribe({\n      next: res => {\n        const isFault = res?.isFault || res?.responseData?.isFault;\n        if (isFault) {\n          permit.internalReviewStatus = previous;\n          this.isLoading = false;\n          this.cdr.markForCheck();\n        }\n        this.isLoading = false;\n        this.cdr.markForCheck();\n      },\n      error: () => {\n        permit.internalReviewStatus = previous;\n        this.isLoading = false;\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  getStatusClass(status) {\n    if (!status) return 'status-n-a';\n    const key = status.toLowerCase();\n    if (key === 'current') return 'status-active';\n    if (key === 'completed') return 'status-completed';\n    if (key === 'cancelled & archived') return 'status-cancelled';\n    if (key === 'closed & archived') return 'status-cancelled';\n    return 'status-' + status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n  }\n  showTab(tab, $event) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n  static ɵfac = function ProjectViewComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProjectViewComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.AppService), i0.ɵɵdirectiveInject(i3.ProjectsService), i0.ɵɵdirectiveInject(i4.PermitsService), i0.ɵɵdirectiveInject(i5.NgbModal));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProjectViewComponent,\n    selectors: [[\"app-project-view\"]],\n    decls: 3,\n    vars: 2,\n    consts: [[\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"project-view-container\"], [\"class\", \"card shadow-sm rounded-3\", 4, \"ngIf\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"card\", \"shadow-sm\", \"rounded-3\"], [1, \"project-details-header\"], [1, \"header-content\"], [1, \"title-wrap\"], [1, \"title-line\"], [1, \"project-title\"], [1, \"status-text\", 3, \"ngClass\"], [1, \"button-group\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-light-primary\", \"d-flex\", \"align-items-center\", \"mb-2\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\"], [1, \"card-header\", \"border-0\", \"py-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-4\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [1, \"d-flex\", \"align-items-center\", \"gap-2\", 2, \"margin-right\", \"16px\"], [\"type\", \"button\", \"class\", \"btn btn-link p-0\", \"title\", \"Edit Project\", 3, \"click\", 4, \"ngIf\"], [1, \"card-body\"], [4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Edit Project\", 1, \"btn\", \"btn-link\", \"p-0\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"text-primary\", 2, \"font-size\", \"1.1rem\"], [1, \"project-details-content\"], [1, \"project-details-grid\"], [1, \"project-detail-item\"], [1, \"project-value\"], [1, \"project-detail-item\", \"span-2\"], [\"class\", \"d-flex justify-content-center align-items-center py-5 text-muted\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\", \"text-muted\"], [1, \"text-center\"], [1, \"fas\", \"fa-file-alt\", \"fa-3x\", \"mb-3\"], [1, \"table-responsive\"], [1, \"table\"], [1, \"ball-in-court-col\"], [4, \"ngFor\", \"ngForOf\"], [\"title\", \"View Permit\", \"aria-label\", \"View Permit\", 1, \"fw-bold\", 3, \"click\"], [1, \"badge\", \"badge-green-light\", \"ms-1\"], [1, \"form-select\", \"form-select-sm\", \"w-auto\", 3, \"change\", \"value\", \"disabled\"], [\"disabled\", \"\", 3, \"value\"], [\"value\", \"Approved\"], [\"value\", \"Pacifica Verification\"], [\"value\", \"Dis-Approved\"], [\"value\", \"Pending\"], [\"value\", \"Not Required\"], [\"value\", \"In Review\"], [\"value\", \"1 Cycle Completed\"], [1, \"ball-in-court-cell\"], [1, \"wrap-text\"]],\n    template: function ProjectViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ProjectViewComponent_div_0_Template, 7, 0, \"div\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵtemplate(2, ProjectViewComponent_div_2_Template, 26, 13, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.project);\n      }\n    },\n    dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i7.NgSelectOption, i7.ɵNgSelectMultipleOption, i6.DatePipe],\n    styles: [\".project-view-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0;\\n}\\n\\n.project-view-container[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  padding-bottom: 0.25rem;\\n}\\n.project-view-container[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n  padding-top: 0.5rem;\\n}\\n\\n.project-details-header[_ngcontent-%COMP%] {\\n  padding: 0 1.5rem;\\n  border-bottom: 1px solid #e5eaee;\\n  background: transparent;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding-top: 0.5rem;\\n}\\n.project-details-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #3f4254;\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  align-items: center;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-sm[_ngcontent-%COMP%] {\\n  font-size: 0.875rem !important;\\n  padding: 0.375rem 0.75rem !important;\\n  line-height: 1.5 !important;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%], \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.3rem;\\n  padding: 0.15rem 0.5rem;\\n  border-radius: 0.55rem;\\n  background-color: #f3f6f9;\\n  color: #3f4254;\\n  border: 1px solid #e5eaee;\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);\\n  font-weight: 600;\\n  font-size: 0.8rem;\\n  line-height: 1;\\n  transition: background-color 0.2s ease, box-shadow 0.2s ease, transform 0.02s ease;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #5e6e82;\\n  font-size: 0.75rem;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover, \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:hover {\\n  background-color: #eef2f7;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.07);\\n  text-decoration: none;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:active, \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(1px);\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n}\\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.project-details-content[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n}\\n\\n.project-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(4, minmax(0, 1fr));\\n  gap: 1.5rem;\\n}\\n\\n.project-detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.1rem;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 600; \\n\\n  display: block; \\n\\n  margin-bottom: 0.15rem;\\n  font-size: 15px;\\n  display: block;\\n  line-height: 1.2;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .project-value[_ngcontent-%COMP%], \\n.project-detail-item[_ngcontent-%COMP%]   .project-status[_ngcontent-%COMP%] {\\n  margin-top: 0.1rem;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .project-label[_ngcontent-%COMP%] {\\n  font-weight: 600; \\n\\n  display: block; \\n\\n  margin-bottom: 0.25rem; \\n\\n}\\n\\n.status-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  border: 1px solid transparent;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n.status-text.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.status-text.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.status-text.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.status-text.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.status-text.status-submitted[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #bbdefb;\\n}\\n.status-text.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.status-text.status-conditional-approval[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.status-text.status-void[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.status-text.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.badge-green-light[_ngcontent-%COMP%] {\\n  background-color: #42c761; \\n\\n  color: #155724; \\n\\n}\\n\\n.project-value[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3f4254;\\n  font-weight: 500;\\n  margin: 0; \\n\\n  padding: 0.25rem 0; \\n\\n  border-bottom: none;\\n}\\n\\n.project-status[_ngcontent-%COMP%] {\\n  display: block;\\n  vertical-align: top;\\n  padding: 0.5rem 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  text-align: left;\\n  background: transparent;\\n  border: none;\\n  min-width: 0;\\n  border-radius: 0;\\n}\\n\\n.project-detail-item.span-2[_ngcontent-%COMP%] {\\n  grid-column: span 2;\\n}\\n\\n.status-active[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n\\n.status-inactive[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.status-completed[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n\\n.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n\\n.status-cancelled[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n\\n.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n  padding: 0 0.5rem 0 0.5rem;\\n  margin-top: 0;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  overflow: hidden;\\n  table-layout: fixed;\\n  width: 100%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #3f4254;\\n  border-bottom: 2px solid #e5eaee;\\n  padding: 1rem 0.75rem;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: none;\\n  border-bottom: 1px solid #e5eaee;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  vertical-align: middle;\\n  white-space: nowrap;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-number-col[_ngcontent-%COMP%] {\\n  width: 25%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-description-col[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-type-col[_ngcontent-%COMP%] {\\n  width: 20%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-status-col[_ngcontent-%COMP%] {\\n  width: 15%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-number-cell[_ngcontent-%COMP%] {\\n  width: 25%;\\n  white-space: nowrap;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-description-cell[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-type-cell[_ngcontent-%COMP%] {\\n  width: 20%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-status-cell[_ngcontent-%COMP%] {\\n  width: 15%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child {\\n  width: 30%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child {\\n  width: 30%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child, \\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child   a[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover   td[_ngcontent-%COMP%]:first-child   a[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.ball-in-court-col[_ngcontent-%COMP%] {\\n  width: 25%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.ball-in-court-cell[_ngcontent-%COMP%] {\\n  white-space: normal;\\n  word-break: break-word;\\n  overflow-wrap: anywhere;\\n}\\n\\n.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%] {\\n  color: var(--bs-primary, #0d6efd);\\n  text-decoration: none;\\n  font-weight: 700;\\n  cursor: pointer;\\n  transition: color 0.15s ease, -webkit-text-decoration 0.15s ease;\\n  transition: color 0.15s ease, text-decoration 0.15s ease;\\n  transition: color 0.15s ease, text-decoration 0.15s ease, -webkit-text-decoration 0.15s ease;\\n}\\n.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%]:hover {\\n  color: #0b5ed7;\\n  text-decoration: underline;\\n}\\n.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(24, 125, 228, 0.25);\\n  border-radius: 0.25rem;\\n}\\n\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 600;\\n  border: 1px solid transparent;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05rem;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-submitted[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #bbdefb;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-conditional-approval[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-void[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-complete[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-approved-w-conditions[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-requires-re-submit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-unknown[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.permit-description-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], \\n.permit-type-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], \\n.permit-status-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  max-width: 100%;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 0.375rem;\\n  font-weight: 600;\\n  transition: all 0.2s ease;\\n}\\n.permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.title-wrap[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n\\n.title-line[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: baseline;\\n  gap: 0.75rem;\\n}\\n\\n.project-title[_ngcontent-%COMP%] {\\n  font-size: 1.05rem;\\n  font-weight: 700;\\n  color: #181c32;\\n}\\n\\n.status-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  border: 1px solid transparent;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n\\n.project-number-line[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #6c7293;\\n  padding-bottom: 0.25rem;\\n}\\n\\n.fullscreen-loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(255, 255, 255, 0.9);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 9999;\\n}\\n.fullscreen-loading-overlay[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background: white;\\n  padding: 2rem;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n}\\n\\n@media (max-width: 768px) {\\n  .project-details-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n    gap: 1rem;\\n  }\\n  .project-detail-item.span-2[_ngcontent-%COMP%] {\\n    grid-column: auto;\\n  }\\n  .table-responsive[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n  }\\n  .permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 0.25rem 0.5rem;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .project-details-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 0.75rem;\\n  }\\n  .project-details-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n    align-items: flex-start;\\n  }\\n  .project-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: flex-end;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Subscription", "combineLatest", "ProjectPopupComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ProjectViewComponent_div_2_button_22_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "editProject", "ɵɵelement", "ɵɵelementContainerStart", "ɵɵadvance", "ɵɵtextInterpolate", "project", "projectStartDate", "ɵɵpipeBind2", "projectEndDate", "projectLocation", "externalPMNames", "projectDescription", "ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template_a_click_2_listener", "permit_r5", "_r4", "$implicit", "viewPermit", "permitId", "ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template_select_change_10_listener", "$event", "onStatusChange", "target", "value", "ɵɵtextInterpolate1", "permitName", "permitReviewType", "permitNumber", "ɵɵproperty", "internalReviewStatus", "isLoading", "permitAppliedDate", "attentionReason", "ɵɵtemplate", "ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template", "projectPermits", "ProjectViewComponent_div_2_ng_container_25_div_1_Template", "ProjectViewComponent_div_2_ng_container_25_div_2_Template", "length", "ProjectViewComponent_div_2_Template_button_click_10_listener", "_r1", "goBack", "ProjectViewComponent_div_2_Template_a_click_16_listener", "showTab", "ProjectViewComponent_div_2_Template_a_click_19_listener", "ProjectViewComponent_div_2_button_22_Template", "ProjectViewComponent_div_2_ng_container_24_Template", "ProjectViewComponent_div_2_ng_container_25_Template", "ɵɵtextInterpolate2", "internalProjectNumber", "projectName", "getStatusClass", "status", "ɵɵpureFunction1", "_c0", "selectedTab", "ProjectViewComponent", "route", "router", "cdr", "appService", "projectsService", "permitsService", "modalService", "projectId", "loginUser", "routeSubscription", "constructor", "ngOnInit", "getLoggedInUser", "paramMap", "queryParams", "subscribe", "idParam", "get", "Number", "console", "log", "activeTab", "fetchProjectDetails", "fetchProjectPermits", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "unsubscribe", "getProject", "next", "res", "<PERSON><PERSON><PERSON>", "responseData", "Project", "data", "Object", "keys", "error", "faultMessage", "err", "getPermitsForKendoGrid", "take", "skip", "sort", "filter", "logic", "filters", "field", "operator", "search", "loggedInUserId", "userId", "rawPermits", "p", "permitProjectId", "projectID", "project_id", "ProjectId", "ProjectID", "navigate", "NgbModalOptions", "size", "backdrop", "keyboard", "scrollable", "modalRef", "open", "componentInstance", "id", "result", "then", "reason", "from", "permit", "newStatus", "allowed", "includes", "previous", "updatePermitInternalReviewStatus", "key", "toLowerCase", "replace", "tab", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "ChangeDetectorRef", "i2", "AppService", "i3", "ProjectsService", "i4", "PermitsService", "i5", "NgbModal", "selectors", "decls", "vars", "consts", "template", "ProjectViewComponent_Template", "rf", "ctx", "ProjectViewComponent_div_0_Template", "ProjectViewComponent_div_2_Template"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-view\\project-view.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-view\\project-view.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Subscription, combineLatest } from 'rxjs';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { ProjectsService } from '../../services/projects.service';\r\nimport { PermitsService } from '../../services/permits.service';\r\nimport { AppService } from '../../services/app.service';\r\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\r\n\r\n@Component({\r\n  selector: 'app-project-view',\r\n  templateUrl: './project-view.component.html',\r\n  styleUrls: ['./project-view.component.scss']\r\n})\r\nexport class ProjectViewComponent implements OnInit, OnDestroy {\r\n  public projectId: number | null = null;\r\n  public project: any = null;\r\n  public isLoading: boolean = false;\r\n  public selectedTab: string = 'details';\r\n  public projectPermits: any[] = [];\r\n  public loginUser: any = {};\r\n  private routeSubscription: Subscription = new Subscription();\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private cdr: ChangeDetectorRef,\r\n    private appService: AppService,\r\n    private projectsService: ProjectsService,\r\n    private permitsService: PermitsService,\r\n    private modalService: NgbModal\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loginUser = this.appService.getLoggedInUser();\r\n\r\n    // Combine route params and query params to handle both together\r\n    this.routeSubscription = combineLatest([\r\n      this.route.paramMap,\r\n      this.route.queryParams\r\n    ]).subscribe(([paramMap, queryParams]) => {\r\n      const idParam = paramMap.get('id');\r\n      this.projectId = idParam ? Number(idParam) : null;\r\n\r\n      console.log('Project view - received params:', { projectId: this.projectId, queryParams });\r\n      \r\n      // Handle active tab from query params\r\n      const activeTab = queryParams['activeTab'];\r\n      if (activeTab && (activeTab === 'permits' || activeTab === 'details')) {\r\n        this.selectedTab = activeTab;\r\n        console.log('Setting selectedTab from query params:', activeTab);\r\n      } else {\r\n        console.log('No valid activeTab found, keeping default:', this.selectedTab);\r\n      }\r\n\r\n      if (this.projectId) {\r\n        this.fetchProjectDetails();\r\n        this.fetchProjectPermits();\r\n      }\r\n      \r\n      this.cdr.markForCheck();\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    if (this.routeSubscription) {\r\n      this.routeSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  public fetchProjectDetails(): void {\r\n    if (!this.projectId) { return; }\r\n    this.isLoading = true;\r\n    this.projectsService.getProject({ projectId: this.projectId }).subscribe({\r\n      next: (res: any) => {\r\n        this.isLoading = false;\r\n        console.log('Project API Response:', res);\r\n        if (!res?.isFault) {\r\n          // Try different response structures\r\n          this.project = res.responseData?.Project || res.responseData?.data || res.responseData || null;\r\n          console.log('Project data assigned:', this.project);\r\n          console.log('Project fields available:', Object.keys(this.project || {}));\r\n          // Don't override selectedTab here - let query params handle it\r\n        } else {\r\n          console.error('API returned fault:', res.faultMessage);\r\n          this.project = null;\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err) => {\r\n        this.isLoading = false;\r\n        console.error('Error fetching project details:', err);\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  public fetchProjectPermits(): void {\r\n    if (!this.projectId) { return; }\r\n    this.isLoading = true;\r\n    \r\n    // Get permits for this specific project\r\n    this.permitsService.getPermitsForKendoGrid({\r\n      take: 100,\r\n      skip: 0,\r\n      sort: [],\r\n      filter: {\r\n        logic: 'and',\r\n        filters: [\r\n          {\r\n            field: 'projectId',\r\n            operator: 'eq',\r\n            value: this.projectId\r\n          }\r\n        ]\r\n      },\r\n      search: '',\r\n      loggedInUserId: this.loginUser.userId\r\n    }).subscribe({\r\n      next: (res: any) => {\r\n        this.isLoading = false;\r\n        console.log('Project permits API response:', res);\r\n        if (res?.isFault) {\r\n          console.error('Failed to load project permits:', res.faultMessage);\r\n          this.projectPermits = [];\r\n        } else {\r\n          const rawPermits = res.responseData?.data || res.data || [];\r\n          // Client-side guard: ensure only permits for this project are shown\r\n          this.projectPermits = (rawPermits || []).filter((p: any) => {\r\n            const permitProjectId = p?.projectId ?? p?.projectID ?? p?.project_id ?? p?.ProjectId ?? p?.ProjectID;\r\n            return this.projectId != null ? Number(permitProjectId) === Number(this.projectId) : true;\r\n          });\r\n          console.log('Project permits assigned (filtered):', this.projectPermits);\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err: any) => {\r\n        this.isLoading = false;\r\n        console.error('Error loading project permits:', err);\r\n        this.projectPermits = [];\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  public goBack(): void {\r\n    this.router.navigate(['/projects/list']);\r\n  }\r\n\r\n  public editProject(): void {\r\n    if (!this.projectId) { return; }\r\n    \r\n    const NgbModalOptions: {\r\n      size: string;\r\n      backdrop: boolean | 'static';\r\n      keyboard: boolean;\r\n      scrollable: boolean;\r\n    } = {\r\n      size: 'lg', // Large modal size\r\n      backdrop: 'static', // Prevents closing when clicking outside\r\n      keyboard: false, // Disables closing with the Escape key\r\n      scrollable: true, // Allows scrolling inside the modal\r\n    };\r\n\r\n    // Open the modal and load the ProjectPopup\r\n    const modalRef = this.modalService.open(\r\n      ProjectPopupComponent,\r\n      NgbModalOptions\r\n    );\r\n    \r\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\r\n    modalRef.componentInstance.id = this.projectId;\r\n    modalRef.componentInstance.project = this.project;\r\n    \r\n    // Subscribe to the modal event when it closes\r\n    modalRef.result.then(\r\n      (result) => {\r\n        // Handle successful edit\r\n        if (result) {\r\n          console.log('Project edited successfully:', result);\r\n          // Refresh project details\r\n          this.fetchProjectDetails();\r\n        }\r\n      },\r\n      (reason) => {\r\n        // Handle modal dismissal\r\n        console.log('Modal dismissed:', reason);\r\n      }\r\n    );\r\n  }\r\n\r\n  public viewPermit(permitId: number): void {\r\n    this.router.navigate(['/permits/view', permitId], { \r\n      queryParams: { from: 'project', projectId: this.projectId } \r\n    });\r\n  }\r\n\r\n  public onStatusChange(permit: any, newStatus: string): void {\r\n    if (!permit?.permitId || !newStatus) { return; }\r\n    const allowed = [\r\n      'Approved',\r\n      'Pacifica Verification',\r\n      'Dis-Approved',\r\n      'Pending',\r\n      'Not Required',\r\n      'In Review',\r\n      '1 Cycle Completed'\r\n    ];\r\n    if (!allowed.includes(newStatus)) { return; }\r\n\r\n    const previous = permit.internalReviewStatus;\r\n    permit.internalReviewStatus = newStatus;\r\n    this.isLoading = true;\r\n    this.cdr.markForCheck();\r\n\r\n    this.permitsService\r\n      .updatePermitInternalReviewStatus({ permitId: permit.permitId, internalReviewStatus: newStatus })\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          const isFault = res?.isFault || res?.responseData?.isFault;\r\n          if (isFault) {\r\n            permit.internalReviewStatus = previous;\r\n            this.isLoading = false;\r\n            this.cdr.markForCheck();\r\n          }\r\n          this.isLoading = false;\r\n          this.cdr.markForCheck();\r\n        },\r\n        error: () => {\r\n          permit.internalReviewStatus = previous;\r\n          this.isLoading = false;\r\n          this.cdr.markForCheck();\r\n        }\r\n      });\r\n  }\r\n\r\n  public getStatusClass(status: string): string {\r\n    if (!status) return 'status-n-a';\r\n    const key = status.toLowerCase();\r\n    if (key === 'current') return 'status-active';\r\n    if (key === 'completed') return 'status-completed';\r\n    if (key === 'cancelled & archived') return 'status-cancelled';\r\n    if (key === 'closed & archived') return 'status-cancelled';\r\n    return 'status-' + status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\r\n  }\r\n\r\n  showTab(tab: string, $event: any) {\r\n    this.selectedTab = tab;\r\n    this.cdr.markForCheck();\r\n  }\r\n}\r\n", "<!-- Full Screen Loading Overlay -->\r\n<div *ngIf=\"isLoading\" class=\"fullscreen-loading-overlay\">\r\n  <div class=\"loading-content\">\r\n    <div class=\"custom-colored-spinner\" role=\"status\">\r\n      <span class=\"visually-hidden\">Loading...</span>\r\n    </div>\r\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\r\n  </div>\r\n</div>\r\n\r\n<div class=\"project-view-container\">\r\n  <!-- Project Details Card -->\r\n  <div class=\"card shadow-sm rounded-3\" *ngIf=\"project\">\r\n    <!-- Project Details Header -->\r\n    <div class=\"project-details-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-wrap\">\r\n          <div class=\"title-line\">\r\n            <span class=\"project-title\">Project # {{ project.internalProjectNumber || \"\" }} - {{ project.projectName || \"\" }}</span>\r\n            <span class=\"status-text\" [ngClass]=\"getStatusClass(project.status)\">{{ project.status || \"Current\" }}</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"button-group\">\r\n          <!-- <button type=\"button\" class=\"btn portal-button\" (click)=\"editProject()\">\r\n            <i class=\"fa fa-pencil\"></i>Edit\r\n          </button> -->\r\n          <button type=\"button\" class=\"btn btn-sm btn-light-primary d-flex align-items-center mb-2\" (click)=\"goBack()\">\r\n            <i class=\"fas fa-arrow-left me-2\"></i>\r\n            Back\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- Card Header with Tabs -->\r\n    <div class=\"card-header border-0 py-2 d-flex justify-content-between align-items-center\">\r\n      <!-- Tabs -->\r\n      <ul class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-4 fw-bold flex-nowrap\">\r\n        <li class=\"nav-item\">\r\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer\" [ngClass]=\"{ active: selectedTab === 'details' }\"\r\n            (click)=\"showTab('details', $event)\">\r\n            Project details\r\n          </a>\r\n        </li>\r\n        <li class=\"nav-item\">\r\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer\" [ngClass]=\"{ active: selectedTab === 'permits' }\"\r\n            (click)=\"showTab('permits', $event)\">\r\n            Permits list\r\n          </a>\r\n        </li>\r\n      </ul>\r\n       <div class=\"d-flex align-items-center gap-2\" style=\"margin-right: 16px;\">\r\n        <!-- Edit icon - only show when permit details tab is active -->\r\n        <button type=\"button\" class=\"btn btn-link p-0\" (click)=\"editProject()\" *ngIf=\"selectedTab === 'details'\" title=\"Edit Project\">\r\n          <i class=\"fas fa-edit text-primary\" style=\"font-size: 1.1rem;\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Card Body with Tab Content -->\r\n    <div class=\"card-body\">\r\n      <!-- Project Details Tab Content -->\r\n      <ng-container *ngIf=\"selectedTab == 'details' && project\">\r\n        <div class=\"project-details-content\">\r\n          <div class=\"project-details-grid\">\r\n            <div class=\"project-detail-item\">\r\n              <label>Start date</label>\r\n              <span class=\"project-value\">{{\r\n                project.projectStartDate\r\n                ? (project.projectStartDate | date : \"MM/dd/yyyy\")\r\n                : \"\"\r\n              }}</span>\r\n            </div>\r\n            <div class=\"project-detail-item\">\r\n              <label>End date</label>\r\n              <span class=\"project-value\">{{\r\n                project.projectEndDate\r\n                ? (project.projectEndDate | date : \"MM/dd/yyyy\")\r\n                : \"\"\r\n              }}</span>\r\n            </div>\r\n            <div class=\"project-detail-item span-2\">\r\n           <label >Location</label>\r\n              <span class=\"project-value\">{{ project.projectLocation || \"\" }}</span>\r\n            </div>\r\n             \r\n            <div class=\"project-detail-item\">\r\n              <label>External project manager</label>\r\n              <span class=\"project-value\">{{ project.externalPMNames || \"\" }}</span>\r\n            </div>\r\n            <div class=\"project-detail-item span-2\">\r\n              <label>Description</label>\r\n              <span class=\"project-value\">{{ project.projectDescription || \"\" }}</span>\r\n            </div>\r\n            \r\n           </div>\r\n         </div>\r\n       </ng-container>\r\n\r\n      <!-- Permits List Tab Content -->\r\n      <ng-container *ngIf=\"selectedTab == 'permits'\">\r\n        <!-- Empty State for Permits -->\r\n        <div class=\"d-flex justify-content-center align-items-center py-5 text-muted\" *ngIf=\"projectPermits.length === 0\">\r\n          <div class=\"text-center\">\r\n            <i class=\"fas fa-file-alt fa-3x mb-3\"></i>\r\n            <p>No permits found for this project.</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Permits Table -->\r\n        <div class=\"table-responsive\" *ngIf=\"projectPermits.length > 0\">\r\n          <table class=\"table\">\r\n            <thead>\r\n              <tr>\r\n                <th>Permit Name</th>\r\n                <th>Permit #</th>\r\n                <th>Status</th>\r\n                <th>Submitted Date</th>\r\n                <th class=\"ball-in-court-col\">Ball in Court</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr *ngFor=\"let permit of projectPermits\">\r\n                <td>\r\n                  <a \r\n                    class=\"fw-bold\" \r\n                    (click)=\"viewPermit(permit.permitId)\" \r\n                    title=\"View Permit\"\r\n                    aria-label=\"View Permit\"\r\n                  >\r\n                    {{ permit.permitName || \"\" }}  \r\n                  </a>\r\n              <span class=\"badge badge-green-light ms-1\">\r\n  {{ permit.permitReviewType || \"\" }}\r\n</span>\r\n                </td>\r\n                <td>\r\n                  <span>{{ permit.permitNumber || \"\" }}</span>\r\n                </td>\r\n                <td>\r\n                  <select class=\"form-select form-select-sm w-auto\"\r\n                          [value]=\"permit.internalReviewStatus || ''\"\r\n                          (change)=\"onStatusChange(permit, $any($event.target).value)\"\r\n                          [disabled]=\"isLoading\">\r\n                    <option [value]=\"''\" disabled>Select status</option>\r\n                    <option value=\"Approved\">Approved</option>\r\n                    <option value=\"Pacifica Verification\">Pacifica Verification</option>\r\n                    <option value=\"Dis-Approved\">Dis-Approved</option>\r\n                    <option value=\"Pending\">Pending</option>\r\n                    <option value=\"Not Required\">Not Required</option>\r\n                    <option value=\"In Review\">In Review</option>\r\n                    <option value=\"1 Cycle Completed\">1 Cycle Completed</option>\r\n                  </select>\r\n                </td>\r\n                <td>\r\n                  <span>{{ permit.permitAppliedDate ? (permit.permitAppliedDate | date : 'MM/dd/yyyy') : '' }}</span>\r\n                </td>\r\n                <td class=\"ball-in-court-cell\">\r\n                  <span class=\"wrap-text\">{{ permit.attentionReason || '' }}</span>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </ng-container>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,YAAY,EAAEC,aAAa,QAAQ,MAAM;AAKlD,SAASC,qBAAqB,QAAQ,0CAA0C;;;;;;;;;;;;;;ICH1EC,EAHN,CAAAC,cAAA,aAA0D,aAC3B,aACuB,cAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;;;;IA4CEH,EAAA,CAAAC,cAAA,iBAA8H;IAA/ED,EAAA,CAAAI,UAAA,mBAAAC,sEAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IACpEX,EAAA,CAAAY,SAAA,YAAmE;IACrEZ,EAAA,CAAAG,YAAA,EAAS;;;;;IAOXH,EAAA,CAAAa,uBAAA,GAA0D;IAIlDb,EAHN,CAAAC,cAAA,cAAqC,cACD,cACC,YACxB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzBH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,GAI1B;;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;IAEJH,EADF,CAAAC,cAAA,cAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAI1B;;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;IAEPH,EADC,CAAAC,cAAA,eAAwC,aACjC;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrBH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IACjEF,EADiE,CAAAG,YAAA,EAAO,EAClE;IAGJH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvCH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IACjEF,EADiE,CAAAG,YAAA,EAAO,EAClE;IAEJH,EADF,CAAAC,cAAA,eAAwC,aAC/B;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1BH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAIvEF,EAJuE,CAAAG,YAAA,EAAO,EACrE,EAED,EACF;;;;;IA7B2BH,EAAA,CAAAc,SAAA,GAI1B;IAJ0Bd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAAC,gBAAA,GAAAjB,EAAA,CAAAkB,WAAA,OAAAV,MAAA,CAAAQ,OAAA,CAAAC,gBAAA,qBAI1B;IAI0BjB,EAAA,CAAAc,SAAA,GAI1B;IAJ0Bd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAAG,cAAA,GAAAnB,EAAA,CAAAkB,WAAA,QAAAV,MAAA,CAAAQ,OAAA,CAAAG,cAAA,qBAI1B;IAI0BnB,EAAA,CAAAc,SAAA,GAAmC;IAAnCd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAAI,eAAA,OAAmC;IAKnCpB,EAAA,CAAAc,SAAA,GAAmC;IAAnCd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAAK,eAAA,OAAmC;IAInCrB,EAAA,CAAAc,SAAA,GAAsC;IAAtCd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAAM,kBAAA,OAAsC;;;;;IAWtEtB,EADF,CAAAC,cAAA,cAAkH,cACvF;IACvBD,EAAA,CAAAY,SAAA,YAA0C;IAC1CZ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IAEzCF,EAFyC,CAAAG,YAAA,EAAI,EACrC,EACF;;;;;;IAiBIH,EAFJ,CAAAC,cAAA,SAA0C,SACpC,YAMD;IAHCD,EAAA,CAAAI,UAAA,mBAAAmB,mFAAA;MAAA,MAAAC,SAAA,GAAAxB,EAAA,CAAAM,aAAA,CAAAmB,GAAA,EAAAC,SAAA;MAAA,MAAAlB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAmB,UAAA,CAAAH,SAAA,CAAAI,QAAA,CAA2B;IAAA,EAAC;IAIrC5B,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACRH,EAAA,CAAAC,cAAA,eAA2C;IACvDD,EAAA,CAAAE,MAAA,GACF;IACgBF,EADhB,CAAAG,YAAA,EAAO,EACc;IAEHH,EADF,CAAAC,cAAA,SAAI,WACI;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IACvCF,EADuC,CAAAG,YAAA,EAAO,EACzC;IAEHH,EADF,CAAAC,cAAA,SAAI,kBAI6B;IADvBD,EAAA,CAAAI,UAAA,oBAAAyB,0FAAAC,MAAA;MAAA,MAAAN,SAAA,GAAAxB,EAAA,CAAAM,aAAA,CAAAmB,GAAA,EAAAC,SAAA;MAAA,MAAAlB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAUF,MAAA,CAAAuB,cAAA,CAAAP,SAAA,EAAAM,MAAA,CAAAE,MAAA,CAAAC,KAAA,CAAiD;IAAA,EAAC;IAElEjC,EAAA,CAAAC,cAAA,kBAA8B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpDH,EAAA,CAAAC,cAAA,kBAAyB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC1CH,EAAA,CAAAC,cAAA,kBAAsC;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClDH,EAAA,CAAAC,cAAA,kBAAwB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACxCH,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClDH,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5CH,EAAA,CAAAC,cAAA,kBAAkC;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAEvDF,EAFuD,CAAAG,YAAA,EAAS,EACrD,EACN;IAEHH,EADF,CAAAC,cAAA,UAAI,YACI;IAAAD,EAAA,CAAAE,MAAA,IAAsF;;IAC9FF,EAD8F,CAAAG,YAAA,EAAO,EAChG;IAEHH,EADF,CAAAC,cAAA,cAA+B,gBACL;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAE9DF,EAF8D,CAAAG,YAAA,EAAO,EAC9D,EACF;;;;;IA9BCH,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAkC,kBAAA,MAAAV,SAAA,CAAAW,UAAA,YACF;IAEhBnC,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAkC,kBAAA,MAAAV,SAAA,CAAAY,gBAAA,aACF;IAGwBpC,EAAA,CAAAc,SAAA,GAA+B;IAA/Bd,EAAA,CAAAe,iBAAA,CAAAS,SAAA,CAAAa,YAAA,OAA+B;IAI7BrC,EAAA,CAAAc,SAAA,GAA2C;IAE3Cd,EAFA,CAAAsC,UAAA,UAAAd,SAAA,CAAAe,oBAAA,OAA2C,aAAA/B,MAAA,CAAAgC,SAAA,CAErB;IACpBxC,EAAA,CAAAc,SAAA,EAAY;IAAZd,EAAA,CAAAsC,UAAA,aAAY;IAWhBtC,EAAA,CAAAc,SAAA,IAAsF;IAAtFd,EAAA,CAAAe,iBAAA,CAAAS,SAAA,CAAAiB,iBAAA,GAAAzC,EAAA,CAAAkB,WAAA,QAAAM,SAAA,CAAAiB,iBAAA,qBAAsF;IAGpEzC,EAAA,CAAAc,SAAA,GAAkC;IAAlCd,EAAA,CAAAe,iBAAA,CAAAS,SAAA,CAAAkB,eAAA,OAAkC;;;;;IA5C5D1C,EAJR,CAAAC,cAAA,cAAgE,gBACzC,YACZ,SACD,SACE;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAE/CF,EAF+C,CAAAG,YAAA,EAAK,EAC7C,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA2C,UAAA,KAAAC,+DAAA,mBAA0C;IAyChD5C,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IAzCuBH,EAAA,CAAAc,SAAA,IAAiB;IAAjBd,EAAA,CAAAsC,UAAA,YAAA9B,MAAA,CAAAqC,cAAA,CAAiB;;;;;IAtBhD7C,EAAA,CAAAa,uBAAA,GAA+C;IAU7Cb,EARA,CAAA2C,UAAA,IAAAG,yDAAA,kBAAkH,IAAAC,yDAAA,mBAQlD;;;;;IARe/C,EAAA,CAAAc,SAAA,EAAiC;IAAjCd,EAAA,CAAAsC,UAAA,SAAA9B,MAAA,CAAAqC,cAAA,CAAAG,MAAA,OAAiC;IAQjFhD,EAAA,CAAAc,SAAA,EAA+B;IAA/Bd,EAAA,CAAAsC,UAAA,SAAA9B,MAAA,CAAAqC,cAAA,CAAAG,MAAA,KAA+B;;;;;;IA3F1DhD,EANV,CAAAC,cAAA,aAAsD,aAEhB,cACN,cACF,cACE,eACM;IAAAD,EAAA,CAAAE,MAAA,GAAqF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxHH,EAAA,CAAAC,cAAA,eAAqE;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAE1GF,EAF0G,CAAAG,YAAA,EAAO,EACzG,EACF;IAKJH,EAJF,CAAAC,cAAA,cAA0B,kBAIqF;IAAnBD,EAAA,CAAAI,UAAA,mBAAA6C,6DAAA;MAAAjD,EAAA,CAAAM,aAAA,CAAA4C,GAAA;MAAA,MAAA1C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA2C,MAAA,EAAQ;IAAA,EAAC;IAC1GnD,EAAA,CAAAY,SAAA,aAAsC;IACtCZ,EAAA,CAAAE,MAAA,cACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;IAMAH,EAJN,CAAAC,cAAA,eAAyF,cAEgB,cAChF,aAEoB;IAArCD,EAAA,CAAAI,UAAA,mBAAAgD,wDAAAtB,MAAA;MAAA9B,EAAA,CAAAM,aAAA,CAAA4C,GAAA;MAAA,MAAA1C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA6C,OAAA,CAAQ,SAAS,EAAAvB,MAAA,CAAS;IAAA,EAAC;IACpC9B,EAAA,CAAAE,MAAA,yBACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACD;IAEHH,EADF,CAAAC,cAAA,cAAqB,aAEoB;IAArCD,EAAA,CAAAI,UAAA,mBAAAkD,wDAAAxB,MAAA;MAAA9B,EAAA,CAAAM,aAAA,CAAA4C,GAAA;MAAA,MAAA1C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA6C,OAAA,CAAQ,SAAS,EAAAvB,MAAA,CAAS;IAAA,EAAC;IACpC9B,EAAA,CAAAE,MAAA,sBACF;IAEJF,EAFI,CAAAG,YAAA,EAAI,EACD,EACF;IACJH,EAAA,CAAAC,cAAA,eAAyE;IAExED,EAAA,CAAA2C,UAAA,KAAAY,6CAAA,qBAA8H;IAIlIvD,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAC,cAAA,eAAuB;IAwCrBD,EAtCA,CAAA2C,UAAA,KAAAa,mDAAA,6BAA0D,KAAAC,mDAAA,2BAsCX;IAkEnDzD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAnJgCH,EAAA,CAAAc,SAAA,GAAqF;IAArFd,EAAA,CAAA0D,kBAAA,eAAAlD,MAAA,CAAAQ,OAAA,CAAA2C,qBAAA,eAAAnD,MAAA,CAAAQ,OAAA,CAAA4C,WAAA,WAAqF;IACvF5D,EAAA,CAAAc,SAAA,EAA0C;IAA1Cd,EAAA,CAAAsC,UAAA,YAAA9B,MAAA,CAAAqD,cAAA,CAAArD,MAAA,CAAAQ,OAAA,CAAA8C,MAAA,EAA0C;IAAC9D,EAAA,CAAAc,SAAA,EAAiC;IAAjCd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAA8C,MAAA,cAAiC;IAmB5C9D,EAAA,CAAAc,SAAA,GAAiD;IAAjDd,EAAA,CAAAsC,UAAA,YAAAtC,EAAA,CAAA+D,eAAA,IAAAC,GAAA,EAAAxD,MAAA,CAAAyD,WAAA,gBAAiD;IAMjDjE,EAAA,CAAAc,SAAA,GAAiD;IAAjDd,EAAA,CAAAsC,UAAA,YAAAtC,EAAA,CAAA+D,eAAA,KAAAC,GAAA,EAAAxD,MAAA,CAAAyD,WAAA,gBAAiD;IAQvCjE,EAAA,CAAAc,SAAA,GAA+B;IAA/Bd,EAAA,CAAAsC,UAAA,SAAA9B,MAAA,CAAAyD,WAAA,eAA+B;IAS1FjE,EAAA,CAAAc,SAAA,GAAyC;IAAzCd,EAAA,CAAAsC,UAAA,SAAA9B,MAAA,CAAAyD,WAAA,iBAAAzD,MAAA,CAAAQ,OAAA,CAAyC;IAsCzChB,EAAA,CAAAc,SAAA,EAA8B;IAA9Bd,EAAA,CAAAsC,UAAA,SAAA9B,MAAA,CAAAyD,WAAA,cAA8B;;;ADrFnD,OAAM,MAAOC,oBAAoB;EAUrBC,KAAA;EACAC,MAAA;EACAC,GAAA;EACAC,UAAA;EACAC,eAAA;EACAC,cAAA;EACAC,YAAA;EAfHC,SAAS,GAAkB,IAAI;EAC/B1D,OAAO,GAAQ,IAAI;EACnBwB,SAAS,GAAY,KAAK;EAC1ByB,WAAW,GAAW,SAAS;EAC/BpB,cAAc,GAAU,EAAE;EAC1B8B,SAAS,GAAQ,EAAE;EAClBC,iBAAiB,GAAiB,IAAI/E,YAAY,EAAE;EAE5DgF,YACUV,KAAqB,EACrBC,MAAc,EACdC,GAAsB,EACtBC,UAAsB,EACtBC,eAAgC,EAChCC,cAA8B,EAC9BC,YAAsB;IANtB,KAAAN,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;EACnB;EAEHK,QAAQA,CAAA;IACN,IAAI,CAACH,SAAS,GAAG,IAAI,CAACL,UAAU,CAACS,eAAe,EAAE;IAElD;IACA,IAAI,CAACH,iBAAiB,GAAG9E,aAAa,CAAC,CACrC,IAAI,CAACqE,KAAK,CAACa,QAAQ,EACnB,IAAI,CAACb,KAAK,CAACc,WAAW,CACvB,CAAC,CAACC,SAAS,CAAC,CAAC,CAACF,QAAQ,EAAEC,WAAW,CAAC,KAAI;MACvC,MAAME,OAAO,GAAGH,QAAQ,CAACI,GAAG,CAAC,IAAI,CAAC;MAClC,IAAI,CAACV,SAAS,GAAGS,OAAO,GAAGE,MAAM,CAACF,OAAO,CAAC,GAAG,IAAI;MAEjDG,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;QAAEb,SAAS,EAAE,IAAI,CAACA,SAAS;QAAEO;MAAW,CAAE,CAAC;MAE1F;MACA,MAAMO,SAAS,GAAGP,WAAW,CAAC,WAAW,CAAC;MAC1C,IAAIO,SAAS,KAAKA,SAAS,KAAK,SAAS,IAAIA,SAAS,KAAK,SAAS,CAAC,EAAE;QACrE,IAAI,CAACvB,WAAW,GAAGuB,SAAS;QAC5BF,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEC,SAAS,CAAC;MAClE,CAAC,MAAM;QACLF,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAACtB,WAAW,CAAC;MAC7E;MAEA,IAAI,IAAI,CAACS,SAAS,EAAE;QAClB,IAAI,CAACe,mBAAmB,EAAE;QAC1B,IAAI,CAACC,mBAAmB,EAAE;MAC5B;MAEA,IAAI,CAACrB,GAAG,CAACsB,YAAY,EAAE;IACzB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAAChB,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACiB,WAAW,EAAE;IACtC;EACF;EAEOJ,mBAAmBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACf,SAAS,EAAE;MAAE;IAAQ;IAC/B,IAAI,CAAClC,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC+B,eAAe,CAACuB,UAAU,CAAC;MAAEpB,SAAS,EAAE,IAAI,CAACA;IAAS,CAAE,CAAC,CAACQ,SAAS,CAAC;MACvEa,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACxD,SAAS,GAAG,KAAK;QACtB8C,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAES,GAAG,CAAC;QACzC,IAAI,CAACA,GAAG,EAAEC,OAAO,EAAE;UACjB;UACA,IAAI,CAACjF,OAAO,GAAGgF,GAAG,CAACE,YAAY,EAAEC,OAAO,IAAIH,GAAG,CAACE,YAAY,EAAEE,IAAI,IAAIJ,GAAG,CAACE,YAAY,IAAI,IAAI;UAC9FZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACvE,OAAO,CAAC;UACnDsE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEc,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtF,OAAO,IAAI,EAAE,CAAC,CAAC;UACzE;QACF,CAAC,MAAM;UACLsE,OAAO,CAACiB,KAAK,CAAC,qBAAqB,EAAEP,GAAG,CAACQ,YAAY,CAAC;UACtD,IAAI,CAACxF,OAAO,GAAG,IAAI;QACrB;QACA,IAAI,CAACqD,GAAG,CAACsB,YAAY,EAAE;MACzB,CAAC;MACDY,KAAK,EAAGE,GAAG,IAAI;QACb,IAAI,CAACjE,SAAS,GAAG,KAAK;QACtB8C,OAAO,CAACiB,KAAK,CAAC,iCAAiC,EAAEE,GAAG,CAAC;QACrD,IAAI,CAACpC,GAAG,CAACsB,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOD,mBAAmBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAChB,SAAS,EAAE;MAAE;IAAQ;IAC/B,IAAI,CAAClC,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACgC,cAAc,CAACkC,sBAAsB,CAAC;MACzCC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE;QACNC,KAAK,EAAE,KAAK;QACZC,OAAO,EAAE,CACP;UACEC,KAAK,EAAE,WAAW;UAClBC,QAAQ,EAAE,IAAI;UACdjF,KAAK,EAAE,IAAI,CAACyC;SACb;OAEJ;MACDyC,MAAM,EAAE,EAAE;MACVC,cAAc,EAAE,IAAI,CAACzC,SAAS,CAAC0C;KAChC,CAAC,CAACnC,SAAS,CAAC;MACXa,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACxD,SAAS,GAAG,KAAK;QACtB8C,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAES,GAAG,CAAC;QACjD,IAAIA,GAAG,EAAEC,OAAO,EAAE;UAChBX,OAAO,CAACiB,KAAK,CAAC,iCAAiC,EAAEP,GAAG,CAACQ,YAAY,CAAC;UAClE,IAAI,CAAC3D,cAAc,GAAG,EAAE;QAC1B,CAAC,MAAM;UACL,MAAMyE,UAAU,GAAGtB,GAAG,CAACE,YAAY,EAAEE,IAAI,IAAIJ,GAAG,CAACI,IAAI,IAAI,EAAE;UAC3D;UACA,IAAI,CAACvD,cAAc,GAAG,CAACyE,UAAU,IAAI,EAAE,EAAER,MAAM,CAAES,CAAM,IAAI;YACzD,MAAMC,eAAe,GAAGD,CAAC,EAAE7C,SAAS,IAAI6C,CAAC,EAAEE,SAAS,IAAIF,CAAC,EAAEG,UAAU,IAAIH,CAAC,EAAEI,SAAS,IAAIJ,CAAC,EAAEK,SAAS;YACrG,OAAO,IAAI,CAAClD,SAAS,IAAI,IAAI,GAAGW,MAAM,CAACmC,eAAe,CAAC,KAAKnC,MAAM,CAAC,IAAI,CAACX,SAAS,CAAC,GAAG,IAAI;UAC3F,CAAC,CAAC;UACFY,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAC1C,cAAc,CAAC;QAC1E;QACA,IAAI,CAACwB,GAAG,CAACsB,YAAY,EAAE;MACzB,CAAC;MACDY,KAAK,EAAGE,GAAQ,IAAI;QAClB,IAAI,CAACjE,SAAS,GAAG,KAAK;QACtB8C,OAAO,CAACiB,KAAK,CAAC,gCAAgC,EAAEE,GAAG,CAAC;QACpD,IAAI,CAAC5D,cAAc,GAAG,EAAE;QACxB,IAAI,CAACwB,GAAG,CAACsB,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOxC,MAAMA,CAAA;IACX,IAAI,CAACiB,MAAM,CAACyD,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEOlH,WAAWA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAC+D,SAAS,EAAE;MAAE;IAAQ;IAE/B,MAAMoD,eAAe,GAKjB;MACFC,IAAI,EAAE,IAAI;MAAE;MACZC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMC,QAAQ,GAAG,IAAI,CAAC1D,YAAY,CAAC2D,IAAI,CACrCrI,qBAAqB,EACrB+H,eAAe,CAChB;IAED;IACAK,QAAQ,CAACE,iBAAiB,CAACC,EAAE,GAAG,IAAI,CAAC5D,SAAS;IAC9CyD,QAAQ,CAACE,iBAAiB,CAACrH,OAAO,GAAG,IAAI,CAACA,OAAO;IAEjD;IACAmH,QAAQ,CAACI,MAAM,CAACC,IAAI,CACjBD,MAAM,IAAI;MACT;MACA,IAAIA,MAAM,EAAE;QACVjD,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgD,MAAM,CAAC;QACnD;QACA,IAAI,CAAC9C,mBAAmB,EAAE;MAC5B;IACF,CAAC,EACAgD,MAAM,IAAI;MACT;MACAnD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEkD,MAAM,CAAC;IACzC,CAAC,CACF;EACH;EAEO9G,UAAUA,CAACC,QAAgB;IAChC,IAAI,CAACwC,MAAM,CAACyD,QAAQ,CAAC,CAAC,eAAe,EAAEjG,QAAQ,CAAC,EAAE;MAChDqD,WAAW,EAAE;QAAEyD,IAAI,EAAE,SAAS;QAAEhE,SAAS,EAAE,IAAI,CAACA;MAAS;KAC1D,CAAC;EACJ;EAEO3C,cAAcA,CAAC4G,MAAW,EAAEC,SAAiB;IAClD,IAAI,CAACD,MAAM,EAAE/G,QAAQ,IAAI,CAACgH,SAAS,EAAE;MAAE;IAAQ;IAC/C,MAAMC,OAAO,GAAG,CACd,UAAU,EACV,uBAAuB,EACvB,cAAc,EACd,SAAS,EACT,cAAc,EACd,WAAW,EACX,mBAAmB,CACpB;IACD,IAAI,CAACA,OAAO,CAACC,QAAQ,CAACF,SAAS,CAAC,EAAE;MAAE;IAAQ;IAE5C,MAAMG,QAAQ,GAAGJ,MAAM,CAACpG,oBAAoB;IAC5CoG,MAAM,CAACpG,oBAAoB,GAAGqG,SAAS;IACvC,IAAI,CAACpG,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC6B,GAAG,CAACsB,YAAY,EAAE;IAEvB,IAAI,CAACnB,cAAc,CAChBwE,gCAAgC,CAAC;MAAEpH,QAAQ,EAAE+G,MAAM,CAAC/G,QAAQ;MAAEW,oBAAoB,EAAEqG;IAAS,CAAE,CAAC,CAChG1D,SAAS,CAAC;MACTa,IAAI,EAAGC,GAAQ,IAAI;QACjB,MAAMC,OAAO,GAAGD,GAAG,EAAEC,OAAO,IAAID,GAAG,EAAEE,YAAY,EAAED,OAAO;QAC1D,IAAIA,OAAO,EAAE;UACX0C,MAAM,CAACpG,oBAAoB,GAAGwG,QAAQ;UACtC,IAAI,CAACvG,SAAS,GAAG,KAAK;UACtB,IAAI,CAAC6B,GAAG,CAACsB,YAAY,EAAE;QACzB;QACA,IAAI,CAACnD,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC6B,GAAG,CAACsB,YAAY,EAAE;MACzB,CAAC;MACDY,KAAK,EAAEA,CAAA,KAAK;QACVoC,MAAM,CAACpG,oBAAoB,GAAGwG,QAAQ;QACtC,IAAI,CAACvG,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC6B,GAAG,CAACsB,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEO9B,cAAcA,CAACC,MAAc;IAClC,IAAI,CAACA,MAAM,EAAE,OAAO,YAAY;IAChC,MAAMmF,GAAG,GAAGnF,MAAM,CAACoF,WAAW,EAAE;IAChC,IAAID,GAAG,KAAK,SAAS,EAAE,OAAO,eAAe;IAC7C,IAAIA,GAAG,KAAK,WAAW,EAAE,OAAO,kBAAkB;IAClD,IAAIA,GAAG,KAAK,sBAAsB,EAAE,OAAO,kBAAkB;IAC7D,IAAIA,GAAG,KAAK,mBAAmB,EAAE,OAAO,kBAAkB;IAC1D,OAAO,SAAS,GAAGnF,MAAM,CAACoF,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EAClF;EAEA9F,OAAOA,CAAC+F,GAAW,EAAEtH,MAAW;IAC9B,IAAI,CAACmC,WAAW,GAAGmF,GAAG;IACtB,IAAI,CAAC/E,GAAG,CAACsB,YAAY,EAAE;EACzB;;qCA3OWzB,oBAAoB,EAAAlE,EAAA,CAAAqJ,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAvJ,EAAA,CAAAqJ,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAxJ,EAAA,CAAAqJ,iBAAA,CAAArJ,EAAA,CAAAyJ,iBAAA,GAAAzJ,EAAA,CAAAqJ,iBAAA,CAAAK,EAAA,CAAAC,UAAA,GAAA3J,EAAA,CAAAqJ,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAA7J,EAAA,CAAAqJ,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAA/J,EAAA,CAAAqJ,iBAAA,CAAAW,EAAA,CAAAC,QAAA;EAAA;;UAApB/F,oBAAoB;IAAAgG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCbjCxK,EAAA,CAAA2C,UAAA,IAAA+H,mCAAA,iBAA0D;QAS1D1K,EAAA,CAAAC,cAAA,aAAoC;QAElCD,EAAA,CAAA2C,UAAA,IAAAgI,mCAAA,mBAAsD;QA0JxD3K,EAAA,CAAAG,YAAA,EAAM;;;QArKAH,EAAA,CAAAsC,UAAA,SAAAmI,GAAA,CAAAjI,SAAA,CAAe;QAWoBxC,EAAA,CAAAc,SAAA,GAAa;QAAbd,EAAA,CAAAsC,UAAA,SAAAmI,GAAA,CAAAzJ,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}