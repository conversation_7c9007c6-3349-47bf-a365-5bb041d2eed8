{"ast": null, "code": "import _asyncToGenerator from \"D:/permittracker/Angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { NavigationStart } from '@angular/router';\nimport { AddUserComponent } from '../add-user/user-add.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/user.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"src/app/modules/services/app.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../services/http-utils.service\";\nimport * as i7 from \"../../services/kendo-column.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@progress/kendo-angular-grid\";\nimport * as i11 from \"@progress/kendo-angular-inputs\";\nimport * as i12 from \"@progress/kendo-angular-buttons\";\nimport * as i13 from \"ng-inline-svg-2\";\nimport * as i14 from \"@progress/kendo-angular-dropdowns\";\nconst _c0 = [\"normalGrid\"];\nconst _c1 = () => [10, 15, 20, 50, 100];\nconst _c2 = a0 => ({\n  pageSizes: a0,\n  previousNext: true,\n  info: true,\n  type: \"numeric\",\n  buttonCount: 5\n});\nconst _c3 = () => ({\n  allowUnsort: true,\n  mode: \"single\"\n});\nconst _c4 = () => ({\n  checkboxOnly: true,\n  mode: \"multiple\"\n});\nconst _c5 = () => ({\n  filter: true\n});\nconst _c6 = () => ({\n  \"background-color\": \"#efefef !important\"\n});\nconst _c7 = () => ({\n  \"background-color\": \"#edf0f3\",\n  \"font-weight\": \"600\"\n});\nfunction UserListComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"div\", 9)(3, \"span\", 10);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 11);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction UserListComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"kendo-textbox\", 13);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function UserListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchData, $event) || (ctx_r2.searchData = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown\", function UserListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchKeyDown($event));\n    })(\"ngModelChange\", function UserListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchChange());\n    })(\"clear\", function UserListComponent_ng_template_4_Template_kendo_textbox_clear_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clearSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(2, \"kendo-grid-spacer\");\n    i0.ɵɵelementStart(3, \"div\", 14)(4, \"span\", 15);\n    i0.ɵɵtext(5, \"Total: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 16);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_4_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.add());\n    });\n    i0.ɵɵelement(9, \"span\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_4_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleExpand());\n    });\n    i0.ɵɵelement(11, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_4_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.resetTable());\n    });\n    i0.ɵɵelement(13, \"i\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_4_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.refreshGrid());\n    });\n    i0.ɵɵelement(15, \"i\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", 500, \"px\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchData);\n    i0.ɵɵproperty(\"clearButton\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.page.totalElements || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/arrows/arr075.svg\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"fa-expand\", !ctx_r2.isExpanded)(\"fa-compress\", ctx_r2.isExpanded);\n  }\n}\nfunction UserListComponent_ng_template_5_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28)(3, \"label\", 29);\n    i0.ɵɵtext(4, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"kendo-dropdownlist\", 30);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function UserListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 28)(7, \"label\", 29);\n    i0.ɵɵtext(8, \"Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"kendo-dropdownlist\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function UserListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.role, $event) || (ctx_r2.appliedFilters.role = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 32)(11, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_5_div_0_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyAdvancedFilters());\n    });\n    i0.ɵɵelement(12, \"i\", 34);\n    i0.ɵɵtext(13, \" Apply Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_5_div_0_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearAllFilters());\n    });\n    i0.ɵɵelement(15, \"i\", 36);\n    i0.ɵɵtext(16, \" Clear \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.status);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.roles);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.role);\n  }\n}\nfunction UserListComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, UserListComponent_ng_template_5_div_0_Template, 17, 4, \"div\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showAdvancedFilters);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 50);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_2_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const dataItem_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.unlockUser(dataItem_r6));\n    });\n    i0.ɵɵelement(1, \"span\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen037.svg\");\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 47);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template_a_click_0_listener() {\n      const dataItem_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.edit(dataItem_r6.userId));\n    });\n    i0.ɵɵelement(1, \"span\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_2_Template, 2, 1, \"a\", 49);\n  }\n  if (rf & 2) {\n    const dataItem_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen055.svg\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", dataItem_r6.IsLocked);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 45);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template, 3, 2, \"ng-template\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(9, _c6));\n    i0.ɵɵproperty(\"width\", 125)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"action\"))(\"headerStyle\", i0.ɵɵpureFunction0(10, _c7))(\"includeInChooser\", false)(\"columnMenu\", false)(\"hidden\", ctx_r2.getHiddenField(\"action\"));\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 54);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r8.userFullName, \" \");\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 55);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r9 = ctx.$implicit;\n    const column_r10 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r10)(\"filter\", filter_r9)(\"extra\", false);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 52);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template, 3, 1, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template, 2, 3, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 150)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"userFullName\"))(\"headerStyle\", i0.ɵɵpureFunction0(7, _c7))(\"includeInChooser\", false)(\"hidden\", ctx_r2.getHiddenField(\"userFullName\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"span\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r11 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", dataItem_r11.email || \"-\", i0.ɵɵsanitizeHtml);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 55);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r12 = ctx.$implicit;\n    const column_r13 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r13)(\"filter\", filter_r12)(\"extra\", false);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 56);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template, 2, 1, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template, 2, 3, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 250)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"email\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"email\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"span\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r14 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", dataItem_r14.title || \"-\", i0.ɵɵsanitizeHtml);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 59);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r15 = ctx.$implicit;\n    const column_r16 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r16)(\"filter\", filter_r15)(\"extra\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 58);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template, 2, 1, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template, 6, 3, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 120)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"title\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"title\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"span\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r17 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", dataItem_r17.phoneNo || \"-\", i0.ɵɵsanitizeHtml);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 55);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r18 = ctx.$implicit;\n    const column_r19 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r19)(\"filter\", filter_r18)(\"extra\", false);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 60);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template, 2, 1, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template, 2, 3, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 120)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"phoneNo\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"phoneNo\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_6_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"span\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r20 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", dataItem_r20.roleName || \"-\", i0.ɵɵsanitizeHtml);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_6_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 55);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r21 = ctx.$implicit;\n    const column_r22 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r22)(\"filter\", filter_r21)(\"extra\", false);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 61);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_6_ng_template_1_Template, 2, 1, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_6_ng_template_2_Template, 2, 3, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 120)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"roleName\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"roleName\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 65);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen037.svg\");\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 66);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen040.svg\");\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_span_0_Template, 1, 1, \"span\", 63)(1, UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_span_1_Template, 1, 1, \"span\", 64);\n  }\n  if (rf & 2) {\n    const dataItem_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", dataItem_r23.userStatus === \"Active\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", dataItem_r23.userStatus === \"Inactive\");\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"kendo-dropdownlist\", 67);\n    i0.ɵɵlistener(\"valueChange\", function UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener($event) {\n      const ctx_r24 = i0.ɵɵrestoreView(_r24);\n      const filter_r26 = ctx_r24.$implicit;\n      const column_r27 = ctx_r24.column;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onStatusFilterChange($event, filter_r26, column_r27));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r26 = ctx.$implicit;\n    const column_r27 = ctx.column;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"data\", ctx_r2.filterOptions)(\"value\", ctx_r2.getFilterValue(filter_r26, column_r27));\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 62);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_Template, 2, 2, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template, 1, 2, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 100)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"userStatus\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"userStatus\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_8_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"br\");\n    i0.ɵɵelementStart(4, \"span\", 69);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r28 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 2, dataItem_r28.lastUpdatedDate, \"MM/dd/yyyy hh:mm a\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(dataItem_r28.lastUpdatedByFullName);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_8_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 70);\n    i0.ɵɵelement(1, \"kendo-filter-eq-operator\")(2, \"kendo-filter-neq-operator\")(3, \"kendo-filter-before-operator\")(4, \"kendo-filter-before-eq-operator\")(5, \"kendo-filter-after-operator\")(6, \"kendo-filter-after-eq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r29 = ctx.$implicit;\n    const column_r30 = ctx.column;\n    const filterService_r31 = ctx.filterService;\n    i0.ɵɵproperty(\"column\", column_r30)(\"filter\", filter_r29)(\"filterService\", filterService_r31);\n  }\n}\nfunction UserListComponent_ng_container_6_kendo_grid_column_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 68);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_8_ng_template_1_Template, 6, 5, \"ng-template\", 46)(2, UserListComponent_ng_container_6_kendo_grid_column_8_ng_template_2_Template, 7, 3, \"ng-template\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 160)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"lastUpdatedDate\"))(\"headerStyle\", i0.ɵɵpureFunction0(7, _c7))(\"maxResizableWidth\", 240)(\"hidden\", ctx_r2.getHiddenField(\"lastUpdatedDate\"))(\"filterable\", true);\n  }\n}\nfunction UserListComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, UserListComponent_ng_container_6_kendo_grid_column_1_Template, 2, 11, \"kendo-grid-column\", 37)(2, UserListComponent_ng_container_6_kendo_grid_column_2_Template, 3, 8, \"kendo-grid-column\", 38)(3, UserListComponent_ng_container_6_kendo_grid_column_3_Template, 3, 7, \"kendo-grid-column\", 39)(4, UserListComponent_ng_container_6_kendo_grid_column_4_Template, 3, 7, \"kendo-grid-column\", 40)(5, UserListComponent_ng_container_6_kendo_grid_column_5_Template, 3, 7, \"kendo-grid-column\", 41)(6, UserListComponent_ng_container_6_kendo_grid_column_6_Template, 3, 7, \"kendo-grid-column\", 42)(7, UserListComponent_ng_container_6_kendo_grid_column_7_Template, 3, 7, \"kendo-grid-column\", 43)(8, UserListComponent_ng_container_6_kendo_grid_column_8_Template, 3, 8, \"kendo-grid-column\", 44);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const column_r32 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"userFullName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"email\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"title\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"phoneNo\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"roleName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"userStatus\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r32 === \"lastUpdatedDate\");\n  }\n}\nfunction UserListComponent_ng_template_7_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 73);\n    i0.ɵɵelement(2, \"i\", 74);\n    i0.ɵɵelementStart(3, \"p\", 15);\n    i0.ɵɵtext(4, \"No users found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ng_template_7_div_0_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.loadTable());\n    });\n    i0.ɵɵelement(6, \"i\", 76);\n    i0.ɵɵtext(7, \"Refresh \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction UserListComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, UserListComponent_ng_template_7_div_0_Template, 8, 0, \"div\", 71);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading && !ctx_r2.isLoading);\n  }\n}\nexport class UserListComponent {\n  usersService;\n  cdr;\n  router;\n  route;\n  modalService;\n  AppService;\n  customLayoutUtilsService;\n  httpUtilService;\n  kendoColumnService;\n  grid;\n  // Data\n  serverSideRowData = [];\n  gridData = [];\n  IsListHasValue = false;\n  loading = false;\n  isLoading = false;\n  loginUser = {};\n  // Search\n  searchData = '';\n  searchTerms = new Subject();\n  searchSubscription;\n  // Enhanced Filters for Kendo UI\n  filter = {\n    logic: 'and',\n    filters: []\n  };\n  gridFilter = {\n    logic: 'and',\n    filters: []\n  };\n  activeFilters = [];\n  filterOptions = [{\n    text: 'All',\n    value: null\n  }, {\n    text: 'Active',\n    value: 'Active'\n  }, {\n    text: 'Inactive',\n    value: 'Inactive'\n  }];\n  // Advanced filter options\n  advancedFilterOptions = {\n    status: [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Active',\n      value: 'Active'\n    }, {\n      text: 'Inactive',\n      value: 'Inactive'\n    }],\n    roles: [] // Will be populated from backend\n  };\n  // Filter state\n  showAdvancedFilters = false;\n  appliedFilters = {};\n  // NEW COLUMN VISIBILITY SYSTEM - replacing the old one\n  kendoHide;\n  hiddenData = [];\n  kendoColOrder = [];\n  kendoInitColOrder = [];\n  hiddenFields = [];\n  // Column configuration for the new system\n  gridColumns = [];\n  defaultColumns = [];\n  fixedColumns = [];\n  draggableColumns = [];\n  normalGrid;\n  expandedGrid;\n  isExpanded = false;\n  // Enhanced Columns with Kendo UI features\n  gridColumnConfig = [{\n    field: 'action',\n    title: 'Action',\n    width: 80,\n    isFixed: true,\n    type: 'action',\n    order: 1\n  }, {\n    field: 'userFullName',\n    title: 'Name',\n    width: 150,\n    isFixed: true,\n    type: 'text',\n    filterable: true,\n    order: 2\n  },\n  // { field: 'lastName', title: 'Last Name', width: 150, isFixed: true, type: 'text', filterable: true, order: 3 },\n  {\n    field: 'email',\n    title: 'Email',\n    width: 250,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 4\n  }, {\n    field: 'title',\n    title: 'Title',\n    width: 120,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 5\n  }, {\n    field: 'phoneNo',\n    title: 'Phone',\n    width: 120,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 6\n  }, {\n    field: 'roleName',\n    title: 'Role',\n    width: 120,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 7\n  }, {\n    field: 'Status',\n    title: 'Status',\n    width: 100,\n    type: 'status',\n    isFixed: false,\n    filterable: true,\n    order: 8\n  }, {\n    field: 'lastUpdatedDate',\n    title: 'Updated Date',\n    width: 160,\n    isFixed: false,\n    type: 'date',\n    filterable: true,\n    order: 9\n  }];\n  // OLD SYSTEM - to be removed\n  columnsVisibility = {};\n  // Old column configuration management removed - replaced with new system\n  // State\n  sort = [{\n    field: 'lastUpdatedDate',\n    dir: 'desc'\n  }];\n  // Remove custom sort state tracking → rely on Kendo native\n  // Router subscription for saving state on navigation\n  routerSubscription;\n  // Storage key for state persistence\n  GRID_STATE_KEY = 'form-templates-grid-state';\n  // Pagination\n  page = {\n    size: 10,\n    pageNumber: 0,\n    totalElements: 0,\n    totalPages: 0,\n    orderBy: 'lastUpdatedDate',\n    orderDir: 'desc'\n  };\n  skip = 0;\n  // Export options\n  exportOptions = [{\n    text: 'Export All',\n    value: 'all'\n  }, {\n    text: 'Export Selected',\n    value: 'selected'\n  }, {\n    text: 'Export Filtered',\n    value: 'filtered'\n  }];\n  // Selection state\n  selectedUsers = [];\n  isAllSelected = false;\n  // Statistics\n  userStatistics = {\n    activeUsers: 0,\n    inactiveUsers: 0,\n    suspendedUsers: 0,\n    lockedUsers: 0,\n    totalUsers: 0\n  };\n  // Bulk operations\n  showBulkActions = false;\n  bulkActionStatus = 'Active';\n  //add or edit default paramters\n  permissionArray = [];\n  constructor(usersService, cdr, router, route, modalService,\n  // Provides modal functionality to display modals\n  AppService, customLayoutUtilsService, httpUtilService, kendoColumnService) {\n    this.usersService = usersService;\n    this.cdr = cdr;\n    this.router = router;\n    this.route = route;\n    this.modalService = modalService;\n    this.AppService = AppService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.httpUtilService = httpUtilService;\n    this.kendoColumnService = kendoColumnService;\n  }\n  ngOnInit() {\n    this.loginUser = this.AppService.getLoggedInUser();\n    console.log('Login user loaded:', this.loginUser);\n    // Setup search with debounce\n    this.searchSubscription = this.searchTerms.pipe(debounceTime(500), distinctUntilChanged()).subscribe(searchTerm => {\n      console.log('Search triggered with term:', searchTerm);\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      // Set loading state for search\n      this.loading = true;\n      this.isLoading = true;\n      // Force change detection to show loader\n      this.cdr.detectChanges();\n      this.loadTable();\n    });\n    // Subscribe to router events to save state before navigation\n    this.routerSubscription = this.router.events.subscribe(event => {\n      if (event instanceof NavigationStart) {\n        this.saveGridState();\n      }\n    });\n    // Load saved state if available\n    this.loadGridState();\n    // Load roles for advanced filters\n    this.loadRoles();\n    // Load user statistics\n    this.loadUserStatistics();\n    // Initialize with default page load\n    this.onPageLoad();\n    // Initialize new column visibility system\n    this.initializeColumnVisibilitySystem();\n    // Load column configuration after a short delay to ensure loginUser is available\n    setTimeout(() => {\n      this.loadColumnConfigFromDatabase();\n    }, 100);\n  }\n  /**\n   * Initialize the new column visibility system\n   */\n  initializeColumnVisibilitySystem() {\n    // Initialize default columns\n    this.defaultColumns = this.gridColumnConfig.map(col => col.field);\n    this.gridColumns = [...this.defaultColumns];\n    // Set fixed columns (first 3 columns)\n    this.fixedColumns = ['action', 'FirstName', 'LastName'];\n    // Set draggable columns (all except fixed)\n    this.draggableColumns = this.defaultColumns.filter(col => !this.fixedColumns.includes(col));\n    // Initialize normal and expanded grid references\n    this.normalGrid = this.grid;\n    this.expandedGrid = this.grid;\n  }\n  ngAfterViewInit() {\n    // Load the table after the view is initialized\n    // Small delay to ensure the grid is properly rendered\n    setTimeout(() => {\n      this.loadTable();\n    }, 200);\n  }\n  // Method to handle when the component becomes visible\n  onTabActivated() {\n    // Set loading state for tab activation\n    this.loading = true;\n    this.isLoading = true;\n    // Refresh the data when the tab is activated\n    this.loadTable();\n    this.loadUserStatistics();\n  }\n  // Method to handle initial page load\n  onPageLoad() {\n    // Initialize the component with default data\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.sort = [{\n      field: 'LastUpdatedDate',\n      dir: 'desc'\n    }];\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.searchData = '';\n    // Set loading state for initial page load\n    this.loading = true;\n    this.isLoading = true;\n    // Load the data\n    this.loadTable();\n    this.loadUserStatistics();\n  }\n  // Refresh grid data - only refresh the grid with latest API call\n  refreshGrid() {\n    // Set loading state to show full-screen loader\n    this.loading = true;\n    this.isLoading = true;\n    // Reset to first page and clear any applied filters\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.gridFilter = {\n      logic: 'and',\n      filters: []\n    };\n    this.activeFilters = [];\n    this.appliedFilters = {};\n    // Clear search data\n    this.searchData = '';\n    // Load fresh data from API\n    this.loadTable();\n  }\n  // Old column configuration methods removed - replaced with new system\n  // Old column selector methods removed - replaced with new system\n  ngOnDestroy() {\n    // Clean up subscriptions\n    if (this.routerSubscription) {\n      this.routerSubscription.unsubscribe();\n    }\n    if (this.searchSubscription) {\n      this.searchSubscription.unsubscribe();\n    }\n    this.searchTerms.complete();\n  }\n  // New method to load data using Kendo UI specific endpoint\n  loadTableWithKendoEndpoint() {\n    this.loading = true;\n    this.isLoading = true;\n    // Enable loader\n    this.httpUtilService.loadingSubject.next(true);\n    // Force change detection to show loader\n    this.cdr.detectChanges();\n    // Prepare state object for Kendo UI endpoint\n    // When sort is empty (3rd click), send default sort to backend\n    const sortForBackend = this.sort.length > 0 ? this.sort : [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    const state = {\n      take: this.page.size,\n      skip: this.skip,\n      sort: sortForBackend,\n      filter: this.filter.filters,\n      search: this.searchData,\n      loggedInUserId: this.loginUser.userId\n    };\n    console.log('Loading table with search term:', this.searchData);\n    console.log('Full state object:', state);\n    console.log('Loading states - loading:', this.loading, 'isLoading:', this.isLoading);\n    this.usersService.getUsersForKendoGrid(state).subscribe({\n      next: data => {\n        // Handle the new API response structure\n        if (data.isFault || data.responseData && data.responseData.errors && data.responseData.errors.length > 0) {\n          const errors = data.responseData?.errors || data.errors || [];\n          console.error('Kendo UI Grid errors:', errors);\n          this.handleEmptyResponse();\n        } else {\n          // Handle both old and new response structures\n          const responseData = data.responseData || data;\n          const userData = responseData.data || [];\n          const total = responseData.total || 0;\n          this.IsListHasValue = userData.length !== 0;\n          this.serverSideRowData = userData;\n          this.gridData = this.serverSideRowData;\n          this.page.totalElements = total;\n          this.page.totalPages = Math.ceil(total / this.page.size);\n        }\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      error: error => {\n        console.error('Error loading data with Kendo UI endpoint:', error);\n        this.handleEmptyResponse();\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      complete: () => {\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  // Enhanced loadTable method that can use either endpoint\n  loadTable() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // Use the new Kendo UI specific endpoint for better performance\n      _this.loadTableWithKendoEndpoint();\n    })();\n  }\n  handleEmptyResponse() {\n    this.IsListHasValue = false;\n    this.serverSideRowData = [];\n    this.gridData = [];\n    this.page.totalElements = 0;\n    this.page.totalPages = 0;\n  }\n  // Enhanced search handling\n  clearSearch() {\n    // Clear search data and trigger search\n    this.searchData = '';\n    // Set loading state for clear search\n    this.loading = true;\n    this.isLoading = true;\n    this.searchTerms.next('');\n  }\n  // Clear all filters and search\n  clearAllFilters() {\n    this.searchData = '';\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.gridFilter = {\n      logic: 'and',\n      filters: []\n    };\n    this.activeFilters = [];\n    this.appliedFilters = {};\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    // Set loading state for clear all filters\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTable();\n  }\n  // Apply advanced filters\n  applyAdvancedFilters() {\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    // Set loading state for advanced filters\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTable();\n  }\n  // Toggle advanced filters panel\n  toggleAdvancedFilters() {\n    this.showAdvancedFilters = !this.showAdvancedFilters;\n  }\n  // Load roles for advanced filters\n  loadRoles() {\n    const queryParams = {\n      pageSize: 1000,\n      sortOrder: 'ASC',\n      sortField: 'roleName',\n      pageNumber: 0\n    };\n    this.usersService.getAllRoles(queryParams).subscribe({\n      next: data => {\n        if (data && data.responseData && data.responseData.content) {\n          this.advancedFilterOptions.roles = [{\n            text: 'All Roles',\n            value: null\n          }, ...data.responseData.content.map(role => ({\n            text: role.roleName,\n            value: role.roleName\n          }))];\n        }\n      },\n      error: error => {\n        console.error('Error loading roles:', error);\n        // Set default roles if loading fails\n        this.advancedFilterOptions.roles = [{\n          text: 'All Roles',\n          value: null\n        }];\n      }\n    });\n    this.usersService.getDefaultPermissions({}).subscribe(permissions => {\n      this.permissionArray = permissions.responseData;\n    });\n  }\n  // Load user statistics\n  loadUserStatistics() {\n    this.usersService.getUserStatistics().subscribe({\n      next: data => {\n        if (data && data.statistics) {\n          this.userStatistics = data.statistics;\n        }\n      },\n      error: error => {\n        console.error('Error loading user statistics:', error);\n      }\n    });\n  }\n  // Selection handling\n  onSelectionChange(selection) {\n    this.selectedUsers = selection.selectedRows || [];\n    this.isAllSelected = this.selectedUsers.length === this.serverSideRowData.length;\n    this.showBulkActions = this.selectedUsers.length > 0;\n  }\n  // Select all users\n  selectAllUsers() {\n    if (this.isAllSelected) {\n      this.selectedUsers = [];\n      this.isAllSelected = false;\n    } else {\n      this.selectedUsers = [...this.serverSideRowData];\n      this.isAllSelected = true;\n    }\n    this.showBulkActions = this.selectedUsers.length > 0;\n  }\n  // Delete user\n  deleteUser(user) {\n    if (confirm(`Are you sure you want to delete user ${user.FirstName} ${user.LastName}?`)) {\n      // Show loading state\n      this.loading = true;\n      this.isLoading = true;\n      const deleteData = {\n        userId: user.userId,\n        loggedInUserId: this.loginUser.userId || 0\n      };\n      this.usersService.deleteUser(deleteData).subscribe({\n        next: response => {\n          if (response && response.message) {\n            //alert(response.message);\n            this.customLayoutUtilsService.showSuccess(response.message, '');\n            this.loadTable(); // Reload the table\n            this.loadUserStatistics(); // Reload statistics\n          }\n        },\n        error: error => {\n          console.error('Error deleting user:', error);\n          this.customLayoutUtilsService.showError('Error deleting user', '');\n          //alert('Error deleting user. Please try again.');\n          // Reset loading state on error\n          this.loading = false;\n          this.isLoading = false;\n        }\n      });\n    }\n  }\n  // Bulk update user status\n  bulkUpdateUserStatus() {\n    if (this.selectedUsers.length === 0) {\n      //alert('Please select users to update.');\n      this.customLayoutUtilsService.showSuccess('Please select users to complete', '');\n      return;\n    }\n    if (confirm(`Are you sure you want to update ${this.selectedUsers.length} users to status: ${this.bulkActionStatus}?`)) {\n      // Show loading state\n      this.loading = true;\n      this.isLoading = true;\n      const bulkUpdateData = {\n        userIds: this.selectedUsers.map(user => user.userId),\n        status: this.bulkActionStatus,\n        loggedInUserId: this.loginUser.userId || 0\n      };\n      this.usersService.bulkUpdateUserStatus(bulkUpdateData).subscribe({\n        next: response => {\n          if (response && response.message) {\n            //alert(response.message);\n            this.loadTable(); // Reload the table\n            this.loadUserStatistics(); // Reload statistics\n            this.selectedUsers = []; // Clear selection\n            this.showBulkActions = false;\n          }\n        },\n        error: error => {\n          console.error('Error updating users:', error);\n          //alert('Error updating users. Please try again.');\n          this.customLayoutUtilsService.showError('Error updating users. Please try again', '');\n          // Reset loading state on error\n          this.loading = false;\n          this.isLoading = false;\n        }\n      });\n    }\n  }\n  // Unlock user\n  unlockUser(user) {\n    if (confirm(`Are you sure you want to unlock user ${user.firstName} ${user.lastName}?`)) {\n      // Show loading state\n      this.loading = true;\n      this.isLoading = true;\n      const unlockData = {\n        userId: user.userId,\n        loggedInUserId: this.loginUser.userId || 0\n      };\n      this.usersService.unlockUser(unlockData).subscribe({\n        next: response => {\n          if (response && response.message) {\n            this.customLayoutUtilsService.showSuccess(response.message, '');\n            //alert(response.message);\n            this.loadTable(); // Reload the table\n            this.loadUserStatistics(); // Reload statistics\n          }\n        },\n        error: error => {\n          console.error('Error unlocking user:', error);\n          this.customLayoutUtilsService.showError('Error unlocking user', '');\n          //alert('Error unlocking user. Please try again.');\n          // Reset loading state on error\n          this.loading = false;\n          this.isLoading = false;\n        }\n      });\n    }\n  }\n  onSearchKeyDown(event) {\n    console.log('Search keydown event:', event.key, 'Search data:', this.searchData);\n    if (event.key === 'Enter') {\n      // Trigger search immediately on Enter key\n      console.log('Triggering search on Enter key');\n      this.searchTerms.next(this.searchData || '');\n    }\n  }\n  // Handle search model changes\n  onSearchChange() {\n    // Trigger search when model changes with debouncing\n    console.log('Search model changed:', this.searchData);\n    console.log('Triggering search with debounce');\n    // Ensure search is triggered even for empty strings\n    this.searchTerms.next(this.searchData || '');\n  }\n  // Enhanced function to filter data from search and advanced filters\n  filterConfiguration() {\n    let filter = {\n      paginate: true,\n      search: '',\n      columnFilter: []\n    };\n    // Handle search text\n    let searchText;\n    if (this.searchData === null || this.searchData === undefined) {\n      searchText = '';\n    } else {\n      searchText = this.searchData;\n    }\n    filter.search = searchText.trim();\n    // Handle Kendo UI grid filters\n    if (this.activeFilters && this.activeFilters.length > 0) {\n      filter.columnFilter = [...this.activeFilters];\n    }\n    // Add advanced filters\n    if (this.appliedFilters.status && this.appliedFilters.status !== null) {\n      filter.columnFilter.push({\n        field: 'userStatus',\n        operator: 'eq',\n        value: this.appliedFilters.status\n      });\n    }\n    if (this.appliedFilters.role && this.appliedFilters.role !== null) {\n      filter.columnFilter.push({\n        field: 'roleName',\n        operator: 'eq',\n        value: this.appliedFilters.role\n      });\n    }\n    return filter;\n  }\n  // Grid event handlers\n  pageChange(event) {\n    this.skip = event.skip;\n    this.page.pageNumber = event.skip / event.take;\n    this.page.size = event.take;\n    // Set loading state for pagination\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTable();\n  }\n  onSortChange(sort) {\n    // Check if this is the 3rd click (dir is undefined)\n    const isThirdClick = sort.length > 0 && sort[0] && sort[0].dir === undefined;\n    if (isThirdClick) {\n      // 3rd click - clear sort and use default\n      this.sort = [];\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n    } else if (sort.length > 0 && sort[0] && sort[0].dir) {\n      // Valid sort with direction\n      this.sort = sort;\n      this.page.orderBy = sort[0].field || 'lastUpdatedDate';\n      this.page.orderDir = sort[0].dir;\n    } else {\n      // Empty sort array or invalid sort\n      this.sort = [];\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n    }\n    // Set loading state for sorting\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTable();\n  }\n  filterChange(filter) {\n    this.filter = filter;\n    this.gridFilter = filter;\n    this.activeFilters = this.flattenFilters(filter);\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    // Set loading state for filtering\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTable();\n  }\n  // Old column visibility methods removed - replaced with new system\n  // Fix 2: More robust getFilterValue method\n  getFilterValue(filter, column) {\n    if (!filter || !filter.filters || !column) {\n      return null;\n    }\n    const predicate = filter.filters.find(f => f && 'field' in f && f.field === column.field);\n    return predicate && 'value' in predicate ? predicate.value : null;\n  }\n  // Fix 3: More robust onStatusFilterChange method\n  onStatusFilterChange(value, filter, column) {\n    if (!filter || !filter.filters || !column) {\n      console.error('Invalid filter or column:', {\n        filter,\n        column\n      });\n      return;\n    }\n    const exists = filter.filters.findIndex(f => f && 'field' in f && f.field === column.field);\n    if (exists > -1) {\n      filter.filters.splice(exists, 1);\n    }\n    if (value !== null) {\n      filter.filters.push({\n        field: column.field,\n        operator: 'eq',\n        value: value\n      });\n    }\n    this.filterChange(filter);\n  }\n  // Fix 4: More robust flattenFilters method\n  flattenFilters(filter) {\n    const filters = [];\n    if (!filter || !filter.filters) {\n      return filters;\n    }\n    filter.filters.forEach(f => {\n      if (f && 'field' in f) {\n        // It's a FilterDescriptor\n        filters.push({\n          field: f.field,\n          operator: f.operator,\n          value: f.value\n        });\n      } else if (f && 'filters' in f) {\n        // It's a CompositeFilterDescriptor\n        filters.push(...this.flattenFilters(f));\n      }\n    });\n    return filters;\n  }\n  // Fix 5: More robust loadGridState method\n  loadGridState() {\n    try {\n      const savedState = localStorage.getItem(this.GRID_STATE_KEY);\n      if (!savedState) {\n        return;\n      }\n      const state = JSON.parse(savedState);\n      // Restore sort state\n      if (state && state.sort) {\n        this.sort = state.sort;\n        if (this.sort && this.sort.length > 0 && this.sort[0]) {\n          this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';\n          this.page.orderDir = this.sort[0].dir || 'desc';\n        }\n      }\n      // Restore filter state\n      if (state && state.filter) {\n        this.filter = state.filter;\n        this.gridFilter = state.filter;\n        this.activeFilters = state.activeFilters || [];\n      }\n      // Restore pagination state\n      if (state && state.page) {\n        this.page = state.page;\n      }\n      if (state && state.skip !== undefined) {\n        this.skip = state.skip;\n      }\n      // Restore column visibility\n      if (state && state.columnsVisibility) {\n        this.columnsVisibility = state.columnsVisibility;\n      }\n      // Restore search state\n      if (state && state.searchData) {\n        this.searchData = state.searchData;\n      }\n      // Restore advanced filter states\n      if (state && state.appliedFilters) {\n        this.appliedFilters = state.appliedFilters;\n      }\n      if (state && state.showAdvancedFilters !== undefined) {\n        this.showAdvancedFilters = state.showAdvancedFilters;\n      }\n    } catch (error) {\n      console.error('Error loading grid state:', error);\n      // If there's an error, use default state\n    }\n  }\n  // Old getHiddenField method removed - replaced with new system\n  // Grid state persistence methods\n  saveGridState() {\n    const state = {\n      sort: this.sort,\n      filter: this.filter,\n      page: this.page,\n      skip: this.skip,\n      columnsVisibility: this.columnsVisibility,\n      searchData: this.searchData,\n      activeFilters: this.activeFilters,\n      appliedFilters: this.appliedFilters,\n      showAdvancedFilters: this.showAdvancedFilters\n    };\n    localStorage.setItem(this.GRID_STATE_KEY, JSON.stringify(state));\n  }\n  // Function to add a new company (calls edit function with ID 0)\n  add() {\n    this.edit(0);\n  }\n  // Function to open the edit modal for adding/editing a company\n  edit(id) {\n    console.log('Line: 413', 'call edit function: ', id);\n    // Configuration options for the modal dialog\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the AddCompaniesComponent\n    const modalRef = this.modalService.open(AddUserComponent, NgbModalOptions);\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = id;\n    modalRef.componentInstance.defaultPermissions = this.permissionArray;\n    // Subscribe to the modal event when data is updated\n    modalRef.componentInstance.passEntry.subscribe(receivedEntry => {\n      if (receivedEntry === true) {\n        // Reload the table data after a successful update\n        this.loadTable();\n      }\n    });\n  }\n  deleteTemplate(item) {\n    console.log('Delete template:', item);\n    // Implement delete functionality\n  }\n  toggleExpand() {\n    // Find grid container element and toggle fullscreen class\n    const gridContainer = document.querySelector('.grid-container');\n    if (gridContainer) {\n      gridContainer.classList.toggle('fullscreen-grid');\n      this.isExpanded = !this.isExpanded;\n      // Refresh grid after resize to ensure proper rendering\n      if (this.grid) {\n        this.grid.refresh();\n      }\n    }\n  }\n  // Enhanced export functionality\n  onExportClick(event) {\n    switch (event.item.value) {\n      case 'all':\n        this.exportAllUsers();\n        break;\n      case 'selected':\n        this.exportSelectedUsers();\n        break;\n      case 'filtered':\n        this.exportFilteredUsers();\n        break;\n      default:\n        console.warn('Unknown export option:', event.item.value);\n    }\n  }\n  exportAllUsers() {\n    const exportParams = {\n      filters: {},\n      format: 'excel'\n    };\n    this.usersService.exportUsers(exportParams).subscribe({\n      next: response => {\n        if (response && response.exportData) {\n          this.downloadExcel(response.exportData, 'All_Users');\n        }\n      },\n      error: error => {\n        console.error('Error exporting users:', error);\n        this.customLayoutUtilsService.showError('Error exporting users', '');\n        //alert('Error exporting users. Please try again.');\n      }\n    });\n  }\n  exportSelectedUsers() {\n    if (this.selectedUsers.length === 0) {\n      this.customLayoutUtilsService.showError('Please select users to export', '');\n      //alert('Please select users to export.');\n      return;\n    }\n    const exportParams = {\n      filters: {\n        userIds: this.selectedUsers.map(user => user.UserId)\n      },\n      format: 'excel'\n    };\n    this.usersService.exportUsers(exportParams).subscribe({\n      next: response => {\n        if (response && response.exportData) {\n          this.downloadExcel(response.exportData, 'Selected_Users');\n        }\n      },\n      error: error => {\n        console.error('Error exporting selected users:', error);\n        this.customLayoutUtilsService.showError('Error exporting selected users', '');\n        //alert('Error exporting selected users. Please try again.');\n      }\n    });\n  }\n  exportFilteredUsers() {\n    const exportParams = {\n      filters: {\n        status: this.appliedFilters.status,\n        role: this.appliedFilters.role,\n        searchTerm: this.searchData\n      },\n      format: 'excel'\n    };\n    this.usersService.exportUsers(exportParams).subscribe({\n      next: response => {\n        if (response && response.exportData) {\n          this.downloadExcel(response.exportData, 'Filtered_Users');\n        }\n      },\n      error: error => {\n        console.error('Error exporting filtered users:', error);\n        this.customLayoutUtilsService.showError('Error exporting filtered users', '');\n        //alert('Error exporting filtered users. Please try again.');\n      }\n    });\n  }\n  downloadExcel(data, filename) {\n    // This would typically use a library like xlsx or similar\n    // For now, we'll create a simple CSV download\n    const csvContent = this.convertToCSV(data);\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n  convertToCSV(data) {\n    if (data.length === 0) return '';\n    const headers = Object.keys(data[0]);\n    const csvRows = [headers.join(',')];\n    for (const row of data) {\n      const values = headers.map(header => {\n        const value = row[header];\n        return typeof value === 'string' && value.includes(',') ? `\"${value}\"` : value;\n      });\n      csvRows.push(values.join(','));\n    }\n    return csvRows.join('\\n');\n  }\n  // NEW COLUMN VISIBILITY SYSTEM METHODS\n  /**\n   * Saves the current state of column visibility and order in the grid.\n   * This function categorizes columns into visible and hidden columns, records their titles,\n   * fields, and visibility status, and also captures the order of draggable columns.\n   * After gathering the necessary data, it sends this information to the backend for saving.\n   */\n  saveHead() {\n    // Check if loginUser is available\n    if (!this.loginUser || !this.loginUser.userId) {\n      console.error('loginUser not available:', this.loginUser);\n      this.customLayoutUtilsService.showError('User not logged in. Please refresh the page.', '');\n      return;\n    }\n    const nonHiddenColumns = [];\n    const hiddenColumns = [];\n    if (this.grid && this.grid.columns) {\n      this.grid.columns.forEach(column => {\n        if (!column.hidden) {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden\n          };\n          nonHiddenColumns.push(columnData);\n        } else {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden\n          };\n          hiddenColumns.push(columnData);\n        }\n      });\n    }\n    const draggableColumnsOrder = this.gridColumns.filter(col => !this.fixedColumns.includes(col)).map((field, index) => ({\n      field,\n      orderIndex: index\n    }));\n    // Prepare data for backend\n    const userData = {\n      pageName: 'Users',\n      userID: this.loginUser.userId,\n      hiddenData: hiddenColumns,\n      kendoColOrder: draggableColumnsOrder,\n      LoggedId: this.loginUser.userId\n    };\n    // Show loading state\n    this.httpUtilService.loadingSubject.next(true);\n    // Save to backend\n    this.kendoColumnService.createHideFields(userData).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!res.isFault) {\n          // Update local state\n          this.hiddenData = hiddenColumns;\n          this.kendoColOrder = draggableColumnsOrder;\n          this.hiddenFields = this.hiddenData.map(col => col.field);\n          // Also save to localStorage as backup\n          this.kendoColumnService.saveToLocalStorage(userData);\n          this.customLayoutUtilsService.showSuccess(res.message || 'Column settings saved successfully.', '');\n        } else {\n          this.customLayoutUtilsService.showError(res.message || 'Failed to save column settings.', '');\n        }\n        this.cdr.markForCheck();\n      },\n      error: error => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error saving column settings:', error);\n        // Fallback to localStorage on error\n        this.kendoColumnService.saveToLocalStorage(userData);\n        // Update local state\n        this.hiddenData = hiddenColumns;\n        this.kendoColOrder = draggableColumnsOrder;\n        this.hiddenFields = this.hiddenData.map(col => col.field);\n        this.customLayoutUtilsService.showError('Failed to save to server. Settings saved locally.', '');\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Reset the current state of column visibility and order in the grid to its original state.\n   * This function resets columns to default visibility and order, and saves the reset state.\n   */\n  resetTable() {\n    // Check if loginUser is available\n    if (!this.loginUser || !this.loginUser.userId) {\n      console.error('loginUser not available:', this.loginUser);\n      this.customLayoutUtilsService.showError('User not logged in. Please refresh the page and try again.', '');\n      return;\n    }\n    // Double-check authentication token\n    const token = this.AppService.getLocalStorageItem('permitToken', true);\n    if (!token) {\n      console.error('Authentication token not found');\n      this.customLayoutUtilsService.showError('Authentication token not found. Please login again.', '');\n      return;\n    }\n    // Reset all state variables\n    this.searchData = '';\n    this.activeFilters = [];\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.skip = 0;\n    this.page.pageNumber = 0;\n    this.gridColumns = [...this.defaultColumns];\n    // Reset sort state to default\n    this.sort = [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    this.page.orderBy = 'lastUpdatedDate';\n    this.page.orderDir = 'desc';\n    // Reset advanced filters\n    this.appliedFilters = {};\n    // Reset advanced filters visibility\n    this.showAdvancedFilters = false;\n    // Reset column order index\n    if (this.grid && this.grid.columns) {\n      this.grid.columns.forEach(column => {\n        const index = this.gridColumns.indexOf(column.field);\n        if (index !== -1) {\n          column.orderIndex = index;\n        }\n        // Reset column visibility - show all columns\n        if (column.field && column.field !== 'action') {\n          column.hidden = false;\n        }\n      });\n    }\n    // Clear hidden columns\n    this.hiddenData = [];\n    this.kendoColOrder = [];\n    this.hiddenFields = [];\n    // Reset the Kendo Grid's internal state\n    if (this.grid) {\n      // Clear all filters\n      this.grid.filter = {\n        logic: 'and',\n        filters: []\n      };\n      // Reset sorting\n      this.grid.sort = [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      // Reset to first page\n      this.grid.skip = 0;\n      this.grid.pageSize = this.page.size;\n    }\n    // Prepare reset data\n    const userData = {\n      pageName: 'Users',\n      userID: this.loginUser.userId,\n      hiddenData: [],\n      kendoColOrder: [],\n      LoggedId: this.loginUser.userId\n    };\n    // Only clear local settings; do not call server\n    this.kendoColumnService.clearFromLocalStorage('Users');\n    // Show loader and refresh grid\n    this.loading = true;\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n    this.cdr.detectChanges();\n    // Force grid refresh to apply all changes\n    if (this.grid) {\n      setTimeout(() => {\n        this.grid.refresh();\n        this.grid.reset();\n      }, 100);\n    }\n    this.loadTable();\n  }\n  /**\n   * Loads and applies the saved column order from the user preferences or configuration.\n   * This function updates the grid column order, ensuring the fixed columns remain in place\n   * and the draggable columns are ordered according to the saved preferences.\n   */\n  loadSavedColumnOrder(kendoColOrder) {\n    try {\n      const savedOrder = kendoColOrder;\n      if (savedOrder) {\n        const parsedOrder = savedOrder;\n        if (Array.isArray(parsedOrder) && parsedOrder.length > 0) {\n          // Get only the draggable columns from saved order\n          const savedDraggableColumns = parsedOrder.sort((a, b) => a.orderIndex - b.orderIndex).map(col => col.field).filter(field => !this.fixedColumns.includes(field));\n          // Add any missing draggable columns at the end\n          const missingColumns = this.draggableColumns.filter(col => !savedDraggableColumns.includes(col));\n          // Combine fixed columns with saved draggable columns\n          this.gridColumns = [...this.fixedColumns, ...savedDraggableColumns, ...missingColumns];\n        } else {\n          this.gridColumns = [...this.defaultColumns];\n        }\n      } else {\n        this.gridColumns = [...this.defaultColumns];\n      }\n    } catch (error) {\n      this.gridColumns = [...this.defaultColumns];\n    }\n  }\n  /**\n   * Checks if a given column is marked as hidden.\n   * This function searches the `hiddenFields` array to determine if the column should be hidden.\n   */\n  getHiddenField(columnName) {\n    return this.hiddenFields.indexOf(columnName) > -1;\n  }\n  /**\n   * Handles the column reordering event triggered when a column is moved by the user.\n   * The function checks if the column being moved is in the fixed columns and prevents reordering\n   * of fixed columns.\n   */\n  onColumnReorder(event) {\n    const {\n      columns,\n      newIndex,\n      oldIndex\n    } = event;\n    // Prevent reordering of fixed columns\n    if (this.fixedColumns.includes(columns[oldIndex].field) || this.fixedColumns.includes(columns[newIndex].field)) {\n      return;\n    }\n    // Update the gridColumns array\n    const reorderedColumns = [...this.gridColumns];\n    const [movedColumn] = reorderedColumns.splice(oldIndex, 1);\n    reorderedColumns.splice(newIndex, 0, movedColumn);\n    this.gridColumns = reorderedColumns;\n    this.cdr.markForCheck();\n  }\n  /**\n   * Handles column visibility changes from the Kendo Grid.\n   * Updates the local state when columns are shown or hidden.\n   */\n  updateColumnVisibility(event) {\n    if (this.isExpanded === false) {\n      if (this.grid && this.grid.columns) {\n        this.grid.columns.forEach(column => {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden\n          };\n          if (column.hidden) {\n            const exists = this.hiddenData.some(item => item.field === columnData.field && item.hidden === true);\n            if (!exists) {\n              this.hiddenData.push(columnData);\n            }\n          } else {\n            let indexExists = this.hiddenData.findIndex(item => item.field === columnData.field && item.hidden === true);\n            if (indexExists !== -1) {\n              this.hiddenData.splice(indexExists, 1);\n            }\n          }\n        });\n        this.hiddenFields = this.hiddenData.map(col => col.field);\n        this.cdr.markForCheck();\n      }\n    }\n  }\n  /**\n   * Loads the saved column configuration from the backend or localStorage as fallback.\n   * This method is called during component initialization to restore user preferences.\n   */\n  loadColumnConfigFromDatabase() {\n    try {\n      // First try to load from backend\n      if (this.loginUser && this.loginUser.userId) {\n        this.kendoColumnService.getHideFields({\n          pageName: 'Users',\n          userID: this.loginUser.userId\n        }).subscribe({\n          next: res => {\n            if (!res.isFault && res.Data) {\n              this.kendoHide = res.Data;\n              this.hiddenData = res.Data.hideData ? JSON.parse(res.Data.hideData) : [];\n              this.kendoInitColOrder = res.Data.kendoColOrder ? JSON.parse(res.Data.kendoColOrder) : [];\n              this.hiddenFields = this.hiddenData.map(col => col.field);\n              // Update grid columns based on the hidden fields\n              if (this.grid && this.grid.columns) {\n                this.grid.columns.forEach(column => {\n                  if (this.hiddenData.some(item => item.title === column.title && item.hidden)) {\n                    column.includeInChooser = true;\n                    column.hidden = true;\n                  } else {\n                    column.hidden = false;\n                  }\n                });\n              }\n              // Load saved column order and update grid\n              this.loadSavedColumnOrder(this.kendoInitColOrder);\n              // Also save to localStorage as backup\n              this.kendoColumnService.saveToLocalStorage({\n                pageName: 'Users',\n                userID: this.loginUser.userId,\n                hiddenData: this.hiddenData,\n                kendoColOrder: this.kendoInitColOrder\n              });\n            }\n          },\n          error: error => {\n            console.error('Error loading from backend, falling back to localStorage:', error);\n            this.loadFromLocalStorageFallback();\n          }\n        });\n      } else {\n        // Fallback to localStorage if no user ID\n        this.loadFromLocalStorageFallback();\n      }\n    } catch (error) {\n      console.error('Error loading column configuration:', error);\n      this.loadFromLocalStorageFallback();\n    }\n  }\n  /**\n   * Fallback method to load column configuration from localStorage\n   */\n  loadFromLocalStorageFallback() {\n    try {\n      const savedConfig = this.kendoColumnService.getFromLocalStorage('Users', this.loginUser?.UserId || 0);\n      if (savedConfig) {\n        this.kendoHide = savedConfig;\n        this.hiddenData = savedConfig.hiddenData || [];\n        this.kendoInitColOrder = savedConfig.kendoColOrder || [];\n        this.hiddenFields = this.hiddenData.map(col => col.field);\n        // Update grid columns based on the hidden fields\n        if (this.grid && this.grid.columns) {\n          this.grid.columns.forEach(column => {\n            if (this.hiddenData.some(item => item.title === column.title && item.hidden)) {\n              column.includeInChooser = true;\n              column.hidden = true;\n            } else {\n              column.hidden = false;\n            }\n          });\n        }\n        // Load saved column order and update grid\n        this.loadSavedColumnOrder(this.kendoInitColOrder);\n      }\n    } catch (error) {\n      console.error('Error loading from localStorage fallback:', error);\n    }\n  }\n  static ɵfac = function UserListComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || UserListComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.NgbModal), i0.ɵɵdirectiveInject(i4.AppService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.HttpUtilsService), i0.ɵɵdirectiveInject(i7.KendoColumnService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: UserListComponent,\n    selectors: [[\"app-user-list\"]],\n    viewQuery: function UserListComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n      }\n    },\n    decls: 8,\n    vars: 21,\n    consts: [[\"normalGrid\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"grid-container\"], [2, \"width\", \"auto\", \"overflow-x\", \"auto\", 3, \"columnReorder\", \"selectionChange\", \"filterChange\", \"pageChange\", \"sortChange\", \"columnVisibilityChange\", \"data\", \"pageSize\", \"sort\", \"pageable\", \"sortable\", \"groupable\", \"selectable\", \"reorderable\", \"resizable\", \"height\", \"skip\", \"filter\", \"columnMenu\"], [\"kendoGridToolbarTemplate\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"kendoGridNoRecordsTemplate\", \"\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"d-flex\", \"align-items-center\", \"me-3\", \"search-section\"], [\"placeholder\", \"Search...\", 3, \"ngModelChange\", \"keydown\", \"clear\", \"ngModel\", \"clearButton\"], [1, \"d-flex\", \"align-items-center\", \"me-3\"], [1, \"text-muted\"], [1, \"fw-bold\", \"ms-1\"], [\"type\", \"button\", \"title\", \"Add User\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", \"text-primary\", 3, \"inlineSVG\"], [\"type\", \"button\", \"title\", \"Toggle Grid Expansion\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"text-secondary\"], [\"type\", \"button\", \"title\", \"Reset to Default\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"text-warning\"], [\"type\", \"button\", \"title\", \"Refresh Grid Data\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\", \"text-info\"], [\"class\", \"advanced-filters-panel p-3 bg-light border-bottom\", 4, \"ngIf\"], [1, \"advanced-filters-panel\", \"p-3\", \"bg-light\", \"border-bottom\"], [1, \"row\"], [1, \"col-md-3\"], [1, \"form-label\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Status\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Role\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [1, \"col-md-3\", \"d-flex\", \"align-items-end\"], [\"kendoButton\", \"\", 1, \"btn-primary\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [\"kendoButton\", \"\", 1, \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"style\", \"hidden\", 4, \"ngIf\"], [\"field\", \"userFullName\", \"title\", \"Name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"email\", \"title\", \"Email\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"title\", \"title\", \"Title\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"phoneNo\", \"title\", \"Phone\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"roleName\", \"title\", \"Role\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"userStatus\", \"title\", \"Status\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", \"filter\", \"date\", \"format\", \"MM/dd/yyyy\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"maxResizableWidth\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"hidden\"], [\"kendoGridCellTemplate\", \"\"], [\"title\", \"Edit\", 1, \"btn\", \"btn-icon\", \"btn-sm\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-primary\", 3, \"inlineSVG\"], [\"title\", \"Unlock\", \"class\", \"btn btn-icon btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Unlock\", 1, \"btn\", \"btn-icon\", \"btn-sm\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-warning\", 3, \"inlineSVG\"], [\"field\", \"userFullName\", \"title\", \"Name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\"], [\"kendoGridFilterMenuTemplate\", \"\"], [1, \"fw-bolder\", \"cursor-pointer\"], [\"operator\", \"contains\", 3, \"column\", \"filter\", \"extra\"], [\"field\", \"email\", \"title\", \"Email\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [3, \"innerHTML\"], [\"field\", \"title\", \"title\", \"Title\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [3, \"column\", \"filter\", \"extra\"], [\"field\", \"phoneNo\", \"title\", \"Phone\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"roleName\", \"title\", \"Role\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"userStatus\", \"title\", \"Status\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"ngbTooltip\", \"Active\", \"class\", \"svg-icon svg-icon-3 svg-icon-success\", \"style\", \"margin-left: 1.5rem\", 3, \"inlineSVG\", 4, \"ngIf\"], [\"ngbTooltip\", \"Inactive\", \"class\", \"svg-icon svg-icon-3 svg-icon-danger text-danger\", \"style\", \"margin-left: 1.5rem\", 3, \"inlineSVG\", 4, \"ngIf\"], [\"ngbTooltip\", \"Active\", 1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-success\", 2, \"margin-left\", \"1.5rem\", 3, \"inlineSVG\"], [\"ngbTooltip\", \"Inactive\", 1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-danger\", \"text-danger\", 2, \"margin-left\", \"1.5rem\", 3, \"inlineSVG\"], [\"textField\", \"text\", \"valueField\", \"value\", 3, \"valueChange\", \"data\", \"value\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", \"filter\", \"date\", \"format\", \"MM/dd/yyyy\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"maxResizableWidth\", \"hidden\", \"filterable\"], [1, \"text-gray-600\", \"fs-1r\"], [\"operator\", \"eq\", 3, \"column\", \"filter\", \"filterService\"], [\"class\", \"custom-no-records\", 4, \"ngIf\"], [1, \"custom-no-records\"], [1, \"text-center\"], [1, \"fas\", \"fa-users\", \"text-muted\", \"mb-2\", 2, \"font-size\", \"2rem\"], [\"kendoButton\", \"\", 1, \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-refresh\", \"me-2\"]],\n    template: function UserListComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵtemplate(0, UserListComponent_div_0_Template, 7, 0, \"div\", 1);\n        i0.ɵɵelementStart(1, \"div\", 2)(2, \"kendo-grid\", 3, 0);\n        i0.ɵɵlistener(\"columnReorder\", function UserListComponent_Template_kendo_grid_columnReorder_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onColumnReorder($event));\n        })(\"selectionChange\", function UserListComponent_Template_kendo_grid_selectionChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSelectionChange($event));\n        })(\"filterChange\", function UserListComponent_Template_kendo_grid_filterChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.filterChange($event));\n        })(\"pageChange\", function UserListComponent_Template_kendo_grid_pageChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.pageChange($event));\n        })(\"sortChange\", function UserListComponent_Template_kendo_grid_sortChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSortChange($event));\n        })(\"columnVisibilityChange\", function UserListComponent_Template_kendo_grid_columnVisibilityChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.updateColumnVisibility($event));\n        });\n        i0.ɵɵtemplate(4, UserListComponent_ng_template_4_Template, 16, 10, \"ng-template\", 4)(5, UserListComponent_ng_template_5_Template, 1, 1, \"ng-template\", 4)(6, UserListComponent_ng_container_6_Template, 9, 8, \"ng-container\", 5)(7, UserListComponent_ng_template_7_Template, 1, 1, \"ng-template\", 6);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.loading || ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"data\", ctx.serverSideRowData)(\"pageSize\", ctx.page.size)(\"sort\", ctx.sort)(\"pageable\", i0.ɵɵpureFunction1(16, _c2, i0.ɵɵpureFunction0(15, _c1)))(\"sortable\", i0.ɵɵpureFunction0(18, _c3))(\"groupable\", false)(\"selectable\", i0.ɵɵpureFunction0(19, _c4))(\"reorderable\", true)(\"resizable\", false)(\"height\", 720)(\"skip\", ctx.page.pageNumber * ctx.page.size)(\"filter\", ctx.filter)(\"columnMenu\", i0.ɵɵpureFunction0(20, _c5));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.gridColumns);\n      }\n    },\n    dependencies: [i8.NgForOf, i8.NgIf, i9.NgControlStatus, i9.NgModel, i3.NgbTooltip, i10.GridComponent, i10.ToolbarTemplateDirective, i10.GridSpacerComponent, i10.ColumnComponent, i10.CellTemplateDirective, i10.NoRecordsTemplateDirective, i10.ContainsFilterOperatorComponent, i10.EndsWithFilterOperatorComponent, i10.EqualFilterOperatorComponent, i10.NotEqualFilterOperatorComponent, i10.StartsWithFilterOperatorComponent, i10.AfterFilterOperatorComponent, i10.AfterEqFilterOperatorComponent, i10.BeforeEqFilterOperatorComponent, i10.BeforeFilterOperatorComponent, i10.StringFilterMenuComponent, i10.FilterMenuTemplateDirective, i10.DateFilterMenuComponent, i11.TextBoxComponent, i12.ButtonComponent, i13.InlineSVGDirective, i14.DropDownListComponent, i8.DatePipe],\n    styles: [\".grid-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  position: relative;\\n}\\n\\n.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  padding: 0.5rem 0.75rem;\\n  width: 80%;\\n  border: 2px solid #afc7dd;\\n  box-shadow: 0 0 6px rgba(59, 83, 135, 0.5);\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  padding: 0.75rem 1.25rem;\\n  min-width: 120px;\\n  background-color: #4c4e4f;\\n  color: white;\\n  font-weight: 500;\\n  transition: background 0.3s, transform 0.2s;\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  background-color: #4c4e4f;\\n  transform: scale(1.05);\\n}\\n\\n\\n\\n.grid-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n.grid-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 500;\\n}\\n.grid-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%] {\\n  width: 300px;\\n}\\n\\n.grid-toolbar[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  align-items: center;\\n  margin-bottom: 10px;\\n}\\n\\n\\n\\n.k-grid-toolbar[_ngcontent-%COMP%] {\\n  \\n\\n  \\n\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 5px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  border-radius: 0.375rem;\\n  transition: all 0.15s ease-in-out;\\n  min-width: 40px;\\n  height: 40px;\\n  border: 1px solid transparent;\\n  background-color: transparent;\\n  \\n\\n  \\n\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn.btn-icon[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  min-width: 40px;\\n  width: 40px;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn.btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .k-grid-toolbar[_ngcontent-%COMP%]   .btn.btn-icon[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem !important;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn.btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%], .k-grid-toolbar[_ngcontent-%COMP%]   .btn.btn-icon[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 1.25rem !important;\\n  height: 1.25rem !important;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:not(.btn-icon) {\\n  padding: 0.375rem 0.75rem;\\n  gap: 0.5rem;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   kendo-dropdownbutton[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 0.375rem;\\n  transition: all 0.15s ease-in-out;\\n  min-width: 40px;\\n  height: 40px;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   kendo-dropdownbutton[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .custom-dropdown[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  background-color: #6f42c1;\\n  border-color: #6f42c1;\\n  color: white;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .custom-dropdown[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  background-color: #5a32a3;\\n  border-color: #5a32a3;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%] {\\n  background-color: #198754;\\n  border-color: #198754;\\n  color: white;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]:hover {\\n  background-color: #157347;\\n  border-color: #146c43;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-warning[_ngcontent-%COMP%] {\\n  background-color: #ffc107;\\n  border-color: #ffc107;\\n  color: #000;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-warning[_ngcontent-%COMP%]:hover {\\n  background-color: #ffca2c;\\n  border-color: #ffc720;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-info[_ngcontent-%COMP%] {\\n  background-color: #0dcaf0;\\n  border-color: #0dcaf0;\\n  color: #000;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-info[_ngcontent-%COMP%]:hover {\\n  background-color: #31d2f2;\\n  border-color: #25cff2;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: white;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5c636a;\\n  border-color: #565e64;\\n}\\n\\n\\n\\n.search-section[_ngcontent-%COMP%] {\\n  \\n\\n}\\n.search-section[_ngcontent-%COMP%]   .kendo-textbox[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  border: 1px solid #dee2e6;\\n}\\n.search-section[_ngcontent-%COMP%]   .kendo-textbox[_ngcontent-%COMP%]:focus {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.search-section[_ngcontent-%COMP%]   kendo-textbox[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  border: 1px solid #dee2e6;\\n  transition: all 0.15s ease-in-out;\\n}\\n.search-section[_ngcontent-%COMP%]   kendo-textbox[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%]:hover {\\n  border-color: #adb5bd;\\n}\\n.search-section[_ngcontent-%COMP%]   kendo-textbox[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%]:focus, .search-section[_ngcontent-%COMP%]   kendo-textbox[_ngcontent-%COMP%]   .k-textbox.k-state-focused[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 0.375rem;\\n  transition: all 0.15s ease-in-out;\\n  min-width: 40px;\\n  height: 40px;\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n\\n\\n.total-count[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 0.875rem;\\n}\\n.total-count[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  color: #6c757d !important;\\n}\\n.total-count[_ngcontent-%COMP%]   .fw-bold[_ngcontent-%COMP%] {\\n  font-weight: 600 !important;\\n  color: #495057;\\n}\\n\\n.k-grid[_ngcontent-%COMP%] {\\n  border-radius: 6px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\\n}\\n\\n.no-data[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: 40px 0;\\n  font-size: 16px;\\n  color: #888;\\n  background-color: #f9f9f9;\\n  border-radius: 6px;\\n  margin-top: 20px;\\n}\\n\\n.detail-container[_ngcontent-%COMP%] {\\n  padding: 15px;\\n  background-color: #f9f9f9;\\n  border-radius: 4px;\\n}\\n\\n.detail-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 8px;\\n}\\n.detail-row[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n  width: 120px;\\n  font-weight: 500;\\n  color: #666;\\n}\\n\\n\\n\\n.status-active[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  background-color: #e8f5e9;\\n  color: #2e7d32;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n\\n.status-inactive[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n\\n.status-pending[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  background-color: #fff8e1;\\n  color: #ff8f00;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n\\n\\n\\n[_nghost-%COMP%]     {\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n[_nghost-%COMP%]     .k-grid-header {\\n  background-color: #f5f5f5;\\n}\\n[_nghost-%COMP%]     .k-grid td .btn-icon {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  padding: 0.25rem;\\n  border-radius: 0.25rem;\\n  transition: all 0.15s ease-in-out;\\n}\\n[_nghost-%COMP%]     .k-grid td .btn-icon:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n[_nghost-%COMP%]     .k-button[title=Refresh] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #dee2e6;\\n  border-radius: 6px;\\n  padding: 8px 12px;\\n  transition: all 0.2s ease;\\n  cursor: pointer;\\n}\\n[_nghost-%COMP%]     .k-button[title=Refresh]:hover {\\n  background-color: #e9ecef;\\n  border-color: #adb5bd;\\n  transform: scale(1.05);\\n}\\n[_nghost-%COMP%]     .k-button[title=Refresh] .fas.fa-sync-alt {\\n  color: #6c757d;\\n  font-size: 14px;\\n}\\n[_nghost-%COMP%]     .column-config-panel {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\\n  color: white;\\n}\\n[_nghost-%COMP%]     .column-config-panel .form-check {\\n  margin-bottom: 10px;\\n}\\n[_nghost-%COMP%]     .column-config-panel .form-check .form-check-input {\\n  margin-right: 8px;\\n}\\n[_nghost-%COMP%]     .column-config-panel .form-check .form-check-input:checked {\\n  background-color: #28a745;\\n  border-color: #28a745;\\n}\\n[_nghost-%COMP%]     .column-config-panel .form-check .form-check-label {\\n  color: white;\\n  font-weight: 500;\\n  cursor: pointer;\\n}\\n[_nghost-%COMP%]     .column-config-panel .btn-primary {\\n  background-color: #28a745;\\n  border-color: #28a745;\\n}\\n[_nghost-%COMP%]     .column-config-panel .btn-primary:hover {\\n  background-color: #218838;\\n  border-color: #1e7e34;\\n}\\n[_nghost-%COMP%]     .column-config-panel .btn-secondary {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n}\\n[_nghost-%COMP%]     .column-config-panel .btn-secondary:hover {\\n  background-color: #5a6268;\\n  border-color: #545b62;\\n}\\n[_nghost-%COMP%]     .k-grid-column-sticky {\\n  background-color: #f8f9fa !important;\\n  border-right: 2px solid #dee2e6 !important;\\n}\\n[_nghost-%COMP%]     .k-grid-column-sticky .k-grid-header {\\n  background-color: #e9ecef !important;\\n}\\n[_nghost-%COMP%]     .k-grid-header th {\\n  font-weight: 600;\\n}\\n[_nghost-%COMP%]     .k-pager-numbers .k-link.k-state-selected {\\n  background-color: #007bff;\\n  color: white;\\n}\\n[_nghost-%COMP%]     .k-button {\\n  margin-right: 5px;\\n}\\n[_nghost-%COMP%]     .k-icon {\\n  font-size: 16px;\\n}\\n[_nghost-%COMP%]     .k-grid-content {\\n  overflow-y: auto;\\n}\\n[_nghost-%COMP%]     .k-grid tr:hover {\\n  background-color: #f0f7ff;\\n}\\n[_nghost-%COMP%]     .k-loading-mask {\\n  background-color: rgba(255, 255, 255, 0.7);\\n}\\n[_nghost-%COMP%]     .k-loading-image::before, \\n[_nghost-%COMP%]     .k-loading-image::after {\\n  border-color: #007bff transparent;\\n}\\n\\n  .k-clear-value {\\n  color: red !important;\\n}\\n\\n  .k-grid td, \\n  .k-grid th {\\n  border: none !important; \\n\\n}\\n\\n  kendo-grid.k-grid .k-table-alt-row .k-grid-content-sticky {\\n  background-color: #fafafa !important;\\n}\\n\\n  kendo-grid.k-grid .k-grid-content-sticky {\\n  border-top-color: rgba(0, 0, 0, 0.08);\\n  border-left-color: rgba(0, 0, 0, 0.3);\\n  border-right-color: rgba(0, 0, 0, 0.3);\\n  background-color: #fafafa !important;\\n}\\n\\n  .k-grid .k-table-row.k-selected > td, \\n.k-grid[_ngcontent-%COMP%]   td.k-selected[_ngcontent-%COMP%], \\n.k-grid[_ngcontent-%COMP%]   .k-table-row.k-selected[_ngcontent-%COMP%]    > td[_ngcontent-%COMP%], \\n.k-grid[_ngcontent-%COMP%]   .k-table-td.k-selected[_ngcontent-%COMP%], \\n.k-grid[_ngcontent-%COMP%]   .k-table-row.k-selected[_ngcontent-%COMP%]    > .k-table-td[_ngcontent-%COMP%] {\\n  background-color: transparent !important;\\n}\\n\\n  .k-grid .k-table-row.k-selected:hover .k-grid-content-sticky {\\n  background-color: #fafafa !important;\\n}\\n\\n  .k-clear-value {\\n  color: red !important;\\n}\\n\\n.fullscreen-grid[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 9999;\\n  background: white;\\n  padding: 20px;\\n  overflow: auto;\\n}\\n\\n.advanced-filters-panel[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border: 1px solid #dee2e6;\\n  border-radius: 0.375rem;\\n  margin-bottom: 1rem;\\n  \\n\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #495057;\\n  margin-bottom: 0.5rem;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%] {\\n  width: 100%;\\n  \\n\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%]   .k-dropdown-wrap[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  border: 1px solid #dee2e6;\\n  transition: all 0.15s ease-in-out;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%]   .k-dropdown-wrap[_ngcontent-%COMP%]:hover {\\n  border-color: #adb5bd;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%]   .k-dropdown-wrap[_ngcontent-%COMP%]:focus, .advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%]   .k-dropdown-wrap.k-state-focused[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%]   .k-select[_ngcontent-%COMP%] {\\n  border-radius: 0 0.375rem 0.375rem 0;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 0.375rem;\\n  transition: all 0.15s ease-in-out;\\n  min-width: 40px;\\n  height: 40px;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-button.btn-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n  color: white;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-button.btn-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0056b3;\\n  border-color: #0056b3;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-button.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: white;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-button.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5c636a;\\n  border-color: #565e64;\\n}\\n\\n[_nghost-%COMP%]     .k-grid .k-grid-header {\\n  background-color: #edf0f3;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-header .k-header {\\n  font-weight: 600;\\n  color: #495057;\\n  border-color: #dee2e6;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-header .k-header:hover {\\n  background-color: #e9ecef;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row:hover {\\n  background-color: #f8f9fa;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row.k-alt {\\n  background-color: #f8f9fa;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager {\\n  background-color: #f8f9fa;\\n  border-top: 1px solid #dee2e6;\\n}\\n[_nghost-%COMP%]     .k-filter-menu .k-filter-menu-content {\\n  padding: 1rem;\\n}\\n[_nghost-%COMP%]     .k-filter-menu .k-filter-menu-content .k-filter-menu-item {\\n  margin-bottom: 0.5rem;\\n}\\n[_nghost-%COMP%]     .k-filter-menu .k-filter-menu-content .k-filter-menu-item .k-textbox {\\n  width: 100%;\\n}\\n[_nghost-%COMP%]     .k-button {\\n  border-radius: 0.375rem;\\n  font-weight: 500;\\n}\\n[_nghost-%COMP%]     .k-button.k-primary {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n}\\n[_nghost-%COMP%]     .k-button.k-primary:hover {\\n  background-color: #0056b3;\\n  border-color: #0056b3;\\n}\\n\\n.custom-no-records[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem;\\n  color: #6c757d;\\n  font-style: italic;\\n}\\n\\n@media (max-width: 768px) {\\n  .advanced-filters-panel[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .k-grid-toolbar[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .k-grid-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .k-grid-toolbar[_ngcontent-%COMP%]   .kendo-grid-spacer[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  \\n\\n  .column-list[_ngcontent-%COMP%] {\\n    max-height: 400px;\\n    overflow-y: auto;\\n  }\\n  .column-item[_ngcontent-%COMP%] {\\n    border: 1px solid #dee2e6;\\n    border-radius: 6px;\\n    margin-bottom: 8px;\\n    background-color: #fff;\\n    transition: all 0.2s ease;\\n  }\\n  .column-item[_ngcontent-%COMP%]:hover {\\n    border-color: #adb5bd;\\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  }\\n  .column-item.dragging[_ngcontent-%COMP%] {\\n    opacity: 0.5;\\n    transform: rotate(5deg);\\n  }\\n  .column-controls[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    padding: 12px;\\n    gap: 12px;\\n  }\\n  .drag-handle[_ngcontent-%COMP%] {\\n    cursor: grab;\\n    color: #6c757d;\\n  }\\n  .drag-handle[_ngcontent-%COMP%]:active {\\n    cursor: grabbing;\\n  }\\n  .column-checkbox[_ngcontent-%COMP%] {\\n    flex-shrink: 0;\\n  }\\n  .column-info[_ngcontent-%COMP%] {\\n    flex: 1;\\n  }\\n  .column-info[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n    margin-bottom: 0;\\n    cursor: pointer;\\n  }\\n  .column-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n  .column-actions[_ngcontent-%COMP%] {\\n    flex-shrink: 0;\\n  }\\n}\\n\\n\\n  .k-grid-toolbar {\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n  .k-grid-toolbar .k-button {\\n  display: inline-flex !important;\\n  align-items: center !important;\\n  justify-content: center !important;\\n  gap: 0.5rem !important;\\n  font-size: 0.875rem !important;\\n  font-weight: 500 !important;\\n  padding: 0.375rem 0.75rem !important;\\n  border-radius: 0.375rem !important;\\n  transition: all 0.15s ease-in-out !important;\\n  min-width: 40px !important;\\n  height: 40px !important;\\n}\\n  .k-grid-toolbar .k-button:hover {\\n  transform: translateY(-1px) !important;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;\\n}\\n  .k-grid-toolbar kendo-dropdownbutton .k-button {\\n  padding: 0.375rem 0.75rem !important;\\n  gap: 0.5rem !important;\\n}\\n  .k-grid-toolbar > * {\\n  margin-right: 0.5rem !important;\\n}\\n  .k-grid-toolbar > *:last-child {\\n  margin-right: 0 !important;\\n}\\n  .k-grid-toolbar .btn, \\n  .k-grid-toolbar .k-button, \\n  .k-grid-toolbar kendo-dropdownbutton {\\n  margin-right: 0.5rem !important;\\n}\\n  .k-grid-toolbar .btn:last-child, \\n  .k-grid-toolbar .k-button:last-child, \\n  .k-grid-toolbar kendo-dropdownbutton:last-child {\\n  margin-right: 0 !important;\\n}\\n\\n\\n\\n  .k-grid .k-grid-header .k-header {\\n  background-color: #edf0f3 !important;\\n  font-weight: 600 !important;\\n  border-color: #dee2e6 !important;\\n}\\n  .k-grid .k-grid-content .k-table-row:hover {\\n  background-color: #f0f7ff !important;\\n}\\n  .k-grid .k-pager .k-pager-numbers .k-link {\\n  border-radius: 0.25rem !important;\\n  transition: all 0.15s ease-in-out !important;\\n}\\n  .k-grid .k-pager .k-pager-numbers .k-link:hover {\\n  background-color: #e9ecef !important;\\n}\\n  .k-grid .k-pager .k-pager-numbers .k-link.k-state-selected {\\n  background-color: #007bff !important;\\n  color: white !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Subject", "debounceTime", "distinctUntilChanged", "NavigationStart", "AddUserComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtwoWayListener", "UserListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "searchData", "ɵɵresetView", "ɵɵlistener", "UserListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener", "onSearchKeyDown", "onSearchChange", "UserListComponent_ng_template_4_Template_kendo_textbox_clear_1_listener", "clearSearch", "ɵɵelement", "UserListComponent_ng_template_4_Template_button_click_8_listener", "add", "UserListComponent_ng_template_4_Template_button_click_10_listener", "toggleExpand", "UserListComponent_ng_template_4_Template_button_click_12_listener", "resetTable", "UserListComponent_ng_template_4_Template_button_click_14_listener", "refreshGrid", "ɵɵadvance", "ɵɵstyleProp", "ɵɵtwoWayProperty", "ɵɵproperty", "ɵɵtextInterpolate", "page", "totalElements", "ɵɵclassProp", "isExpanded", "UserListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener", "_r4", "appliedFilters", "status", "UserListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_9_listener", "role", "UserListComponent_ng_template_5_div_0_Template_button_click_11_listener", "applyAdvancedFilters", "UserListComponent_ng_template_5_div_0_Template_button_click_14_listener", "clearAllFilters", "advancedFilterOptions", "roles", "ɵɵtemplate", "UserListComponent_ng_template_5_div_0_Template", "showAdvancedFilters", "UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_2_Template_a_click_0_listener", "_r7", "dataItem_r6", "$implicit", "unlockUser", "UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template_a_click_0_listener", "_r5", "edit", "userId", "UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_a_2_Template", "IsLocked", "UserListComponent_ng_container_6_kendo_grid_column_1_ng_template_1_Template", "ɵɵstyleMap", "ɵɵpureFunction0", "_c6", "fixedColumns", "includes", "_c7", "getHiddenField", "ɵɵtextInterpolate1", "dataItem_r8", "userFullName", "column_r10", "filter_r9", "UserListComponent_ng_container_6_kendo_grid_column_2_ng_template_1_Template", "UserListComponent_ng_container_6_kendo_grid_column_2_ng_template_2_Template", "dataItem_r11", "email", "ɵɵsanitizeHtml", "column_r13", "filter_r12", "UserListComponent_ng_container_6_kendo_grid_column_3_ng_template_1_Template", "UserListComponent_ng_container_6_kendo_grid_column_3_ng_template_2_Template", "dataItem_r14", "title", "column_r16", "filter_r15", "UserListComponent_ng_container_6_kendo_grid_column_4_ng_template_1_Template", "UserListComponent_ng_container_6_kendo_grid_column_4_ng_template_2_Template", "dataItem_r17", "phoneNo", "column_r19", "filter_r18", "UserListComponent_ng_container_6_kendo_grid_column_5_ng_template_1_Template", "UserListComponent_ng_container_6_kendo_grid_column_5_ng_template_2_Template", "dataItem_r20", "<PERSON><PERSON><PERSON>", "column_r22", "filter_r21", "UserListComponent_ng_container_6_kendo_grid_column_6_ng_template_1_Template", "UserListComponent_ng_container_6_kendo_grid_column_6_ng_template_2_Template", "UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_span_0_Template", "UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_span_1_Template", "dataItem_r23", "userStatus", "UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener", "ctx_r24", "_r24", "filter_r26", "column_r27", "column", "onStatusFilterChange", "filterOptions", "getFilterValue", "UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_1_Template", "UserListComponent_ng_container_6_kendo_grid_column_7_ng_template_2_Template", "ɵɵpipeBind2", "dataItem_r28", "lastUpdatedDate", "lastUpdatedByFullName", "column_r30", "filter_r29", "filterService_r31", "UserListComponent_ng_container_6_kendo_grid_column_8_ng_template_1_Template", "UserListComponent_ng_container_6_kendo_grid_column_8_ng_template_2_Template", "ɵɵelementContainerStart", "UserListComponent_ng_container_6_kendo_grid_column_1_Template", "UserListComponent_ng_container_6_kendo_grid_column_2_Template", "UserListComponent_ng_container_6_kendo_grid_column_3_Template", "UserListComponent_ng_container_6_kendo_grid_column_4_Template", "UserListComponent_ng_container_6_kendo_grid_column_5_Template", "UserListComponent_ng_container_6_kendo_grid_column_6_Template", "UserListComponent_ng_container_6_kendo_grid_column_7_Template", "UserListComponent_ng_container_6_kendo_grid_column_8_Template", "column_r32", "UserListComponent_ng_template_7_div_0_Template_button_click_5_listener", "_r33", "loadTable", "UserListComponent_ng_template_7_div_0_Template", "loading", "isLoading", "UserListComponent", "usersService", "cdr", "router", "route", "modalService", "AppService", "customLayoutUtilsService", "httpUtilService", "kendoColumnService", "grid", "serverSideRowData", "gridData", "IsListHasValue", "loginUser", "searchTerms", "searchSubscription", "filter", "logic", "filters", "gridFilter", "activeFilters", "text", "value", "kendoHide", "hiddenData", "kendoColOrder", "kendoInitColOrder", "hiddenFields", "gridColumns", "defaultColumns", "draggableColumns", "normalGrid", "expandedGrid", "gridColumnConfig", "field", "width", "isFixed", "type", "order", "filterable", "columnsVisibility", "sort", "dir", "routerSubscription", "GRID_STATE_KEY", "size", "pageNumber", "totalPages", "orderBy", "orderDir", "skip", "exportOptions", "selectedUsers", "isAllSelected", "userStatistics", "activeUsers", "inactiveUsers", "suspendedUsers", "lockedUsers", "totalUsers", "showBulkActions", "bulkActionStatus", "permissionArray", "constructor", "ngOnInit", "getLoggedInUser", "console", "log", "pipe", "subscribe", "searchTerm", "detectChanges", "events", "event", "saveGridState", "loadGridState", "loadRoles", "loadUserStatistics", "onPageLoad", "initializeColumnVisibilitySystem", "setTimeout", "loadColumnConfigFromDatabase", "map", "col", "ngAfterViewInit", "onTabActivated", "ngOnDestroy", "unsubscribe", "complete", "loadTableWithKendoEndpoint", "loadingSubject", "next", "sortForBackend", "length", "state", "take", "search", "loggedInUserId", "getUsersForKendoGrid", "data", "<PERSON><PERSON><PERSON>", "responseData", "errors", "error", "handleEmptyResponse", "userData", "total", "Math", "ceil", "_this", "_asyncToGenerator", "toggleAdvancedFilters", "queryParams", "pageSize", "sortOrder", "sortField", "getAllRoles", "content", "getDefaultPermissions", "permissions", "getUserStatistics", "statistics", "onSelectionChange", "selection", "selectedRows", "selectAllUsers", "deleteUser", "user", "confirm", "FirstName", "LastName", "deleteData", "response", "message", "showSuccess", "showError", "bulkUpdateUserStatus", "bulkUpdateData", "userIds", "firstName", "lastName", "unlockData", "key", "filterConfiguration", "paginate", "columnFilter", "searchText", "undefined", "trim", "push", "operator", "pageChange", "onSortChange", "isThirdClick", "filterChange", "flattenFilters", "predicate", "find", "f", "exists", "findIndex", "splice", "for<PERSON>ach", "savedState", "localStorage", "getItem", "JSON", "parse", "setItem", "stringify", "id", "NgbModalOptions", "backdrop", "keyboard", "scrollable", "modalRef", "open", "componentInstance", "defaultPermissions", "passEntry", "receivedEntry", "deleteTemplate", "item", "gridContainer", "document", "querySelector", "classList", "toggle", "refresh", "onExportClick", "exportAllUsers", "exportSelectedUsers", "exportFilteredUsers", "warn", "exportParams", "format", "exportUsers", "exportData", "downloadExcel", "UserId", "filename", "csv<PERSON><PERSON>nt", "convertToCSV", "blob", "Blob", "link", "createElement", "url", "URL", "createObjectURL", "setAttribute", "Date", "toISOString", "split", "style", "visibility", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "headers", "Object", "keys", "csvRows", "join", "row", "values", "header", "saveHead", "nonHiddenColumns", "hiddenColumns", "columns", "hidden", "columnData", "draggableColumnsOrder", "index", "orderIndex", "pageName", "userID", "LoggedId", "createHideFields", "res", "saveToLocalStorage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "token", "getLocalStorageItem", "indexOf", "clearFromLocalStorage", "reset", "loadSavedColumnOrder", "savedOrder", "parsedOrder", "Array", "isArray", "savedDraggableColumns", "a", "b", "missingColumns", "columnName", "onColumnReorder", "newIndex", "oldIndex", "reorderedColumns", "movedColumn", "updateColumnVisibility", "some", "indexExists", "getHideFields", "Data", "hideData", "includeInChooser", "loadFromLocalStorageFallback", "savedConfig", "getFromLocalStorage", "ɵɵdirectiveInject", "i1", "UserService", "ChangeDetectorRef", "i2", "Router", "ActivatedRoute", "i3", "NgbModal", "i4", "i5", "CustomLayoutUtilsService", "i6", "HttpUtilsService", "i7", "KendoColumnService", "selectors", "viewQuery", "UserListComponent_Query", "rf", "ctx", "UserListComponent_div_0_Template", "UserListComponent_Template_kendo_grid_columnReorder_2_listener", "_r1", "UserListComponent_Template_kendo_grid_selectionChange_2_listener", "UserListComponent_Template_kendo_grid_filterChange_2_listener", "UserListComponent_Template_kendo_grid_pageChange_2_listener", "UserListComponent_Template_kendo_grid_sortChange_2_listener", "UserListComponent_Template_kendo_grid_columnVisibilityChange_2_listener", "UserListComponent_ng_template_4_Template", "UserListComponent_ng_template_5_Template", "UserListComponent_ng_container_6_Template", "UserListComponent_ng_template_7_Template", "ɵɵpureFunction1", "_c2", "_c1", "_c3", "_c4", "_c5"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\setting\\user_list\\user-list.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\setting\\user_list\\user-list.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  On<PERSON>ni<PERSON>,\r\n  <PERSON><PERSON><PERSON>roy,\r\n  ChangeDetector<PERSON>ef,\r\n  ViewChild,\r\n  AfterViewInit,\r\n} from '@angular/core';\r\nimport { SortDescriptor } from '@progress/kendo-data-query';\r\nimport {\r\n  FilterDescriptor,\r\n  CompositeFilterDescriptor,\r\n  process,\r\n} from '@progress/kendo-data-query';\r\nimport { State } from '@progress/kendo-data-query';\r\nimport {\r\n  Subject,\r\n  debounceTime,\r\n  distinctUntilChanged,\r\n  Subscription,\r\n} from 'rxjs';\r\nimport { Router, NavigationStart, ActivatedRoute } from '@angular/router';\r\nimport { saveAs } from '@progress/kendo-file-saver';\r\nimport { AppService } from 'src/app/modules/services/app.service';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\n\r\nimport { AddUserComponent } from '../add-user/user-add.component';\r\nimport { HttpUtilsService } from '../../services/http-utils.service';\r\nimport { UserService } from '../../services/user.service';\r\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\r\nimport { KendoColumnService } from '../../services/kendo-column.service';\r\n\r\n// Type definitions\r\ninterface UserData {\r\n  UserId: number;\r\n  FirstName: string;\r\n  LastName: string;\r\n  Email: string;\r\n  Status: string;\r\n  Title: string;\r\n  PhoneNo: string;\r\n  RoleName: string;\r\n  LastUpdatedDate: string;\r\n  CreatedDate: string;\r\n  IsEmailNotificationEnabled: boolean;\r\n  IsPasswordChanged: boolean;\r\n  IsLocked: boolean;\r\n  PharmacyId?: number;\r\n  MedicalCenterId?: number;\r\n  CreatedBy?: string;\r\n  LastUpdatedBy?: string;\r\n}\r\n\r\n// Type for page configuration\r\ninterface PageConfig {\r\n  size: number;\r\n  pageNumber: number;\r\n  totalElements: number;\r\n  totalPages: number;\r\n  orderBy: string;\r\n  orderDir: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-user-list',\r\n  templateUrl: './user-list.component.html',\r\n  styleUrls: ['./user-list.component.scss'],\r\n})\r\nexport class UserListComponent implements OnInit, OnDestroy, AfterViewInit {\r\n  @ViewChild('normalGrid') grid: any;\r\n\r\n  // Data\r\n  public serverSideRowData: any[] = [];\r\n  public gridData: any[] = [];\r\n  public IsListHasValue: boolean = false;\r\n\r\n  public loading: boolean = false;\r\n  public isLoading: boolean = false;\r\n\r\n  loginUser: any = {};\r\n\r\n  // Search\r\n  public searchData: string = '';\r\n  private searchTerms = new Subject<string>();\r\n  private searchSubscription: Subscription;\r\n\r\n  // Enhanced Filters for Kendo UI\r\n  public filter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\r\n  public gridFilter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\r\n  public activeFilters: Array<{\r\n    field: string;\r\n    operator: string;\r\n    value: any;\r\n  }> = [];\r\n\r\n  public filterOptions: Array<{ text: string; value: string | null }> = [\r\n    { text: 'All', value: null },\r\n    { text: 'Active', value: 'Active' },\r\n    { text: 'Inactive', value: 'Inactive' },\r\n  ];\r\n\r\n  // Advanced filter options\r\n  public advancedFilterOptions = {\r\n    status: [\r\n      { text: 'All', value: null },\r\n      { text: 'Active', value: 'Active' },\r\n      { text: 'Inactive', value: 'Inactive' },\r\n    ] as Array<{ text: string; value: string | null }>,\r\n    roles: [] as Array<{ text: string; value: string | null }>, // Will be populated from backend\r\n  };\r\n\r\n  // Filter state\r\n  public showAdvancedFilters = false;\r\n  public appliedFilters: {\r\n    status?: string | null;\r\n    role?: string | null;\r\n  } = {};\r\n\r\n  // NEW COLUMN VISIBILITY SYSTEM - replacing the old one\r\n  public kendoHide: any;\r\n  public hiddenData: any = [];\r\n  public kendoColOrder: any = [];\r\n  public kendoInitColOrder: any = [];\r\n  public hiddenFields: any = [];\r\n\r\n  // Column configuration for the new system\r\n  public gridColumns: string[] = [];\r\n  public defaultColumns: string[] = [];\r\n  public fixedColumns: string[] = [];\r\n  public draggableColumns: string[] = [];\r\n  public normalGrid: any;\r\n  public expandedGrid: any;\r\n  public isExpanded = false;\r\n\r\n  // Enhanced Columns with Kendo UI features\r\n  public gridColumnConfig: Array<{\r\n    field: string;\r\n    title: string;\r\n    width: number;\r\n    isFixed: boolean;\r\n    type: string;\r\n    filterable?: boolean;\r\n    order: number;\r\n  }> = [\r\n    {\r\n      field: 'action',\r\n      title: 'Action',\r\n      width: 80,\r\n      isFixed: true,\r\n      type: 'action',\r\n      order: 1,\r\n    },\r\n    {\r\n      field: 'userFullName',\r\n      title: 'Name',\r\n      width: 150,\r\n      isFixed: true,\r\n      type: 'text',\r\n      filterable: true,\r\n      order: 2,\r\n    },\r\n    // { field: 'lastName', title: 'Last Name', width: 150, isFixed: true, type: 'text', filterable: true, order: 3 },\r\n    {\r\n      field: 'email',\r\n      title: 'Email',\r\n      width: 250,\r\n      isFixed: false,\r\n      type: 'text',\r\n      filterable: true,\r\n      order: 4,\r\n    },\r\n    {\r\n      field: 'title',\r\n      title: 'Title',\r\n      width: 120,\r\n      isFixed: false,\r\n      type: 'text',\r\n      filterable: true,\r\n      order: 5,\r\n    },\r\n    {\r\n      field: 'phoneNo',\r\n      title: 'Phone',\r\n      width: 120,\r\n      isFixed: false,\r\n      type: 'text',\r\n      filterable: true,\r\n      order: 6,\r\n    },\r\n    {\r\n      field: 'roleName',\r\n      title: 'Role',\r\n      width: 120,\r\n      isFixed: false,\r\n      type: 'text',\r\n      filterable: true,\r\n      order: 7,\r\n    },\r\n    {\r\n      field: 'Status',\r\n      title: 'Status',\r\n      width: 100,\r\n      type: 'status',\r\n      isFixed: false,\r\n      filterable: true,\r\n      order: 8,\r\n    },\r\n    {\r\n      field: 'lastUpdatedDate',\r\n      title: 'Updated Date',\r\n      width: 160,\r\n      isFixed: false,\r\n      type: 'date',\r\n      filterable: true,\r\n      order: 9,\r\n    },\r\n  ];\r\n\r\n  // OLD SYSTEM - to be removed\r\n  public columnsVisibility: Record<string, boolean> = {};\r\n\r\n  // Old column configuration management removed - replaced with new system\r\n\r\n  // State\r\n  public sort: SortDescriptor[] = [{ field: 'lastUpdatedDate', dir: 'desc' }];\r\n  \r\n  // Remove custom sort state tracking → rely on Kendo native\r\n\r\n  // Router subscription for saving state on navigation\r\n  private routerSubscription: Subscription;\r\n\r\n  // Storage key for state persistence\r\n  private readonly GRID_STATE_KEY = 'form-templates-grid-state';\r\n\r\n  // Pagination\r\n  public page: PageConfig = {\r\n    size: 10,\r\n    pageNumber: 0,\r\n    totalElements: 0,\r\n    totalPages: 0,\r\n    orderBy: 'lastUpdatedDate',\r\n    orderDir: 'desc',\r\n  };\r\n  public skip: number = 0;\r\n\r\n  // Export options\r\n  public exportOptions: Array<{ text: string; value: string }> = [\r\n    { text: 'Export All', value: 'all' },\r\n    { text: 'Export Selected', value: 'selected' },\r\n    { text: 'Export Filtered', value: 'filtered' },\r\n  ];\r\n\r\n  // Selection state\r\n  public selectedUsers: any[] = [];\r\n  public isAllSelected: boolean = false;\r\n\r\n  // Statistics\r\n  public userStatistics: {\r\n    activeUsers: number;\r\n    inactiveUsers: number;\r\n    suspendedUsers: number;\r\n    lockedUsers: number;\r\n    totalUsers: number;\r\n  } = {\r\n    activeUsers: 0,\r\n    inactiveUsers: 0,\r\n    suspendedUsers: 0,\r\n    lockedUsers: 0,\r\n    totalUsers: 0,\r\n  };\r\n\r\n  // Bulk operations\r\n  public showBulkActions = false;\r\n  public bulkActionStatus: string = 'Active';\r\n\r\n  //add or edit default paramters\r\n  public permissionArray: any = [];\r\n\r\n  constructor(\r\n    private usersService: UserService,\r\n    private cdr: ChangeDetectorRef,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private modalService: NgbModal, // Provides modal functionality to display modals\r\n    public AppService: AppService,\r\n    private customLayoutUtilsService: CustomLayoutUtilsService,\r\n    private httpUtilService: HttpUtilsService,\r\n    private kendoColumnService: KendoColumnService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loginUser = this.AppService.getLoggedInUser();\r\n    console.log('Login user loaded:', this.loginUser);\r\n\r\n    // Setup search with debounce\r\n    this.searchSubscription = this.searchTerms\r\n      .pipe(debounceTime(500), distinctUntilChanged())\r\n      .subscribe((searchTerm) => {\r\n        console.log('Search triggered with term:', searchTerm);\r\n        this.page.pageNumber = 0;\r\n        this.skip = 0;\r\n        // Set loading state for search\r\n        this.loading = true;\r\n        this.isLoading = true;\r\n        // Force change detection to show loader\r\n        this.cdr.detectChanges();\r\n        this.loadTable();\r\n      });\r\n\r\n    // Subscribe to router events to save state before navigation\r\n    this.routerSubscription = this.router.events.subscribe((event) => {\r\n      if (event instanceof NavigationStart) {\r\n        this.saveGridState();\r\n      }\r\n    });\r\n\r\n    // Load saved state if available\r\n    this.loadGridState();\r\n\r\n    // Load roles for advanced filters\r\n    this.loadRoles();\r\n\r\n    // Load user statistics\r\n    this.loadUserStatistics();\r\n\r\n    // Initialize with default page load\r\n    this.onPageLoad();\r\n\r\n    // Initialize new column visibility system\r\n    this.initializeColumnVisibilitySystem();\r\n\r\n    // Load column configuration after a short delay to ensure loginUser is available\r\n    setTimeout(() => {\r\n      this.loadColumnConfigFromDatabase();\r\n    }, 100);\r\n  }\r\n\r\n  /**\r\n   * Initialize the new column visibility system\r\n   */\r\n  private initializeColumnVisibilitySystem(): void {\r\n    // Initialize default columns\r\n    this.defaultColumns = this.gridColumnConfig.map((col) => col.field);\r\n    this.gridColumns = [...this.defaultColumns];\r\n\r\n    // Set fixed columns (first 3 columns)\r\n    this.fixedColumns = ['action', 'FirstName', 'LastName'];\r\n\r\n    // Set draggable columns (all except fixed)\r\n    this.draggableColumns = this.defaultColumns.filter(\r\n      (col) => !this.fixedColumns.includes(col)\r\n    );\r\n\r\n    // Initialize normal and expanded grid references\r\n    this.normalGrid = this.grid;\r\n    this.expandedGrid = this.grid;\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    // Load the table after the view is initialized\r\n    // Small delay to ensure the grid is properly rendered\r\n    setTimeout(() => {\r\n      this.loadTable();\r\n    }, 200);\r\n  }\r\n\r\n  // Method to handle when the component becomes visible\r\n  onTabActivated(): void {\r\n    // Set loading state for tab activation\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    // Refresh the data when the tab is activated\r\n    this.loadTable();\r\n    this.loadUserStatistics();\r\n  }\r\n\r\n  // Method to handle initial page load\r\n  onPageLoad(): void {\r\n    // Initialize the component with default data\r\n    this.page.pageNumber = 0;\r\n    this.skip = 0;\r\n    this.sort = [{ field: 'LastUpdatedDate', dir: 'desc' }];\r\n    this.filter = { logic: 'and', filters: [] };\r\n    this.searchData = '';\r\n\r\n    // Set loading state for initial page load\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    // Load the data\r\n    this.loadTable();\r\n    this.loadUserStatistics();\r\n  }\r\n\r\n  // Refresh grid data - only refresh the grid with latest API call\r\n  refreshGrid(): void {\r\n    // Set loading state to show full-screen loader\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n\r\n    // Reset to first page and clear any applied filters\r\n    this.page.pageNumber = 0;\r\n    this.skip = 0;\r\n    this.filter = { logic: 'and', filters: [] };\r\n    this.gridFilter = { logic: 'and', filters: [] };\r\n    this.activeFilters = [];\r\n    this.appliedFilters = {};\r\n\r\n    // Clear search data\r\n    this.searchData = '';\r\n\r\n    // Load fresh data from API\r\n    this.loadTable();\r\n  }\r\n\r\n  // Old column configuration methods removed - replaced with new system\r\n\r\n  // Old column selector methods removed - replaced with new system\r\n\r\n  ngOnDestroy(): void {\r\n    // Clean up subscriptions\r\n    if (this.routerSubscription) {\r\n      this.routerSubscription.unsubscribe();\r\n    }\r\n    if (this.searchSubscription) {\r\n      this.searchSubscription.unsubscribe();\r\n    }\r\n    this.searchTerms.complete();\r\n  }\r\n  // New method to load data using Kendo UI specific endpoint\r\n  loadTableWithKendoEndpoint() {\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n\r\n    // Enable loader\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    \r\n    // Force change detection to show loader\r\n    this.cdr.detectChanges();\r\n\r\n    // Prepare state object for Kendo UI endpoint\r\n    // When sort is empty (3rd click), send default sort to backend\r\n    const sortForBackend = this.sort.length > 0\r\n      ? this.sort\r\n      : [{ field: 'lastUpdatedDate', dir: 'desc' }];\r\n    const state = {\r\n      take: this.page.size,\r\n      skip: this.skip,\r\n      sort: sortForBackend,\r\n      filter: this.filter.filters,\r\n      search: this.searchData,\r\n      loggedInUserId: this.loginUser.userId,\r\n    };\r\n\r\n    console.log('Loading table with search term:', this.searchData);\r\n    console.log('Full state object:', state);\r\n    console.log('Loading states - loading:', this.loading, 'isLoading:', this.isLoading);\r\n\r\n    this.usersService.getUsersForKendoGrid(state).subscribe({\r\n      next: (data: {\r\n        isFault?: boolean;\r\n        responseData?: {\r\n          data: any[];\r\n          total: number;\r\n          errors?: string[];\r\n          status?: number;\r\n        };\r\n        data?: any[];\r\n        total?: number;\r\n        errors?: string[];\r\n        status?: number;\r\n      }) => {\r\n        // Handle the new API response structure\r\n        if (\r\n          data.isFault ||\r\n          (data.responseData &&\r\n            data.responseData.errors &&\r\n            data.responseData.errors.length > 0)\r\n        ) {\r\n          const errors = data.responseData?.errors || data.errors || [];\r\n          console.error('Kendo UI Grid errors:', errors);\r\n          this.handleEmptyResponse();\r\n        } else {\r\n          // Handle both old and new response structures\r\n          const responseData = data.responseData || data;\r\n          const userData = responseData.data || [];\r\n          const total = responseData.total || 0;\r\n\r\n          this.IsListHasValue = userData.length !== 0;\r\n          this.serverSideRowData = userData;\r\n          this.gridData = this.serverSideRowData;\r\n          this.page.totalElements = total;\r\n          this.page.totalPages = Math.ceil(total / this.page.size);\r\n        }\r\n        this.httpUtilService.loadingSubject.next(false);\r\n      },\r\n      error: (error: unknown) => {\r\n        console.error('Error loading data with Kendo UI endpoint:', error);\r\n        this.handleEmptyResponse();\r\n        this.loading = false;\r\n        this.isLoading = false;\r\n        this.httpUtilService.loadingSubject.next(false);\r\n      },\r\n      complete: () => {\r\n        this.loading = false;\r\n        this.isLoading = false;\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        this.cdr.detectChanges();\r\n      },\r\n    });\r\n  }\r\n\r\n  // Enhanced loadTable method that can use either endpoint\r\n  async loadTable() {\r\n    // Use the new Kendo UI specific endpoint for better performance\r\n    this.loadTableWithKendoEndpoint();\r\n  }\r\n\r\n  private handleEmptyResponse(): void {\r\n    this.IsListHasValue = false;\r\n    this.serverSideRowData = [];\r\n    this.gridData = [];\r\n    this.page.totalElements = 0;\r\n    this.page.totalPages = 0;\r\n  }\r\n\r\n  // Enhanced search handling\r\n  clearSearch(): void {\r\n    // Clear search data and trigger search\r\n    this.searchData = '';\r\n    // Set loading state for clear search\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.searchTerms.next('');\r\n  }\r\n\r\n  // Clear all filters and search\r\n  clearAllFilters(): void {\r\n    this.searchData = '';\r\n    this.filter = { logic: 'and', filters: [] };\r\n    this.gridFilter = { logic: 'and', filters: [] };\r\n    this.activeFilters = [];\r\n    this.appliedFilters = {};\r\n    this.page.pageNumber = 0;\r\n    this.skip = 0;\r\n    // Set loading state for clear all filters\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.loadTable();\r\n  }\r\n\r\n  // Apply advanced filters\r\n  applyAdvancedFilters(): void {\r\n    this.page.pageNumber = 0;\r\n    this.skip = 0;\r\n    // Set loading state for advanced filters\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.loadTable();\r\n  }\r\n\r\n  // Toggle advanced filters panel\r\n  toggleAdvancedFilters(): void {\r\n    this.showAdvancedFilters = !this.showAdvancedFilters;\r\n  }\r\n\r\n  // Load roles for advanced filters\r\n  loadRoles(): void {\r\n    const queryParams: {\r\n      pageSize: number;\r\n      sortOrder: string;\r\n      sortField: string;\r\n      pageNumber: number;\r\n    } = {\r\n      pageSize: 1000,\r\n      sortOrder: 'ASC',\r\n      sortField: 'roleName',\r\n      pageNumber: 0,\r\n    };\r\n\r\n    this.usersService.getAllRoles(queryParams).subscribe({\r\n      next: (data: {\r\n        responseData?: {\r\n          content: Array<{ roleName: string }>;\r\n        };\r\n      }) => {\r\n        if (data && data.responseData && data.responseData.content) {\r\n          this.advancedFilterOptions.roles = [\r\n            { text: 'All Roles', value: null },\r\n            ...data.responseData.content.map((role: { roleName: string }) => ({\r\n              text: role.roleName,\r\n              value: role.roleName,\r\n            })),\r\n          ];\r\n        }\r\n      },\r\n      error: (error: unknown) => {\r\n        console.error('Error loading roles:', error);\r\n        // Set default roles if loading fails\r\n        this.advancedFilterOptions.roles = [{ text: 'All Roles', value: null }];\r\n      },\r\n    });\r\n    this.usersService\r\n      .getDefaultPermissions({})\r\n      .subscribe((permissions: any) => {\r\n        this.permissionArray = permissions.responseData;\r\n      });\r\n  }\r\n\r\n  // Load user statistics\r\n  loadUserStatistics(): void {\r\n    this.usersService.getUserStatistics().subscribe({\r\n      next: (data: any) => {\r\n        if (data && data.statistics) {\r\n          this.userStatistics = data.statistics;\r\n        }\r\n      },\r\n      error: (error: unknown) => {\r\n        console.error('Error loading user statistics:', error);\r\n      },\r\n    });\r\n  }\r\n\r\n  // Selection handling\r\n  onSelectionChange(selection: any): void {\r\n    this.selectedUsers = selection.selectedRows || [];\r\n    this.isAllSelected =\r\n      this.selectedUsers.length === this.serverSideRowData.length;\r\n    this.showBulkActions = this.selectedUsers.length > 0;\r\n  }\r\n\r\n  // Select all users\r\n  selectAllUsers(): void {\r\n    if (this.isAllSelected) {\r\n      this.selectedUsers = [];\r\n      this.isAllSelected = false;\r\n    } else {\r\n      this.selectedUsers = [...this.serverSideRowData];\r\n      this.isAllSelected = true;\r\n    }\r\n    this.showBulkActions = this.selectedUsers.length > 0;\r\n  }\r\n\r\n  // Delete user\r\n  deleteUser(user: any): void {\r\n    if (\r\n      confirm(\r\n        `Are you sure you want to delete user ${user.FirstName} ${user.LastName}?`\r\n      )\r\n    ) {\r\n      // Show loading state\r\n      this.loading = true;\r\n      this.isLoading = true;\r\n\r\n      const deleteData = {\r\n        userId: user.userId,\r\n        loggedInUserId: this.loginUser.userId || 0,\r\n      };\r\n\r\n      this.usersService.deleteUser(deleteData).subscribe({\r\n        next: (response: any) => {\r\n          if (response && response.message) {\r\n            //alert(response.message);\r\n                                        this.customLayoutUtilsService.showSuccess(response.message, '');\r\n\r\n            this.loadTable(); // Reload the table\r\n            this.loadUserStatistics(); // Reload statistics\r\n          }\r\n        },\r\n        error: (error: unknown) => {\r\n          console.error('Error deleting user:', error);\r\n                            this.customLayoutUtilsService.showError('Error deleting user', '');\r\n\r\n          //alert('Error deleting user. Please try again.');\r\n          // Reset loading state on error\r\n          this.loading = false;\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  // Bulk update user status\r\n  bulkUpdateUserStatus(): void {\r\n    if (this.selectedUsers.length === 0) {\r\n      //alert('Please select users to update.');\r\n                                  this.customLayoutUtilsService.showSuccess('Please select users to complete', '');\r\n\r\n      return;\r\n    }\r\n\r\n    if (\r\n      confirm(\r\n        `Are you sure you want to update ${this.selectedUsers.length} users to status: ${this.bulkActionStatus}?`\r\n      )\r\n    ) {\r\n      // Show loading state\r\n      this.loading = true;\r\n      this.isLoading = true;\r\n\r\n      const bulkUpdateData = {\r\n        userIds: this.selectedUsers.map((user) => user.userId),\r\n        status: this.bulkActionStatus,\r\n        loggedInUserId: this.loginUser.userId || 0,\r\n      };\r\n\r\n      this.usersService.bulkUpdateUserStatus(bulkUpdateData).subscribe({\r\n        next: (response: any) => {\r\n          if (response && response.message) {\r\n            //alert(response.message);\r\n            this.loadTable(); // Reload the table\r\n            this.loadUserStatistics(); // Reload statistics\r\n            this.selectedUsers = []; // Clear selection\r\n            this.showBulkActions = false;\r\n          }\r\n        },\r\n        error: (error: unknown) => {\r\n          console.error('Error updating users:', error);\r\n          //alert('Error updating users. Please try again.');\r\n                                      this.customLayoutUtilsService.showError('Error updating users. Please try again', '');\r\n\r\n          // Reset loading state on error\r\n          this.loading = false;\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  // Unlock user\r\n  unlockUser(user: any): void {\r\n    if (\r\n      confirm(\r\n        `Are you sure you want to unlock user ${user.firstName} ${user.lastName}?`\r\n      )\r\n    ) {\r\n      // Show loading state\r\n      this.loading = true;\r\n      this.isLoading = true;\r\n\r\n      const unlockData = {\r\n        userId: user.userId,\r\n        loggedInUserId: this.loginUser.userId || 0,\r\n      };\r\n\r\n      this.usersService.unlockUser(unlockData).subscribe({\r\n        next: (response: any) => {\r\n          if (response && response.message) {\r\n                                        this.customLayoutUtilsService.showSuccess(response.message, '');\r\n\r\n            //alert(response.message);\r\n            this.loadTable(); // Reload the table\r\n            this.loadUserStatistics(); // Reload statistics\r\n          }\r\n        },\r\n        error: (error: unknown) => {\r\n          console.error('Error unlocking user:', error);\r\n                                      this.customLayoutUtilsService.showError('Error unlocking user', '');\r\n\r\n          //alert('Error unlocking user. Please try again.');\r\n          // Reset loading state on error\r\n          this.loading = false;\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  onSearchKeyDown(event: KeyboardEvent): void {\r\n    console.log('Search keydown event:', event.key, 'Search data:', this.searchData);\r\n    if (event.key === 'Enter') {\r\n      // Trigger search immediately on Enter key\r\n      console.log('Triggering search on Enter key');\r\n      this.searchTerms.next(this.searchData || '');\r\n    }\r\n  }\r\n\r\n  // Handle search model changes\r\n  onSearchChange(): void {\r\n    // Trigger search when model changes with debouncing\r\n    console.log('Search model changed:', this.searchData);\r\n    console.log('Triggering search with debounce');\r\n    // Ensure search is triggered even for empty strings\r\n    this.searchTerms.next(this.searchData || '');\r\n  }\r\n\r\n  // Enhanced function to filter data from search and advanced filters\r\n  filterConfiguration(): {\r\n    paginate: boolean;\r\n    search: string;\r\n    columnFilter: Array<{\r\n      field: string;\r\n      operator: string;\r\n      value: any;\r\n    }>;\r\n  } {\r\n    let filter: {\r\n      paginate: boolean;\r\n      search: string;\r\n      columnFilter: Array<{\r\n        field: string;\r\n        operator: string;\r\n        value: any;\r\n      }>;\r\n    } = {\r\n      paginate: true,\r\n      search: '',\r\n      columnFilter: [],\r\n    };\r\n\r\n    // Handle search text\r\n    let searchText: string;\r\n    if (this.searchData === null || this.searchData === undefined) {\r\n      searchText = '';\r\n    } else {\r\n      searchText = this.searchData;\r\n    }\r\n    filter.search = searchText.trim();\r\n\r\n    // Handle Kendo UI grid filters\r\n    if (this.activeFilters && this.activeFilters.length > 0) {\r\n      filter.columnFilter = [...this.activeFilters];\r\n    }\r\n\r\n    // Add advanced filters\r\n    if (this.appliedFilters.status && this.appliedFilters.status !== null) {\r\n      filter.columnFilter.push({\r\n        field: 'userStatus',\r\n        operator: 'eq',\r\n        value: this.appliedFilters.status,\r\n      });\r\n    }\r\n\r\n    if (this.appliedFilters.role && this.appliedFilters.role !== null) {\r\n      filter.columnFilter.push({\r\n        field: 'roleName',\r\n        operator: 'eq',\r\n        value: this.appliedFilters.role,\r\n      });\r\n    }\r\n\r\n    return filter;\r\n  }\r\n\r\n  // Grid event handlers\r\n  public pageChange(event: { skip: number; take: number }): void {\r\n    this.skip = event.skip;\r\n    this.page.pageNumber = event.skip / event.take;\r\n    this.page.size = event.take;\r\n    // Set loading state for pagination\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.loadTable();\r\n  }\r\n\r\n  public onSortChange(sort: SortDescriptor[]): void {\r\n    // Check if this is the 3rd click (dir is undefined)\r\n    const isThirdClick = sort.length > 0 && sort[0] && sort[0].dir === undefined;\r\n\r\n    if (isThirdClick) {\r\n      // 3rd click - clear sort and use default\r\n      this.sort = [];\r\n      this.page.orderBy = 'lastUpdatedDate';\r\n      this.page.orderDir = 'desc';\r\n    } else if (sort.length > 0 && sort[0] && sort[0].dir) {\r\n      // Valid sort with direction\r\n      this.sort = sort;\r\n      this.page.orderBy = sort[0].field || 'lastUpdatedDate';\r\n      this.page.orderDir = sort[0].dir;\r\n    } else {\r\n      // Empty sort array or invalid sort\r\n      this.sort = [];\r\n      this.page.orderBy = 'lastUpdatedDate';\r\n      this.page.orderDir = 'desc';\r\n    }\r\n\r\n    // Set loading state for sorting\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.loadTable();\r\n  }\r\n\r\n  public filterChange(filter: CompositeFilterDescriptor): void {\r\n    this.filter = filter;\r\n    this.gridFilter = filter;\r\n    this.activeFilters = this.flattenFilters(filter);\r\n    this.page.pageNumber = 0;\r\n    this.skip = 0;\r\n    // Set loading state for filtering\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.loadTable();\r\n  }\r\n\r\n  // Old column visibility methods removed - replaced with new system\r\n\r\n  // Fix 2: More robust getFilterValue method\r\n  public getFilterValue(\r\n    filter: CompositeFilterDescriptor,\r\n    column: { field: string }\r\n  ): any {\r\n    if (!filter || !filter.filters || !column) {\r\n      return null;\r\n    }\r\n    const predicate = filter.filters.find(\r\n      (f: any) => f && 'field' in f && f.field === column.field\r\n    );\r\n    return predicate && 'value' in predicate ? predicate.value : null;\r\n  }\r\n\r\n  // Fix 3: More robust onStatusFilterChange method\r\n  public onStatusFilterChange(\r\n    value: string | null,\r\n    filter: CompositeFilterDescriptor,\r\n    column: { field: string }\r\n  ): void {\r\n    if (!filter || !filter.filters || !column) {\r\n      console.error('Invalid filter or column:', { filter, column });\r\n      return;\r\n    }\r\n\r\n    const exists = filter.filters.findIndex(\r\n      (f: any) => f && 'field' in f && f.field === column.field\r\n    );\r\n    if (exists > -1) {\r\n      filter.filters.splice(exists, 1);\r\n    }\r\n\r\n    if (value !== null) {\r\n      filter.filters.push({\r\n        field: column.field,\r\n        operator: 'eq',\r\n        value: value,\r\n      });\r\n    }\r\n\r\n    this.filterChange(filter);\r\n  }\r\n\r\n  // Fix 4: More robust flattenFilters method\r\n  private flattenFilters(filter: CompositeFilterDescriptor): Array<{\r\n    field: string;\r\n    operator: string;\r\n    value: any;\r\n  }> {\r\n    const filters: Array<{\r\n      field: string;\r\n      operator: string;\r\n      value: any;\r\n    }> = [];\r\n\r\n    if (!filter || !filter.filters) {\r\n      return filters;\r\n    }\r\n\r\n    filter.filters.forEach((f: any) => {\r\n      if (f && 'field' in f) {\r\n        // It's a FilterDescriptor\r\n        filters.push({\r\n          field: f.field,\r\n          operator: f.operator,\r\n          value: f.value,\r\n        });\r\n      } else if (f && 'filters' in f) {\r\n        // It's a CompositeFilterDescriptor\r\n        filters.push(...this.flattenFilters(f));\r\n      }\r\n    });\r\n\r\n    return filters;\r\n  }\r\n\r\n  // Fix 5: More robust loadGridState method\r\n  private loadGridState(): void {\r\n    try {\r\n      const savedState = localStorage.getItem(this.GRID_STATE_KEY);\r\n\r\n      if (!savedState) {\r\n        return;\r\n      }\r\n\r\n      const state: {\r\n        sort?: SortDescriptor[];\r\n        filter?: CompositeFilterDescriptor;\r\n        page?: {\r\n          size: number;\r\n          pageNumber: number;\r\n          totalElements: number;\r\n          totalPages: number;\r\n          orderBy: string;\r\n          orderDir: string;\r\n        };\r\n        skip?: number;\r\n        columnsVisibility?: Record<string, boolean>;\r\n        searchData?: string;\r\n        activeFilters?: Array<{\r\n          field: string;\r\n          operator: string;\r\n          value: any;\r\n        }>;\r\n        appliedFilters?: {\r\n          status?: string | null;\r\n          role?: string | null;\r\n        };\r\n        showAdvancedFilters?: boolean;\r\n      } = JSON.parse(savedState);\r\n\r\n      // Restore sort state\r\n      if (state && state.sort) {\r\n        this.sort = state.sort;\r\n        if (this.sort && this.sort.length > 0 && this.sort[0]) {\r\n          this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';\r\n          this.page.orderDir = this.sort[0].dir || 'desc';\r\n        }\r\n      }\r\n\r\n      // Restore filter state\r\n      if (state && state.filter) {\r\n        this.filter = state.filter;\r\n        this.gridFilter = state.filter;\r\n        this.activeFilters = state.activeFilters || [];\r\n      }\r\n\r\n      // Restore pagination state\r\n      if (state && state.page) {\r\n        this.page = state.page;\r\n      }\r\n\r\n      if (state && state.skip !== undefined) {\r\n        this.skip = state.skip;\r\n      }\r\n\r\n      // Restore column visibility\r\n      if (state && state.columnsVisibility) {\r\n        this.columnsVisibility = state.columnsVisibility;\r\n      }\r\n\r\n      // Restore search state\r\n      if (state && state.searchData) {\r\n        this.searchData = state.searchData;\r\n      }\r\n\r\n      // Restore advanced filter states\r\n      if (state && state.appliedFilters) {\r\n        this.appliedFilters = state.appliedFilters;\r\n      }\r\n\r\n      if (state && state.showAdvancedFilters !== undefined) {\r\n        this.showAdvancedFilters = state.showAdvancedFilters;\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading grid state:', error);\r\n      // If there's an error, use default state\r\n    }\r\n  }\r\n\r\n  // Old getHiddenField method removed - replaced with new system\r\n\r\n  // Grid state persistence methods\r\n  private saveGridState(): void {\r\n    const state: {\r\n      sort: SortDescriptor[];\r\n      filter: CompositeFilterDescriptor;\r\n      page: {\r\n        size: number;\r\n        pageNumber: number;\r\n        totalElements: number;\r\n        totalPages: number;\r\n        orderBy: string;\r\n        orderDir: string;\r\n      };\r\n      skip: number;\r\n      columnsVisibility: Record<string, boolean>;\r\n      searchData: string;\r\n      activeFilters: Array<{\r\n        field: string;\r\n        operator: string;\r\n        value: any;\r\n      }>;\r\n      appliedFilters: {\r\n        status?: string | null;\r\n        role?: string | null;\r\n      };\r\n      showAdvancedFilters: boolean;\r\n    } = {\r\n      sort: this.sort,\r\n      filter: this.filter,\r\n      page: this.page,\r\n      skip: this.skip,\r\n      columnsVisibility: this.columnsVisibility,\r\n      searchData: this.searchData,\r\n      activeFilters: this.activeFilters,\r\n      appliedFilters: this.appliedFilters,\r\n      showAdvancedFilters: this.showAdvancedFilters,\r\n    };\r\n\r\n    localStorage.setItem(this.GRID_STATE_KEY, JSON.stringify(state));\r\n  }\r\n\r\n  // Function to add a new company (calls edit function with ID 0)\r\n  add() {\r\n    this.edit(0);\r\n  }\r\n\r\n  // Function to open the edit modal for adding/editing a company\r\n  edit(id: number) {\r\n    console.log('Line: 413', 'call edit function: ', id);\r\n    // Configuration options for the modal dialog\r\n    const NgbModalOptions: {\r\n      size: string;\r\n      backdrop: boolean | 'static';\r\n      keyboard: boolean;\r\n      scrollable: boolean;\r\n    } = {\r\n      size: 'lg', // Large modal size\r\n      backdrop: 'static', // Prevents closing when clicking outside\r\n      keyboard: false, // Disables closing with the Escape key\r\n      scrollable: true, // Allows scrolling inside the modal\r\n    };\r\n\r\n    // Open the modal and load the AddCompaniesComponent\r\n    const modalRef = this.modalService.open(AddUserComponent, NgbModalOptions);\r\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\r\n    modalRef.componentInstance.id = id;\r\n    modalRef.componentInstance.defaultPermissions = this.permissionArray;\r\n    // Subscribe to the modal event when data is updated\r\n    modalRef.componentInstance.passEntry.subscribe((receivedEntry: boolean) => {\r\n      if (receivedEntry === true) {\r\n        // Reload the table data after a successful update\r\n        this.loadTable();\r\n      }\r\n    });\r\n  }\r\n\r\n  public deleteTemplate(item: UserData): void {\r\n    console.log('Delete template:', item);\r\n    // Implement delete functionality\r\n  }\r\n  public toggleExpand(): void {\r\n    // Find grid container element and toggle fullscreen class\r\n    const gridContainer = document.querySelector(\r\n      '.grid-container'\r\n    ) as HTMLElement;\r\n    if (gridContainer) {\r\n      gridContainer.classList.toggle('fullscreen-grid');\r\n      this.isExpanded = !this.isExpanded;\r\n      // Refresh grid after resize to ensure proper rendering\r\n      if (this.grid) {\r\n        this.grid.refresh();\r\n      }\r\n    }\r\n  }\r\n\r\n  // Enhanced export functionality\r\n  public onExportClick(event: { item: { value: string } }): void {\r\n    switch (event.item.value) {\r\n      case 'all':\r\n        this.exportAllUsers();\r\n        break;\r\n      case 'selected':\r\n        this.exportSelectedUsers();\r\n        break;\r\n      case 'filtered':\r\n        this.exportFilteredUsers();\r\n        break;\r\n      default:\r\n        console.warn('Unknown export option:', event.item.value);\r\n    }\r\n  }\r\n\r\n  private exportAllUsers(): void {\r\n    const exportParams = {\r\n      filters: {},\r\n      format: 'excel',\r\n    };\r\n\r\n    this.usersService.exportUsers(exportParams).subscribe({\r\n      next: (response: any) => {\r\n        if (response && response.exportData) {\r\n          this.downloadExcel(response.exportData, 'All_Users');\r\n        }\r\n      },\r\n      error: (error: unknown) => {\r\n        console.error('Error exporting users:', error);\r\n                                    this.customLayoutUtilsService.showError('Error exporting users', '');\r\n\r\n        //alert('Error exporting users. Please try again.');\r\n      },\r\n    });\r\n  }\r\n\r\n  private exportSelectedUsers(): void {\r\n    if (this.selectedUsers.length === 0) {\r\n                                  this.customLayoutUtilsService.showError('Please select users to export', '');\r\n\r\n      //alert('Please select users to export.');\r\n      return;\r\n    }\r\n\r\n    const exportParams = {\r\n      filters: {\r\n        userIds: this.selectedUsers.map((user) => user.UserId),\r\n      },\r\n      format: 'excel',\r\n    };\r\n\r\n    this.usersService.exportUsers(exportParams).subscribe({\r\n      next: (response: any) => {\r\n        if (response && response.exportData) {\r\n          this.downloadExcel(response.exportData, 'Selected_Users');\r\n        }\r\n      },\r\n      error: (error: unknown) => {\r\n        console.error('Error exporting selected users:', error);\r\n                                    this.customLayoutUtilsService.showError('Error exporting selected users', '');\r\n\r\n        //alert('Error exporting selected users. Please try again.');\r\n      },\r\n    });\r\n  }\r\n\r\n  private exportFilteredUsers(): void {\r\n    const exportParams = {\r\n      filters: {\r\n        status: this.appliedFilters.status,\r\n        role: this.appliedFilters.role,\r\n        searchTerm: this.searchData,\r\n      },\r\n      format: 'excel',\r\n    };\r\n\r\n    this.usersService.exportUsers(exportParams).subscribe({\r\n      next: (response: any) => {\r\n        if (response && response.exportData) {\r\n          this.downloadExcel(response.exportData, 'Filtered_Users');\r\n        }\r\n      },\r\n      error: (error: unknown) => {\r\n        console.error('Error exporting filtered users:', error);\r\n                                    this.customLayoutUtilsService.showError('Error exporting filtered users', '');\r\n\r\n        //alert('Error exporting filtered users. Please try again.');\r\n      },\r\n    });\r\n  }\r\n\r\n  private downloadExcel(data: any[], filename: string): void {\r\n    // This would typically use a library like xlsx or similar\r\n    // For now, we'll create a simple CSV download\r\n    const csvContent = this.convertToCSV(data);\r\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n    const link = document.createElement('a');\r\n    const url = URL.createObjectURL(blob);\r\n    link.setAttribute('href', url);\r\n    link.setAttribute(\r\n      'download',\r\n      `${filename}_${new Date().toISOString().split('T')[0]}.csv`\r\n    );\r\n    link.style.visibility = 'hidden';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  }\r\n\r\n  private convertToCSV(data: any[]): string {\r\n    if (data.length === 0) return '';\r\n\r\n    const headers = Object.keys(data[0]);\r\n    const csvRows = [headers.join(',')];\r\n\r\n    for (const row of data) {\r\n      const values = headers.map((header) => {\r\n        const value = row[header];\r\n        return typeof value === 'string' && value.includes(',')\r\n          ? `\"${value}\"`\r\n          : value;\r\n      });\r\n      csvRows.push(values.join(','));\r\n    }\r\n\r\n    return csvRows.join('\\n');\r\n  }\r\n\r\n  // NEW COLUMN VISIBILITY SYSTEM METHODS\r\n\r\n  /**\r\n   * Saves the current state of column visibility and order in the grid.\r\n   * This function categorizes columns into visible and hidden columns, records their titles,\r\n   * fields, and visibility status, and also captures the order of draggable columns.\r\n   * After gathering the necessary data, it sends this information to the backend for saving.\r\n   */\r\n  saveHead(): void {\r\n    // Check if loginUser is available\r\n    if (!this.loginUser || !this.loginUser.userId) {\r\n      console.error('loginUser not available:', this.loginUser);\r\n      this.customLayoutUtilsService.showError(\r\n        'User not logged in. Please refresh the page.',\r\n        ''\r\n      );\r\n      return;\r\n    }\r\n\r\n    const nonHiddenColumns: any[] = [];\r\n    const hiddenColumns: any[] = [];\r\n\r\n    if (this.grid && this.grid.columns) {\r\n      this.grid.columns.forEach((column: any) => {\r\n        if (!column.hidden) {\r\n          const columnData = {\r\n            title: column.title,\r\n            field: column.field,\r\n            hidden: column.hidden,\r\n          };\r\n          nonHiddenColumns.push(columnData);\r\n        } else {\r\n          const columnData = {\r\n            title: column.title,\r\n            field: column.field,\r\n            hidden: column.hidden,\r\n          };\r\n          hiddenColumns.push(columnData);\r\n        }\r\n      });\r\n    }\r\n\r\n    const draggableColumnsOrder = this.gridColumns\r\n      .filter((col) => !this.fixedColumns.includes(col))\r\n      .map((field, index) => ({\r\n        field,\r\n        orderIndex: index,\r\n      }));\r\n\r\n    // Prepare data for backend\r\n    const userData = {\r\n      pageName: 'Users',\r\n      userID: this.loginUser.userId,\r\n      hiddenData: hiddenColumns,\r\n      kendoColOrder: draggableColumnsOrder,\r\n      LoggedId: this.loginUser.userId,\r\n    };\r\n\r\n    // Show loading state\r\n    this.httpUtilService.loadingSubject.next(true);\r\n\r\n    // Save to backend\r\n    this.kendoColumnService.createHideFields(userData).subscribe({\r\n      next: (res) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        if (!res.isFault) {\r\n          // Update local state\r\n          this.hiddenData = hiddenColumns;\r\n          this.kendoColOrder = draggableColumnsOrder;\r\n          this.hiddenFields = this.hiddenData.map((col: any) => col.field);\r\n\r\n          // Also save to localStorage as backup\r\n          this.kendoColumnService.saveToLocalStorage(userData);\r\n\r\n          this.customLayoutUtilsService.showSuccess(\r\n            res.message || 'Column settings saved successfully.',\r\n            ''\r\n          );\r\n        } else {\r\n          this.customLayoutUtilsService.showError(\r\n            res.message || 'Failed to save column settings.',\r\n            ''\r\n          );\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (error) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        console.error('Error saving column settings:', error);\r\n\r\n        // Fallback to localStorage on error\r\n        this.kendoColumnService.saveToLocalStorage(userData);\r\n\r\n        // Update local state\r\n        this.hiddenData = hiddenColumns;\r\n        this.kendoColOrder = draggableColumnsOrder;\r\n        this.hiddenFields = this.hiddenData.map((col: any) => col.field);\r\n\r\n        this.customLayoutUtilsService.showError(\r\n          'Failed to save to server. Settings saved locally.',\r\n          ''\r\n        );\r\n        this.cdr.markForCheck();\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Reset the current state of column visibility and order in the grid to its original state.\r\n   * This function resets columns to default visibility and order, and saves the reset state.\r\n   */\r\n  resetTable(): void {\r\n    // Check if loginUser is available\r\n    if (!this.loginUser || !this.loginUser.userId) {\r\n      console.error('loginUser not available:', this.loginUser);\r\n      this.customLayoutUtilsService.showError(\r\n        'User not logged in. Please refresh the page and try again.',\r\n        ''\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Double-check authentication token\r\n    const token = this.AppService.getLocalStorageItem('permitToken', true);\r\n    if (!token) {\r\n      console.error('Authentication token not found');\r\n      this.customLayoutUtilsService.showError(\r\n        'Authentication token not found. Please login again.',\r\n        ''\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Reset all state variables\r\n    this.searchData = '';\r\n    this.activeFilters = [];\r\n    this.filter = { logic: 'and', filters: [] };\r\n    this.skip = 0;\r\n    this.page.pageNumber = 0;\r\n    this.gridColumns = [...this.defaultColumns];\r\n\r\n    // Reset sort state to default\r\n    this.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\r\n    this.page.orderBy = 'lastUpdatedDate';\r\n    this.page.orderDir = 'desc';\r\n\r\n    // Reset advanced filters\r\n    this.appliedFilters = {};\r\n\r\n    // Reset advanced filters visibility\r\n    this.showAdvancedFilters = false;\r\n\r\n    // Reset column order index\r\n    if (this.grid && this.grid.columns) {\r\n      this.grid.columns.forEach((column: any) => {\r\n        const index = this.gridColumns.indexOf(column.field);\r\n        if (index !== -1) {\r\n          column.orderIndex = index;\r\n        }\r\n        // Reset column visibility - show all columns\r\n        if (column.field && column.field !== 'action') {\r\n          column.hidden = false;\r\n        }\r\n      });\r\n    }\r\n\r\n    // Clear hidden columns\r\n    this.hiddenData = [];\r\n    this.kendoColOrder = [];\r\n    this.hiddenFields = [];\r\n\r\n    // Reset the Kendo Grid's internal state\r\n    if (this.grid) {\r\n      // Clear all filters\r\n      this.grid.filter = { logic: 'and', filters: [] };\r\n      \r\n      // Reset sorting\r\n      this.grid.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\r\n      \r\n      // Reset to first page\r\n      this.grid.skip = 0;\r\n      this.grid.pageSize = this.page.size;\r\n    }\r\n\r\n    // Prepare reset data\r\n    const userData = {\r\n      pageName: 'Users',\r\n      userID: this.loginUser.userId,\r\n      hiddenData: [],\r\n      kendoColOrder: [],\r\n      LoggedId: this.loginUser.userId,\r\n    };\r\n\r\n    // Only clear local settings; do not call server\r\n    this.kendoColumnService.clearFromLocalStorage('Users');\r\n\r\n    // Show loader and refresh grid\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.cdr.detectChanges();\r\n\r\n    // Force grid refresh to apply all changes\r\n    if (this.grid) {\r\n      setTimeout(() => {\r\n        this.grid.refresh();\r\n        this.grid.reset();\r\n      }, 100);\r\n    }\r\n\r\n    this.loadTable();\r\n  }\r\n\r\n  /**\r\n   * Loads and applies the saved column order from the user preferences or configuration.\r\n   * This function updates the grid column order, ensuring the fixed columns remain in place\r\n   * and the draggable columns are ordered according to the saved preferences.\r\n   */\r\n  loadSavedColumnOrder(kendoColOrder: any): void {\r\n    try {\r\n      const savedOrder = kendoColOrder;\r\n      if (savedOrder) {\r\n        const parsedOrder = savedOrder;\r\n        if (Array.isArray(parsedOrder) && parsedOrder.length > 0) {\r\n          // Get only the draggable columns from saved order\r\n          const savedDraggableColumns = parsedOrder\r\n            .sort((a, b) => a.orderIndex - b.orderIndex)\r\n            .map((col) => col.field)\r\n            .filter((field) => !this.fixedColumns.includes(field));\r\n\r\n          // Add any missing draggable columns at the end\r\n          const missingColumns = this.draggableColumns.filter(\r\n            (col) => !savedDraggableColumns.includes(col)\r\n          );\r\n\r\n          // Combine fixed columns with saved draggable columns\r\n          this.gridColumns = [\r\n            ...this.fixedColumns,\r\n            ...savedDraggableColumns,\r\n            ...missingColumns,\r\n          ];\r\n        } else {\r\n          this.gridColumns = [...this.defaultColumns];\r\n        }\r\n      } else {\r\n        this.gridColumns = [...this.defaultColumns];\r\n      }\r\n    } catch (error) {\r\n      this.gridColumns = [...this.defaultColumns];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Checks if a given column is marked as hidden.\r\n   * This function searches the `hiddenFields` array to determine if the column should be hidden.\r\n   */\r\n  getHiddenField(columnName: any): boolean {\r\n    return this.hiddenFields.indexOf(columnName) > -1;\r\n  }\r\n\r\n  /**\r\n   * Handles the column reordering event triggered when a column is moved by the user.\r\n   * The function checks if the column being moved is in the fixed columns and prevents reordering\r\n   * of fixed columns.\r\n   */\r\n  onColumnReorder(event: any): void {\r\n    const { columns, newIndex, oldIndex } = event;\r\n\r\n    // Prevent reordering of fixed columns\r\n    if (\r\n      this.fixedColumns.includes(columns[oldIndex].field) ||\r\n      this.fixedColumns.includes(columns[newIndex].field)\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    // Update the gridColumns array\r\n    const reorderedColumns = [...this.gridColumns];\r\n    const [movedColumn] = reorderedColumns.splice(oldIndex, 1);\r\n    reorderedColumns.splice(newIndex, 0, movedColumn);\r\n\r\n    this.gridColumns = reorderedColumns;\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  /**\r\n   * Handles column visibility changes from the Kendo Grid.\r\n   * Updates the local state when columns are shown or hidden.\r\n   */\r\n  updateColumnVisibility(event: any): void {\r\n    if (this.isExpanded === false) {\r\n      if (this.grid && this.grid.columns) {\r\n        this.grid.columns.forEach((column: any) => {\r\n          const columnData = {\r\n            title: column.title,\r\n            field: column.field,\r\n            hidden: column.hidden,\r\n          };\r\n          if (column.hidden) {\r\n            const exists = this.hiddenData.some(\r\n              (item: any) =>\r\n                item.field === columnData.field && item.hidden === true\r\n            );\r\n            if (!exists) {\r\n              this.hiddenData.push(columnData);\r\n            }\r\n          } else {\r\n            let indexExists = this.hiddenData.findIndex(\r\n              (item: any) =>\r\n                item.field === columnData.field && item.hidden === true\r\n            );\r\n            if (indexExists !== -1) {\r\n              this.hiddenData.splice(indexExists, 1);\r\n            }\r\n          }\r\n        });\r\n        this.hiddenFields = this.hiddenData.map((col: any) => col.field);\r\n        this.cdr.markForCheck();\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Loads the saved column configuration from the backend or localStorage as fallback.\r\n   * This method is called during component initialization to restore user preferences.\r\n   */\r\n  private loadColumnConfigFromDatabase(): void {\r\n    try {\r\n      // First try to load from backend\r\n      if (this.loginUser && this.loginUser.userId) {\r\n        this.kendoColumnService\r\n          .getHideFields({\r\n            pageName: 'Users',\r\n            userID: this.loginUser.userId,\r\n          })\r\n          .subscribe({\r\n            next: (res) => {\r\n              if (!res.isFault && res.Data) {\r\n                this.kendoHide = res.Data;\r\n                this.hiddenData = res.Data.hideData\r\n                  ? JSON.parse(res.Data.hideData)\r\n                  : [];\r\n                this.kendoInitColOrder = res.Data.kendoColOrder\r\n                  ? JSON.parse(res.Data.kendoColOrder)\r\n                  : [];\r\n                this.hiddenFields = this.hiddenData.map(\r\n                  (col: any) => col.field\r\n                );\r\n\r\n                // Update grid columns based on the hidden fields\r\n                if (this.grid && this.grid.columns) {\r\n                  this.grid.columns.forEach((column: any) => {\r\n                    if (\r\n                      this.hiddenData.some(\r\n                        (item: any) =>\r\n                          item.title === column.title && item.hidden\r\n                      )\r\n                    ) {\r\n                      column.includeInChooser = true;\r\n                      column.hidden = true;\r\n                    } else {\r\n                      column.hidden = false;\r\n                    }\r\n                  });\r\n                }\r\n\r\n                // Load saved column order and update grid\r\n                this.loadSavedColumnOrder(this.kendoInitColOrder);\r\n\r\n                // Also save to localStorage as backup\r\n                this.kendoColumnService.saveToLocalStorage({\r\n                  pageName: 'Users',\r\n                  userID: this.loginUser.userId,\r\n                  hiddenData: this.hiddenData,\r\n                  kendoColOrder: this.kendoInitColOrder,\r\n                });\r\n              }\r\n            },\r\n            error: (error) => {\r\n              console.error(\r\n                'Error loading from backend, falling back to localStorage:',\r\n                error\r\n              );\r\n              this.loadFromLocalStorageFallback();\r\n            },\r\n          });\r\n      } else {\r\n        // Fallback to localStorage if no user ID\r\n        this.loadFromLocalStorageFallback();\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading column configuration:', error);\r\n      this.loadFromLocalStorageFallback();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Fallback method to load column configuration from localStorage\r\n   */\r\n  private loadFromLocalStorageFallback(): void {\r\n    try {\r\n      const savedConfig = this.kendoColumnService.getFromLocalStorage(\r\n        'Users',\r\n        this.loginUser?.UserId || 0\r\n      );\r\n      if (savedConfig) {\r\n        this.kendoHide = savedConfig;\r\n        this.hiddenData = savedConfig.hiddenData || [];\r\n        this.kendoInitColOrder = savedConfig.kendoColOrder || [];\r\n        this.hiddenFields = this.hiddenData.map((col: any) => col.field);\r\n\r\n        // Update grid columns based on the hidden fields\r\n        if (this.grid && this.grid.columns) {\r\n          this.grid.columns.forEach((column: any) => {\r\n            if (\r\n              this.hiddenData.some(\r\n                (item: any) => item.title === column.title && item.hidden\r\n              )\r\n            ) {\r\n              column.includeInChooser = true;\r\n              column.hidden = true;\r\n            } else {\r\n              column.hidden = false;\r\n            }\r\n          });\r\n        }\r\n\r\n        // Load saved column order and update grid\r\n        this.loadSavedColumnOrder(this.kendoInitColOrder);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading from localStorage fallback:', error);\r\n    }\r\n  }\r\n}\r\n", "<!-- Full Screen Loading Overlay -->\r\n<div *ngIf=\"loading || isLoading\" class=\"fullscreen-loading-overlay\">\r\n  <div class=\"loading-content\">\r\n    <div class=\"custom-colored-spinner\" role=\"status\">\r\n      <span class=\"visually-hidden\">Loading...</span>\r\n    </div>\r\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\r\n  </div>\r\n</div>\r\n\r\n<div class=\"grid-container\">\r\n  <kendo-grid\r\n    #normalGrid\r\n    [data]=\"serverSideRowData\"\r\n    [pageSize]=\"page.size\"\r\n    [sort]=\"sort\"\r\n    [pageable]=\"{\r\n      pageSizes: [10, 15, 20, 50, 100],\r\n      previousNext: true,\r\n      info: true,\r\n      type: 'numeric',\r\n      buttonCount: 5\r\n    }\"\r\n    [sortable]=\"{ allowUnsort: true, mode: 'single' }\"\r\n    [groupable]=\"false\"\r\n    [selectable]=\"{ checkboxOnly: true, mode: 'multiple' }\"\r\n    (columnReorder)=\"onColumnReorder($event)\"\r\n    (selectionChange)=\"onSelectionChange($event)\"\r\n    [reorderable]=\"true\"\r\n    style=\"width: auto; overflow-x: auto\"\r\n    [resizable]=\"false\"\r\n    [height]=\"720\"\r\n    [skip]=\"page.pageNumber * page.size\"\r\n    [filter]=\"filter\"\r\n    [columnMenu]=\"{ filter: true }\"\r\n    (filterChange)=\"filterChange($event)\"\r\n    (pageChange)=\"pageChange($event)\"\r\n    (sortChange)=\"onSortChange($event)\"\r\n    (columnVisibilityChange)=\"updateColumnVisibility($event)\"\r\n  >\r\n    <ng-template kendoGridToolbarTemplate>\r\n      <!-- Search Section -->\r\n      <div class=\"d-flex align-items-center me-3 search-section\">\r\n        <kendo-textbox\r\n          [style.width.px]=\"500\"\r\n          placeholder=\"Search...\"\r\n          [(ngModel)]=\"searchData\"\r\n          [clearButton]=\"true\"\r\n          (keydown)=\"onSearchKeyDown($event)\"\r\n          (ngModelChange)=\"onSearchChange()\"\r\n          (clear)=\"clearSearch()\"\r\n        ></kendo-textbox>\r\n      </div>\r\n\r\n      <kendo-grid-spacer></kendo-grid-spacer>\r\n\r\n      <!-- Total Count - Repositioned to the right -->\r\n      <div class=\"d-flex align-items-center me-3\">\r\n        <span class=\"text-muted\">Total: </span>\r\n        <span class=\"fw-bold ms-1\">{{ page.totalElements || 0 }}</span>\r\n      </div>\r\n\r\n      <!-- Action Buttons -->\r\n      <button type=\"button\" class=\"btn btn-icon btn-sm me-2\" (click)=\"add()\" title=\"Add User\">\r\n        <span\r\n          [inlineSVG]=\"'./assets/media/icons/duotune/arrows/arr075.svg'\"\r\n          class=\"svg-icon svg-icon-3 text-primary\"\r\n        ></span>\r\n      </button>\r\n\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-icon btn-sm me-2\"\r\n        (click)=\"toggleExpand()\"\r\n        title=\"Toggle Grid Expansion\"\r\n      >\r\n        <i\r\n          class=\"fas text-secondary\"\r\n          [class.fa-expand]=\"!isExpanded\"\r\n          [class.fa-compress]=\"isExpanded\"\r\n        ></i>\r\n      </button>\r\n\r\n      <!-- <kendo-dropdownbutton\r\n        text=\"Export Excel\"\r\n        iconClass=\"fas fa-file-excel\"\r\n        [data]=\"exportOptions\"\r\n        class=\"custom-dropdown\"\r\n        (itemClick)=\"onExportClick($event)\"\r\n        title=\"Export\"\r\n      >\r\n      </kendo-dropdownbutton> -->\r\n\r\n      <!-- Save Column Settings Button -->\r\n      <!-- <button\r\n        type=\"button\"\r\n        class=\"btn btn-icon btn-sm me-2\"\r\n        (click)=\"saveHead()\"\r\n        title=\"Save Column Settings\"\r\n      >\r\n        <i class=\"fas fa-save text-success\"></i>\r\n      </button> -->\r\n\r\n      <!-- Reset Button -->\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-icon btn-sm me-2\"\r\n        (click)=\"resetTable()\"\r\n        title=\"Reset to Default\"\r\n      >\r\n        <i class=\"fas fa-undo text-warning\"></i>\r\n      </button>\r\n\r\n      <!-- Refresh Button -->\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-icon btn-sm me-2\"\r\n        (click)=\"refreshGrid()\"\r\n        title=\"Refresh Grid Data\"\r\n      >\r\n        <i class=\"fas fa-sync-alt text-info\"></i>\r\n      </button>\r\n    </ng-template>\r\n\r\n    <!-- Advanced Filters Panel -->\r\n    <ng-template kendoGridToolbarTemplate>\r\n      <div\r\n        *ngIf=\"showAdvancedFilters\"\r\n        class=\"advanced-filters-panel p-3 bg-light border-bottom\"\r\n      >\r\n        <div class=\"row\">\r\n          <div class=\"col-md-3\">\r\n            <label class=\"form-label\">Status</label>\r\n            <kendo-dropdownlist\r\n              [data]=\"advancedFilterOptions.status\"\r\n              [(ngModel)]=\"appliedFilters.status\"\r\n              textField=\"text\"\r\n              valueField=\"value\"\r\n              placeholder=\"Select Status\"\r\n            >\r\n            </kendo-dropdownlist>\r\n          </div>\r\n          <div class=\"col-md-3\">\r\n            <label class=\"form-label\">Role</label>\r\n            <kendo-dropdownlist\r\n              [data]=\"advancedFilterOptions.roles\"\r\n              [(ngModel)]=\"appliedFilters.role\"\r\n              textField=\"text\"\r\n              valueField=\"value\"\r\n              placeholder=\"Select Role\"\r\n            >\r\n            </kendo-dropdownlist>\r\n          </div>\r\n          <div class=\"col-md-3 d-flex align-items-end\">\r\n            <button\r\n              kendoButton\r\n              (click)=\"applyAdvancedFilters()\"\r\n              class=\"btn-primary me-2\"\r\n            >\r\n              <i class=\"fas fa-check\"></i> Apply Filters\r\n            </button>\r\n            <button\r\n              kendoButton\r\n              (click)=\"clearAllFilters()\"\r\n              class=\"btn-secondary\"\r\n            >\r\n              <i class=\"fas fa-times\"></i> Clear\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </ng-template>\r\n\r\n    <ng-container *ngFor=\"let column of gridColumns\">\r\n      <!-- Action Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'action'\"\r\n        title=\"Actions\"\r\n        [width]=\"125\"\r\n        [sticky]=\"true\"\r\n        [reorderable]=\"!fixedColumns.includes('action')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [includeInChooser]=\"false\"\r\n        [columnMenu]=\"false\"\r\n        [style]=\"{ 'background-color': '#efefef !important' }\"\r\n        [hidden]=\"getHiddenField('action')\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <a\r\n            title=\"Edit\"\r\n            class=\"btn btn-icon btn-sm\"\r\n            (click)=\"edit(dataItem.userId)\"\r\n          >\r\n            <span\r\n              [inlineSVG]=\"'./assets/media/icons/duotune/general/gen055.svg'\"\r\n              class=\"svg-icon svg-icon-3 svg-icon-primary\"\r\n            >\r\n            </span>\r\n          </a>\r\n          <!-- Delete button hidden -->\r\n          <!-- <a\r\n            title=\"Delete\"\r\n            class=\"btn btn-icon btn-sm\"\r\n            (click)=\"deleteUser(dataItem)\"\r\n          >\r\n            <span\r\n              [inlineSVG]=\"'./assets/media/icons/duotune/general/gen027.svg'\"\r\n              class=\"svg-icon svg-icon-3 svg-icon-danger\"\r\n            >\r\n            </span>\r\n          </a> -->\r\n          <a\r\n            *ngIf=\"dataItem.IsLocked\"\r\n            title=\"Unlock\"\r\n            class=\"btn btn-icon btn-sm\"\r\n            (click)=\"unlockUser(dataItem)\"\r\n          >\r\n            <span\r\n              [inlineSVG]=\"'./assets/media/icons/duotune/general/gen037.svg'\"\r\n              class=\"svg-icon svg-icon-3 svg-icon-warning\"\r\n            >\r\n            </span>\r\n          </a>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- First Name Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'userFullName'\"\r\n        field=\"userFullName\"\r\n        title=\"Name\"\r\n        [width]=\"150\"\r\n        [sticky]=\"true\"\r\n        [reorderable]=\"!fixedColumns.includes('userFullName')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [includeInChooser]=\"false\"\r\n        [hidden]=\"getHiddenField('userFullName')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span class=\"fw-bolder cursor-pointer\">\r\n              {{ dataItem.userFullName }}\r\n            </span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Last Name Column -->\r\n      <!-- <kendo-grid-column *ngIf=\"column === 'lastName'\"\r\n        field=\"LastName\"\r\n        title=\"Last Name\"\r\n        [width]=\"150\"\r\n        [sticky]=\"true\"\r\n        [reorderable]=\"!fixedColumns.includes('LastName')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3','font-weight':'600' }\"\r\n        [includeInChooser]=\"false\"\r\n        [hidden]=\"getHiddenField('LastName')\"\r\n        [filterable]=\"true\">\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span class=\"fw-bolder cursor-pointer\">\r\n              {{ dataItem.LastName }}\r\n            </span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu [column]=\"column\" [filter]=\"filter\" [extra]=\"true\">\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\r\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column> -->\r\n\r\n      <!-- Email Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'email'\"\r\n        field=\"email\"\r\n        title=\"Email\"\r\n        [width]=\"250\"\r\n        [sticky]=\"false\"\r\n        [reorderable]=\"!fixedColumns.includes('email')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('email')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span [innerHTML]=\"dataItem.email || '-'\"></span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Title Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'title'\"\r\n        field=\"title\"\r\n        title=\"Title\"\r\n        [width]=\"120\"\r\n        [sticky]=\"false\"\r\n        [reorderable]=\"!fixedColumns.includes('title')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('title')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span [innerHTML]=\"dataItem.title || '-'\"></span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"true\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\r\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Phone Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'phoneNo'\"\r\n        field=\"phoneNo\"\r\n        title=\"Phone\"\r\n        [width]=\"120\"\r\n        [sticky]=\"false\"\r\n        [reorderable]=\"!fixedColumns.includes('phoneNo')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('phoneNo')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span [innerHTML]=\"dataItem.phoneNo || '-'\"></span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Role Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'roleName'\"\r\n        field=\"roleName\"\r\n        title=\"Role\"\r\n        [width]=\"120\"\r\n        [sticky]=\"false\"\r\n        [reorderable]=\"!fixedColumns.includes('roleName')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('roleName')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span [innerHTML]=\"dataItem.roleName || '-'\"></span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Status Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'userStatus'\"\r\n        field=\"userStatus\"\r\n        title=\"Status\"\r\n        [width]=\"100\"\r\n        [sticky]=\"false\"\r\n        [reorderable]=\"!fixedColumns.includes('userStatus')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('userStatus')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <span\r\n            *ngIf=\"dataItem.userStatus === 'Active'\"\r\n            ngbTooltip=\"Active\"\r\n            [inlineSVG]=\"'./assets/media/icons/duotune/general/gen037.svg'\"\r\n            class=\"svg-icon svg-icon-3 svg-icon-success\"\r\n            style=\"margin-left: 1.5rem\"\r\n          >\r\n          </span>\r\n          <span\r\n            *ngIf=\"dataItem.userStatus === 'Inactive'\"\r\n            ngbTooltip=\"Inactive\"\r\n            [inlineSVG]=\"'./assets/media/icons/duotune/general/gen040.svg'\"\r\n            class=\"svg-icon svg-icon-3 svg-icon-danger text-danger\"\r\n            style=\"margin-left: 1.5rem\"\r\n          >\r\n          </span>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-dropdownlist\r\n            [data]=\"filterOptions\"\r\n            [value]=\"getFilterValue(filter, column)\"\r\n            (valueChange)=\"onStatusFilterChange($event, filter, column)\"\r\n            textField=\"text\"\r\n            valueField=\"value\"\r\n          >\r\n          </kendo-dropdownlist>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Updated Date Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'lastUpdatedDate'\"\r\n        field=\"lastUpdatedDate\"\r\n        title=\"Updated Date\"\r\n        [width]=\"160\"\r\n        [sticky]=\"false\"\r\n        [reorderable]=\"!fixedColumns.includes('lastUpdatedDate')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        filter=\"date\"\r\n        format=\"MM/dd/yyyy\"\r\n        [maxResizableWidth]=\"240\"\r\n        [hidden]=\"getHiddenField('lastUpdatedDate')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        \r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <span class=\"text-gray-600 fs-1r\">{{\r\n            dataItem.lastUpdatedDate | date : \"MM/dd/yyyy hh:mm a\"\r\n          }}</span>\r\n          <br /><span class=\"text-gray-600 fs-1r\">{{\r\n            dataItem.lastUpdatedByFullName\r\n          }}</span>\r\n        </ng-template>\r\n        <ng-template\r\n          kendoGridFilterMenuTemplate\r\n          let-filter\r\n          let-column=\"column\"\r\n          let-filterService=\"filterService\"\r\n        >\r\n          <kendo-grid-date-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            operator=\"eq\"\r\n            [filterService]=\"filterService\"\r\n          >\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-before-operator></kendo-filter-before-operator>\r\n            <kendo-filter-before-eq-operator></kendo-filter-before-eq-operator>\r\n            <kendo-filter-after-operator></kendo-filter-after-operator>\r\n            <kendo-filter-after-eq-operator></kendo-filter-after-eq-operator>\r\n          </kendo-grid-date-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n    </ng-container>\r\n\r\n    <ng-template kendoGridNoRecordsTemplate>\r\n      <div class=\"custom-no-records\" *ngIf=\"!loading && !isLoading\">\r\n        <div class=\"text-center\">\r\n          <i class=\"fas fa-users text-muted mb-2\" style=\"font-size: 2rem\"></i>\r\n          <p class=\"text-muted\">No users found</p>\r\n          <button kendoButton (click)=\"loadTable()\" class=\"btn-primary\">\r\n            <i class=\"fas fa-refresh me-2\"></i>Refresh\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </ng-template>\r\n  </kendo-grid>\r\n</div>\r\n"], "mappings": ";AAeA,SACEA,OAAO,EACPC,YAAY,EACZC,oBAAoB,QAEf,MAAM;AACb,SAAiBC,eAAe,QAAwB,iBAAiB;AAKzE,SAASC,gBAAgB,QAAQ,gCAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICtB3DC,EAHN,CAAAC,cAAA,aAAqE,aACtC,aACuB,eAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,cAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;;;;IAmCEH,EADF,CAAAC,cAAA,cAA2D,wBASxD;IALCD,EAAA,CAAAI,gBAAA,2BAAAC,gFAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAG,UAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,UAAA,GAAAN,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAwB;IAIxBN,EAFA,CAAAc,UAAA,qBAAAC,0EAAAT,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAWJ,MAAA,CAAAO,eAAA,CAAAV,MAAA,CAAuB;IAAA,EAAC,2BAAAD,gFAAA;MAAAL,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAClBJ,MAAA,CAAAQ,cAAA,EAAgB;IAAA,EAAC,mBAAAC,wEAAA;MAAAlB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CACzBJ,MAAA,CAAAU,WAAA,EAAa;IAAA,EAAC;IAE3BnB,EADG,CAAAG,YAAA,EAAgB,EACb;IAENH,EAAA,CAAAoB,SAAA,wBAAuC;IAIrCpB,EADF,CAAAC,cAAA,cAA4C,eACjB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;IAGNH,EAAA,CAAAC,cAAA,iBAAwF;IAAjCD,EAAA,CAAAc,UAAA,mBAAAO,iEAAA;MAAArB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAa,GAAA,EAAK;IAAA,EAAC;IACpEtB,EAAA,CAAAoB,SAAA,eAGQ;IACVpB,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAS,kEAAA;MAAAvB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAe,YAAA,EAAc;IAAA,EAAC;IAGxBxB,EAAA,CAAAoB,SAAA,aAIK;IACPpB,EAAA,CAAAG,YAAA,EAAS;IAuBTH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAW,kEAAA;MAAAzB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAiB,UAAA,EAAY;IAAA,EAAC;IAGtB1B,EAAA,CAAAoB,SAAA,aAAwC;IAC1CpB,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAa,kEAAA;MAAA3B,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAmB,WAAA,EAAa;IAAA,EAAC;IAGvB5B,EAAA,CAAAoB,SAAA,aAAyC;IAC3CpB,EAAA,CAAAG,YAAA,EAAS;;;;IA7ELH,EAAA,CAAA6B,SAAA,EAAsB;IAAtB7B,EAAA,CAAA8B,WAAA,oBAAsB;IAEtB9B,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAAG,UAAA,CAAwB;IACxBZ,EAAA,CAAAgC,UAAA,qBAAoB;IAYKhC,EAAA,CAAA6B,SAAA,GAA6B;IAA7B7B,EAAA,CAAAiC,iBAAA,CAAAxB,MAAA,CAAAyB,IAAA,CAAAC,aAAA,MAA6B;IAMtDnC,EAAA,CAAA6B,SAAA,GAA8D;IAA9D7B,EAAA,CAAAgC,UAAA,+DAA8D;IAa9DhC,EAAA,CAAA6B,SAAA,GAA+B;IAC/B7B,EADA,CAAAoC,WAAA,eAAA3B,MAAA,CAAA4B,UAAA,CAA+B,gBAAA5B,MAAA,CAAA4B,UAAA,CACC;;;;;;IAqD9BrC,EANN,CAAAC,cAAA,cAGC,cACkB,cACO,gBACM;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxCH,EAAA,CAAAC,cAAA,6BAMC;IAJCD,EAAA,CAAAI,gBAAA,2BAAAkC,2FAAAhC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAA+B,cAAA,CAAAC,MAAA,EAAAnC,MAAA,MAAAG,MAAA,CAAA+B,cAAA,CAAAC,MAAA,GAAAnC,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAmC;IAMvCN,EADE,CAAAG,YAAA,EAAqB,EACjB;IAEJH,EADF,CAAAC,cAAA,cAAsB,gBACM;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtCH,EAAA,CAAAC,cAAA,6BAMC;IAJCD,EAAA,CAAAI,gBAAA,2BAAAsC,2FAAApC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAA+B,cAAA,CAAAG,IAAA,EAAArC,MAAA,MAAAG,MAAA,CAAA+B,cAAA,CAAAG,IAAA,GAAArC,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAiC;IAMrCN,EADE,CAAAG,YAAA,EAAqB,EACjB;IAEJH,EADF,CAAAC,cAAA,eAA6C,kBAK1C;IAFCD,EAAA,CAAAc,UAAA,mBAAA8B,wEAAA;MAAA5C,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAoC,oBAAA,EAAsB;IAAA,EAAC;IAGhC7C,EAAA,CAAAoB,SAAA,aAA4B;IAACpB,EAAA,CAAAE,MAAA,uBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAc,UAAA,mBAAAgC,wEAAA;MAAA9C,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAsC,eAAA,EAAiB;IAAA,EAAC;IAG3B/C,EAAA,CAAAoB,SAAA,aAA4B;IAACpB,EAAA,CAAAE,MAAA,eAC/B;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IApCEH,EAAA,CAAA6B,SAAA,GAAqC;IAArC7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAuC,qBAAA,CAAAP,MAAA,CAAqC;IACrCzC,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAA+B,cAAA,CAAAC,MAAA,CAAmC;IAUnCzC,EAAA,CAAA6B,SAAA,GAAoC;IAApC7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAuC,qBAAA,CAAAC,KAAA,CAAoC;IACpCjD,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAA+B,cAAA,CAAAG,IAAA,CAAiC;;;;;IApBzC3C,EAAA,CAAAkD,UAAA,IAAAC,8CAAA,mBAGC;;;;IAFEnD,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAA2C,mBAAA,CAAyB;;;;;;IAoFxBpD,EAAA,CAAAC,cAAA,YAKC;IADCD,EAAA,CAAAc,UAAA,mBAAAuC,mGAAA;MAAArD,EAAA,CAAAO,aAAA,CAAA+C,GAAA;MAAA,MAAAC,WAAA,GAAAvD,EAAA,CAAAU,aAAA,GAAA8C,SAAA;MAAA,MAAA/C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAgD,UAAA,CAAAF,WAAA,CAAoB;IAAA,EAAC;IAE9BvD,EAAA,CAAAoB,SAAA,eAIO;IACTpB,EAAA,CAAAG,YAAA,EAAI;;;IAJAH,EAAA,CAAA6B,SAAA,EAA+D;IAA/D7B,EAAA,CAAAgC,UAAA,gEAA+D;;;;;;IA9BnEhC,EAAA,CAAAC,cAAA,YAIC;IADCD,EAAA,CAAAc,UAAA,mBAAA4C,+FAAA;MAAA,MAAAH,WAAA,GAAAvD,EAAA,CAAAO,aAAA,CAAAoD,GAAA,EAAAH,SAAA;MAAA,MAAA/C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAmD,IAAA,CAAAL,WAAA,CAAAM,MAAA,CAAqB;IAAA,EAAC;IAE/B7D,EAAA,CAAAoB,SAAA,eAIO;IACTpB,EAAA,CAAAG,YAAA,EAAI;IAaJH,EAAA,CAAAkD,UAAA,IAAAY,+EAAA,gBAKC;;;;IAtBG9D,EAAA,CAAA6B,SAAA,EAA+D;IAA/D7B,EAAA,CAAAgC,UAAA,gEAA+D;IAkBhEhC,EAAA,CAAA6B,SAAA,EAAuB;IAAvB7B,EAAA,CAAAgC,UAAA,SAAAuB,WAAA,CAAAQ,QAAA,CAAuB;;;;;IArC9B/D,EAAA,CAAAC,cAAA,4BAWC;IACCD,EAAA,CAAAkD,UAAA,IAAAc,2EAAA,0BAAgD;IAqClDhE,EAAA,CAAAG,YAAA,EAAoB;;;;IAxClBH,EAAA,CAAAiE,UAAA,CAAAjE,EAAA,CAAAkE,eAAA,IAAAC,GAAA,EAAsD;IACtDnE,EAPA,CAAAgC,UAAA,cAAa,gBACE,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,WACiC,gBAAArE,EAAA,CAAAkE,eAAA,KAAAI,GAAA,EACuB,2BAC7C,qBACN,WAAA7D,MAAA,CAAA8D,cAAA,WAEe;;;;;IAwD/BvE,EADF,CAAAC,cAAA,UAAK,eACoC;IACrCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAFFH,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAwE,kBAAA,MAAAC,WAAA,CAAAC,YAAA,MACF;;;;;IAIF1E,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAAoB,SAAA,qCAAiE;IACnEpB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAAgC,UAAA,WAAA2C,UAAA,CAAiB,WAAAC,SAAA,CACA,gBACF;;;;;IAvBrB5E,EAAA,CAAAC,cAAA,4BAWC;IAQCD,EAPA,CAAAkD,UAAA,IAAA2B,2EAAA,0BAAgD,IAAAC,2EAAA,0BAOwB;IAU1E9E,EAAA,CAAAG,YAAA,EAAoB;;;;IAnBlBH,EANA,CAAAgC,UAAA,cAAa,gBACE,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,iBACuC,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACiB,2BAC7C,WAAA7D,MAAA,CAAA8D,cAAA,iBACe,oBACtB;;;;;IA+DjBvE,EAAA,CAAAC,cAAA,UAAK;IACHD,EAAA,CAAAoB,SAAA,eAAiD;IACnDpB,EAAA,CAAAG,YAAA,EAAM;;;;IADEH,EAAA,CAAA6B,SAAA,EAAmC;IAAnC7B,EAAA,CAAAgC,UAAA,cAAA+C,YAAA,CAAAC,KAAA,SAAAhF,EAAA,CAAAiF,cAAA,CAAmC;;;;;IAI3CjF,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAAoB,SAAA,qCAAiE;IACnEpB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAAgC,UAAA,WAAAkD,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IApBrBnF,EAAA,CAAAC,cAAA,4BAUC;IAMCD,EALA,CAAAkD,UAAA,IAAAkC,2EAAA,0BAAgD,IAAAC,2EAAA,0BAKwB;IAU1ErF,EAAA,CAAAG,YAAA,EAAoB;;;;IAjBlBH,EALA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,UAC+B,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACwB,WAAA7D,MAAA,CAAA8D,cAAA,UACrC,oBACf;;;;;IAgCjBvE,EAAA,CAAAC,cAAA,UAAK;IACHD,EAAA,CAAAoB,SAAA,eAAiD;IACnDpB,EAAA,CAAAG,YAAA,EAAM;;;;IADEH,EAAA,CAAA6B,SAAA,EAAmC;IAAnC7B,EAAA,CAAAgC,UAAA,cAAAsD,YAAA,CAAAC,KAAA,SAAAvF,EAAA,CAAAiF,cAAA,CAAmC;;;;;IAI3CjF,EAAA,CAAAC,cAAA,wCAIC;IAKCD,EAJA,CAAAoB,SAAA,qCAAiE,+BACZ,gCACE,uCACc,qCACJ;IACnEpB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAAgC,UAAA,WAAAwD,UAAA,CAAiB,WAAAC,UAAA,CACA,eACH;;;;;IApBpBzF,EAAA,CAAAC,cAAA,4BAUC;IAMCD,EALA,CAAAkD,UAAA,IAAAwC,2EAAA,0BAAgD,IAAAC,2EAAA,0BAKwB;IAa1E3F,EAAA,CAAAG,YAAA,EAAoB;;;;IApBlBH,EALA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,UAC+B,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACwB,WAAA7D,MAAA,CAAA8D,cAAA,UACrC,oBACf;;;;;IAmCjBvE,EAAA,CAAAC,cAAA,UAAK;IACHD,EAAA,CAAAoB,SAAA,eAAmD;IACrDpB,EAAA,CAAAG,YAAA,EAAM;;;;IADEH,EAAA,CAAA6B,SAAA,EAAqC;IAArC7B,EAAA,CAAAgC,UAAA,cAAA4D,YAAA,CAAAC,OAAA,SAAA7F,EAAA,CAAAiF,cAAA,CAAqC;;;;;IAI7CjF,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAAoB,SAAA,qCAAiE;IACnEpB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAAgC,UAAA,WAAA8D,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IApBrB/F,EAAA,CAAAC,cAAA,4BAUC;IAMCD,EALA,CAAAkD,UAAA,IAAA8C,2EAAA,0BAAgD,IAAAC,2EAAA,0BAKwB;IAU1EjG,EAAA,CAAAG,YAAA,EAAoB;;;;IAjBlBH,EALA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,YACiC,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACsB,WAAA7D,MAAA,CAAA8D,cAAA,YACnC,oBACjB;;;;;IAgCjBvE,EAAA,CAAAC,cAAA,UAAK;IACHD,EAAA,CAAAoB,SAAA,eAAoD;IACtDpB,EAAA,CAAAG,YAAA,EAAM;;;;IADEH,EAAA,CAAA6B,SAAA,EAAsC;IAAtC7B,EAAA,CAAAgC,UAAA,cAAAkE,YAAA,CAAAC,QAAA,SAAAnG,EAAA,CAAAiF,cAAA,CAAsC;;;;;IAI9CjF,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAAoB,SAAA,qCAAiE;IACnEpB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAAgC,UAAA,WAAAoE,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IApBrBrG,EAAA,CAAAC,cAAA,4BAUC;IAMCD,EALA,CAAAkD,UAAA,IAAAoD,2EAAA,0BAAgD,IAAAC,2EAAA,0BAKwB;IAU1EvG,EAAA,CAAAG,YAAA,EAAoB;;;;IAjBlBH,EALA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,aACkC,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACqB,WAAA7D,MAAA,CAAA8D,cAAA,aAClC,oBAClB;;;;;IAgCjBvE,EAAA,CAAAoB,SAAA,eAOO;;;IAJLpB,EAAA,CAAAgC,UAAA,gEAA+D;;;;;IAKjEhC,EAAA,CAAAoB,SAAA,eAOO;;;IAJLpB,EAAA,CAAAgC,UAAA,gEAA+D;;;;;IAHjEhC,EARA,CAAAkD,UAAA,IAAAsD,kFAAA,mBAMC,IAAAC,kFAAA,mBAQA;;;;IAbEzG,EAAA,CAAAgC,UAAA,SAAA0E,YAAA,CAAAC,UAAA,cAAsC;IAQtC3G,EAAA,CAAA6B,SAAA,EAAwC;IAAxC7B,EAAA,CAAAgC,UAAA,SAAA0E,YAAA,CAAAC,UAAA,gBAAwC;;;;;;IAS3C3G,EAAA,CAAAC,cAAA,6BAMC;IAHCD,EAAA,CAAAc,UAAA,yBAAA8F,sHAAAtG,MAAA;MAAA,MAAAuG,OAAA,GAAA7G,EAAA,CAAAO,aAAA,CAAAuG,IAAA;MAAA,MAAAC,UAAA,GAAAF,OAAA,CAAArD,SAAA;MAAA,MAAAwD,UAAA,GAAAH,OAAA,CAAAI,MAAA;MAAA,MAAAxG,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAeJ,MAAA,CAAAyG,oBAAA,CAAA5G,MAAA,EAAAyG,UAAA,EAAAC,UAAA,CAA4C;IAAA,EAAC;IAI9DhH,EAAA,CAAAG,YAAA,EAAqB;;;;;;IALnBH,EADA,CAAAgC,UAAA,SAAAvB,MAAA,CAAA0G,aAAA,CAAsB,UAAA1G,MAAA,CAAA2G,cAAA,CAAAL,UAAA,EAAAC,UAAA,EACkB;;;;;IAhC9ChH,EAAA,CAAAC,cAAA,4BAUC;IAmBCD,EAlBA,CAAAkD,UAAA,IAAAmE,2EAAA,0BAAgD,IAAAC,2EAAA,0BAkBwB;IAU1EtH,EAAA,CAAAG,YAAA,EAAoB;;;;IA9BlBH,EALA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,eACoC,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACmB,WAAA7D,MAAA,CAAA8D,cAAA,eAChC,oBACpB;;;;;IAiDjBvE,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,GAEhC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACTH,EAAA,CAAAoB,SAAA,SAAM;IAAApB,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,GAEtC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IALyBH,EAAA,CAAA6B,SAAA,EAEhC;IAFgC7B,EAAA,CAAAiC,iBAAA,CAAAjC,EAAA,CAAAuH,WAAA,OAAAC,YAAA,CAAAC,eAAA,wBAEhC;IACsCzH,EAAA,CAAA6B,SAAA,GAEtC;IAFsC7B,EAAA,CAAAiC,iBAAA,CAAAuF,YAAA,CAAAE,qBAAA,CAEtC;;;;;IAQF1H,EAAA,CAAAC,cAAA,sCAKC;IAMCD,EALA,CAAAoB,SAAA,+BAAqD,gCACE,mCACM,sCACM,kCACR,qCACM;IACnEpB,EAAA,CAAAG,YAAA,EAA8B;;;;;;IAR5BH,EAHA,CAAAgC,UAAA,WAAA2F,UAAA,CAAiB,WAAAC,UAAA,CACA,kBAAAC,iBAAA,CAEc;;;;;IAjCrC7H,EAAA,CAAAC,cAAA,4BAaC;IAUCD,EARA,CAAAkD,UAAA,IAAA4E,2EAAA,0BAAgD,IAAAC,2EAAA,0BAa/C;IAeH/H,EAAA,CAAAG,YAAA,EAAoB;;;;IA/BlBH,EARA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,oBACyC,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACc,0BAG9C,WAAA7D,MAAA,CAAA8D,cAAA,oBACmB,oBACzB;;;;;IAhSvBvE,EAAA,CAAAgI,uBAAA,GAAiD;IAoR/ChI,EAlRA,CAAAkD,UAAA,IAAA+E,6DAAA,iCAWC,IAAAC,6DAAA,gCAoDA,IAAAC,6DAAA,gCA4DA,IAAAC,6DAAA,gCA6BA,IAAAC,6DAAA,gCAgCA,IAAAC,6DAAA,gCA6BA,IAAAC,6DAAA,gCA6BA,IAAAC,6DAAA,gCA6CA;;;;;IA9RExI,EAAA,CAAA6B,SAAA,EAAyB;IAAzB7B,EAAA,CAAAgC,UAAA,SAAAyG,UAAA,cAAyB;IAoDzBzI,EAAA,CAAA6B,SAAA,EAA+B;IAA/B7B,EAAA,CAAAgC,UAAA,SAAAyG,UAAA,oBAA+B;IA6D/BzI,EAAA,CAAA6B,SAAA,EAAwB;IAAxB7B,EAAA,CAAAgC,UAAA,SAAAyG,UAAA,aAAwB;IA6BxBzI,EAAA,CAAA6B,SAAA,EAAwB;IAAxB7B,EAAA,CAAAgC,UAAA,SAAAyG,UAAA,aAAwB;IAgCxBzI,EAAA,CAAA6B,SAAA,EAA0B;IAA1B7B,EAAA,CAAAgC,UAAA,SAAAyG,UAAA,eAA0B;IA6B1BzI,EAAA,CAAA6B,SAAA,EAA2B;IAA3B7B,EAAA,CAAAgC,UAAA,SAAAyG,UAAA,gBAA2B;IA6B3BzI,EAAA,CAAA6B,SAAA,EAA6B;IAA7B7B,EAAA,CAAAgC,UAAA,SAAAyG,UAAA,kBAA6B;IA0C7BzI,EAAA,CAAA6B,SAAA,EAAkC;IAAlC7B,EAAA,CAAAgC,UAAA,SAAAyG,UAAA,uBAAkC;;;;;;IA+CnCzI,EADF,CAAAC,cAAA,cAA8D,cACnC;IACvBD,EAAA,CAAAoB,SAAA,YAAoE;IACpEpB,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAC,cAAA,iBAA8D;IAA1CD,EAAA,CAAAc,UAAA,mBAAA4H,uEAAA;MAAA1I,EAAA,CAAAO,aAAA,CAAAoI,IAAA;MAAA,MAAAlI,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAmI,SAAA,EAAW;IAAA,EAAC;IACvC5I,EAAA,CAAAoB,SAAA,YAAmC;IAAApB,EAAA,CAAAE,MAAA,eACrC;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IARNH,EAAA,CAAAkD,UAAA,IAAA2F,8CAAA,kBAA8D;;;;IAA9B7I,EAAA,CAAAgC,UAAA,UAAAvB,MAAA,CAAAqI,OAAA,KAAArI,MAAA,CAAAsI,SAAA,CAA4B;;;AD5alE,OAAM,MAAOC,iBAAiB;EAmNlBC,YAAA;EACAC,GAAA;EACAC,MAAA;EACAC,KAAA;EACAC,YAAA;EACDC,UAAA;EACCC,wBAAA;EACAC,eAAA;EACAC,kBAAA;EA1NeC,IAAI;EAE7B;EACOC,iBAAiB,GAAU,EAAE;EAC7BC,QAAQ,GAAU,EAAE;EACpBC,cAAc,GAAY,KAAK;EAE/Bf,OAAO,GAAY,KAAK;EACxBC,SAAS,GAAY,KAAK;EAEjCe,SAAS,GAAQ,EAAE;EAEnB;EACOlJ,UAAU,GAAW,EAAE;EACtBmJ,WAAW,GAAG,IAAIpK,OAAO,EAAU;EACnCqK,kBAAkB;EAE1B;EACOC,MAAM,GAA8B;IAAEC,KAAK,EAAE,KAAK;IAAEC,OAAO,EAAE;EAAE,CAAE;EACjEC,UAAU,GAA8B;IAAEF,KAAK,EAAE,KAAK;IAAEC,OAAO,EAAE;EAAE,CAAE;EACrEE,aAAa,GAIf,EAAE;EAEAlD,aAAa,GAAkD,CACpE;IAAEmD,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAI,CAAE,EAC5B;IAAED,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAQ,CAAE,EACnC;IAAED,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAE,CACxC;EAED;EACOvH,qBAAqB,GAAG;IAC7BP,MAAM,EAAE,CACN;MAAE6H,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAI,CAAE,EAC5B;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACnC;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAU,CAAE,CACS;IAClDtH,KAAK,EAAE,EAAmD,CAAE;GAC7D;EAED;EACOG,mBAAmB,GAAG,KAAK;EAC3BZ,cAAc,GAGjB,EAAE;EAEN;EACOgI,SAAS;EACTC,UAAU,GAAQ,EAAE;EACpBC,aAAa,GAAQ,EAAE;EACvBC,iBAAiB,GAAQ,EAAE;EAC3BC,YAAY,GAAQ,EAAE;EAE7B;EACOC,WAAW,GAAa,EAAE;EAC1BC,cAAc,GAAa,EAAE;EAC7B1G,YAAY,GAAa,EAAE;EAC3B2G,gBAAgB,GAAa,EAAE;EAC/BC,UAAU;EACVC,YAAY;EACZ5I,UAAU,GAAG,KAAK;EAEzB;EACO6I,gBAAgB,GAQlB,CACH;IACEC,KAAK,EAAE,QAAQ;IACf5F,KAAK,EAAE,QAAQ;IACf6F,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;GACR,EACD;IACEJ,KAAK,EAAE,cAAc;IACrB5F,KAAK,EAAE,MAAM;IACb6F,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR;EACD;EACA;IACEJ,KAAK,EAAE,OAAO;IACd5F,KAAK,EAAE,OAAO;IACd6F,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEJ,KAAK,EAAE,OAAO;IACd5F,KAAK,EAAE,OAAO;IACd6F,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEJ,KAAK,EAAE,SAAS;IAChB5F,KAAK,EAAE,OAAO;IACd6F,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEJ,KAAK,EAAE,UAAU;IACjB5F,KAAK,EAAE,MAAM;IACb6F,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEJ,KAAK,EAAE,QAAQ;IACf5F,KAAK,EAAE,QAAQ;IACf6F,KAAK,EAAE,GAAG;IACVE,IAAI,EAAE,QAAQ;IACdD,OAAO,EAAE,KAAK;IACdG,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEJ,KAAK,EAAE,iBAAiB;IACxB5F,KAAK,EAAE,cAAc;IACrB6F,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,CACF;EAED;EACOE,iBAAiB,GAA4B,EAAE;EAEtD;EAEA;EACOC,IAAI,GAAqB,CAAC;IAAEP,KAAK,EAAE,iBAAiB;IAAEQ,GAAG,EAAE;EAAM,CAAE,CAAC;EAE3E;EAEA;EACQC,kBAAkB;EAE1B;EACiBC,cAAc,GAAG,2BAA2B;EAE7D;EACO3J,IAAI,GAAe;IACxB4J,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,CAAC;IACb5J,aAAa,EAAE,CAAC;IAChB6J,UAAU,EAAE,CAAC;IACbC,OAAO,EAAE,iBAAiB;IAC1BC,QAAQ,EAAE;GACX;EACMC,IAAI,GAAW,CAAC;EAEvB;EACOC,aAAa,GAA2C,CAC7D;IAAE9B,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAK,CAAE,EACpC;IAAED,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAU,CAAE,EAC9C;IAAED,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAU,CAAE,CAC/C;EAED;EACO8B,aAAa,GAAU,EAAE;EACzBC,aAAa,GAAY,KAAK;EAErC;EACOC,cAAc,GAMjB;IACFC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE;GACb;EAED;EACOC,eAAe,GAAG,KAAK;EACvBC,gBAAgB,GAAW,QAAQ;EAE1C;EACOC,eAAe,GAAQ,EAAE;EAEhCC,YACU/D,YAAyB,EACzBC,GAAsB,EACtBC,MAAc,EACdC,KAAqB,EACrBC,YAAsB;EAAE;EACzBC,UAAsB,EACrBC,wBAAkD,EAClDC,eAAiC,EACjCC,kBAAsC;IARtC,KAAAR,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,YAAY,GAAZA,YAAY;IACb,KAAAC,UAAU,GAAVA,UAAU;IACT,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,kBAAkB,GAAlBA,kBAAkB;EACzB;EAEHwD,QAAQA,CAAA;IACN,IAAI,CAACnD,SAAS,GAAG,IAAI,CAACR,UAAU,CAAC4D,eAAe,EAAE;IAClDC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACtD,SAAS,CAAC;IAEjD;IACA,IAAI,CAACE,kBAAkB,GAAG,IAAI,CAACD,WAAW,CACvCsD,IAAI,CAACzN,YAAY,CAAC,GAAG,CAAC,EAAEC,oBAAoB,EAAE,CAAC,CAC/CyN,SAAS,CAAEC,UAAU,IAAI;MACxBJ,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEG,UAAU,CAAC;MACtD,IAAI,CAACrL,IAAI,CAAC6J,UAAU,GAAG,CAAC;MACxB,IAAI,CAACI,IAAI,GAAG,CAAC;MACb;MACA,IAAI,CAACrD,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB;MACA,IAAI,CAACG,GAAG,CAACsE,aAAa,EAAE;MACxB,IAAI,CAAC5E,SAAS,EAAE;IAClB,CAAC,CAAC;IAEJ;IACA,IAAI,CAACgD,kBAAkB,GAAG,IAAI,CAACzC,MAAM,CAACsE,MAAM,CAACH,SAAS,CAAEI,KAAK,IAAI;MAC/D,IAAIA,KAAK,YAAY5N,eAAe,EAAE;QACpC,IAAI,CAAC6N,aAAa,EAAE;MACtB;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACC,aAAa,EAAE;IAEpB;IACA,IAAI,CAACC,SAAS,EAAE;IAEhB;IACA,IAAI,CAACC,kBAAkB,EAAE;IAEzB;IACA,IAAI,CAACC,UAAU,EAAE;IAEjB;IACA,IAAI,CAACC,gCAAgC,EAAE;IAEvC;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,4BAA4B,EAAE;IACrC,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGQF,gCAAgCA,CAAA;IACtC;IACA,IAAI,CAAClD,cAAc,GAAG,IAAI,CAACI,gBAAgB,CAACiD,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACjD,KAAK,CAAC;IACnE,IAAI,CAACN,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;IAE3C;IACA,IAAI,CAAC1G,YAAY,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC;IAEvD;IACA,IAAI,CAAC2G,gBAAgB,GAAG,IAAI,CAACD,cAAc,CAACb,MAAM,CAC/CmE,GAAG,IAAK,CAAC,IAAI,CAAChK,YAAY,CAACC,QAAQ,CAAC+J,GAAG,CAAC,CAC1C;IAED;IACA,IAAI,CAACpD,UAAU,GAAG,IAAI,CAACtB,IAAI;IAC3B,IAAI,CAACuB,YAAY,GAAG,IAAI,CAACvB,IAAI;EAC/B;EAEA2E,eAAeA,CAAA;IACb;IACA;IACAJ,UAAU,CAAC,MAAK;MACd,IAAI,CAACrF,SAAS,EAAE;IAClB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACA0F,cAAcA,CAAA;IACZ;IACA,IAAI,CAACxF,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACH,SAAS,EAAE;IAChB,IAAI,CAACkF,kBAAkB,EAAE;EAC3B;EAEA;EACAC,UAAUA,CAAA;IACR;IACA,IAAI,CAAC7L,IAAI,CAAC6J,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAACT,IAAI,GAAG,CAAC;MAAEP,KAAK,EAAE,iBAAiB;MAAEQ,GAAG,EAAE;IAAM,CAAE,CAAC;IACvD,IAAI,CAAC1B,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAACvJ,UAAU,GAAG,EAAE;IAEpB;IACA,IAAI,CAACkI,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACH,SAAS,EAAE;IAChB,IAAI,CAACkF,kBAAkB,EAAE;EAC3B;EAEA;EACAlM,WAAWA,CAAA;IACT;IACA,IAAI,CAACkH,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAAC7G,IAAI,CAAC6J,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAAClC,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAACC,UAAU,GAAG;MAAEF,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC/C,IAAI,CAACE,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC7H,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAAC5B,UAAU,GAAG,EAAE;IAEpB;IACA,IAAI,CAACgI,SAAS,EAAE;EAClB;EAEA;EAEA;EAEA2F,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAAC3C,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAAC4C,WAAW,EAAE;IACvC;IACA,IAAI,IAAI,CAACxE,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACwE,WAAW,EAAE;IACvC;IACA,IAAI,CAACzE,WAAW,CAAC0E,QAAQ,EAAE;EAC7B;EACA;EACAC,0BAA0BA,CAAA;IACxB,IAAI,CAAC5F,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACS,eAAe,CAACmF,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,IAAI,CAAC1F,GAAG,CAACsE,aAAa,EAAE;IAExB;IACA;IACA,MAAMqB,cAAc,GAAG,IAAI,CAACnD,IAAI,CAACoD,MAAM,GAAG,CAAC,GACvC,IAAI,CAACpD,IAAI,GACT,CAAC;MAAEP,KAAK,EAAE,iBAAiB;MAAEQ,GAAG,EAAE;IAAM,CAAE,CAAC;IAC/C,MAAMoD,KAAK,GAAG;MACZC,IAAI,EAAE,IAAI,CAAC9M,IAAI,CAAC4J,IAAI;MACpBK,IAAI,EAAE,IAAI,CAACA,IAAI;MACfT,IAAI,EAAEmD,cAAc;MACpB5E,MAAM,EAAE,IAAI,CAACA,MAAM,CAACE,OAAO;MAC3B8E,MAAM,EAAE,IAAI,CAACrO,UAAU;MACvBsO,cAAc,EAAE,IAAI,CAACpF,SAAS,CAACjG;KAChC;IAEDsJ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACxM,UAAU,CAAC;IAC/DuM,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE2B,KAAK,CAAC;IACxC5B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACtE,OAAO,EAAE,YAAY,EAAE,IAAI,CAACC,SAAS,CAAC;IAEpF,IAAI,CAACE,YAAY,CAACkG,oBAAoB,CAACJ,KAAK,CAAC,CAACzB,SAAS,CAAC;MACtDsB,IAAI,EAAGQ,IAYN,IAAI;QACH;QACA,IACEA,IAAI,CAACC,OAAO,IACXD,IAAI,CAACE,YAAY,IAChBF,IAAI,CAACE,YAAY,CAACC,MAAM,IACxBH,IAAI,CAACE,YAAY,CAACC,MAAM,CAACT,MAAM,GAAG,CAAE,EACtC;UACA,MAAMS,MAAM,GAAGH,IAAI,CAACE,YAAY,EAAEC,MAAM,IAAIH,IAAI,CAACG,MAAM,IAAI,EAAE;UAC7DpC,OAAO,CAACqC,KAAK,CAAC,uBAAuB,EAAED,MAAM,CAAC;UAC9C,IAAI,CAACE,mBAAmB,EAAE;QAC5B,CAAC,MAAM;UACL;UACA,MAAMH,YAAY,GAAGF,IAAI,CAACE,YAAY,IAAIF,IAAI;UAC9C,MAAMM,QAAQ,GAAGJ,YAAY,CAACF,IAAI,IAAI,EAAE;UACxC,MAAMO,KAAK,GAAGL,YAAY,CAACK,KAAK,IAAI,CAAC;UAErC,IAAI,CAAC9F,cAAc,GAAG6F,QAAQ,CAACZ,MAAM,KAAK,CAAC;UAC3C,IAAI,CAACnF,iBAAiB,GAAG+F,QAAQ;UACjC,IAAI,CAAC9F,QAAQ,GAAG,IAAI,CAACD,iBAAiB;UACtC,IAAI,CAACzH,IAAI,CAACC,aAAa,GAAGwN,KAAK;UAC/B,IAAI,CAACzN,IAAI,CAAC8J,UAAU,GAAG4D,IAAI,CAACC,IAAI,CAACF,KAAK,GAAG,IAAI,CAACzN,IAAI,CAAC4J,IAAI,CAAC;QAC1D;QACA,IAAI,CAACtC,eAAe,CAACmF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MACjD,CAAC;MACDY,KAAK,EAAGA,KAAc,IAAI;QACxBrC,OAAO,CAACqC,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClE,IAAI,CAACC,mBAAmB,EAAE;QAC1B,IAAI,CAAC3G,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACS,eAAe,CAACmF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MACjD,CAAC;MACDH,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAAC3F,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACS,eAAe,CAACmF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAAC1F,GAAG,CAACsE,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEA;EACM5E,SAASA,CAAA;IAAA,IAAAkH,KAAA;IAAA,OAAAC,iBAAA;MACb;MACAD,KAAI,CAACpB,0BAA0B,EAAE;IAAC;EACpC;EAEQe,mBAAmBA,CAAA;IACzB,IAAI,CAAC5F,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACF,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC1H,IAAI,CAACC,aAAa,GAAG,CAAC;IAC3B,IAAI,CAACD,IAAI,CAAC8J,UAAU,GAAG,CAAC;EAC1B;EAEA;EACA7K,WAAWA,CAAA;IACT;IACA,IAAI,CAACP,UAAU,GAAG,EAAE;IACpB;IACA,IAAI,CAACkI,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACgB,WAAW,CAAC6E,IAAI,CAAC,EAAE,CAAC;EAC3B;EAEA;EACA7L,eAAeA,CAAA;IACb,IAAI,CAACnC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACqJ,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAACC,UAAU,GAAG;MAAEF,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC/C,IAAI,CAACE,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC7H,cAAc,GAAG,EAAE;IACxB,IAAI,CAACN,IAAI,CAAC6J,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb;IACA,IAAI,CAACrD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,SAAS,EAAE;EAClB;EAEA;EACA/F,oBAAoBA,CAAA;IAClB,IAAI,CAACX,IAAI,CAAC6J,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb;IACA,IAAI,CAACrD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,SAAS,EAAE;EAClB;EAEA;EACAoH,qBAAqBA,CAAA;IACnB,IAAI,CAAC5M,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEA;EACAyK,SAASA,CAAA;IACP,MAAMoC,WAAW,GAKb;MACFC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,UAAU;MACrBrE,UAAU,EAAE;KACb;IAED,IAAI,CAAC9C,YAAY,CAACoH,WAAW,CAACJ,WAAW,CAAC,CAAC3C,SAAS,CAAC;MACnDsB,IAAI,EAAGQ,IAIN,IAAI;QACH,IAAIA,IAAI,IAAIA,IAAI,CAACE,YAAY,IAAIF,IAAI,CAACE,YAAY,CAACgB,OAAO,EAAE;UAC1D,IAAI,CAACtN,qBAAqB,CAACC,KAAK,GAAG,CACjC;YAAEqH,IAAI,EAAE,WAAW;YAAEC,KAAK,EAAE;UAAI,CAAE,EAClC,GAAG6E,IAAI,CAACE,YAAY,CAACgB,OAAO,CAACnC,GAAG,CAAExL,IAA0B,KAAM;YAChE2H,IAAI,EAAE3H,IAAI,CAACwD,QAAQ;YACnBoE,KAAK,EAAE5H,IAAI,CAACwD;WACb,CAAC,CAAC,CACJ;QACH;MACF,CAAC;MACDqJ,KAAK,EAAGA,KAAc,IAAI;QACxBrC,OAAO,CAACqC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C;QACA,IAAI,CAACxM,qBAAqB,CAACC,KAAK,GAAG,CAAC;UAAEqH,IAAI,EAAE,WAAW;UAAEC,KAAK,EAAE;QAAI,CAAE,CAAC;MACzE;KACD,CAAC;IACF,IAAI,CAACtB,YAAY,CACdsH,qBAAqB,CAAC,EAAE,CAAC,CACzBjD,SAAS,CAAEkD,WAAgB,IAAI;MAC9B,IAAI,CAACzD,eAAe,GAAGyD,WAAW,CAAClB,YAAY;IACjD,CAAC,CAAC;EACN;EAEA;EACAxB,kBAAkBA,CAAA;IAChB,IAAI,CAAC7E,YAAY,CAACwH,iBAAiB,EAAE,CAACnD,SAAS,CAAC;MAC9CsB,IAAI,EAAGQ,IAAS,IAAI;QAClB,IAAIA,IAAI,IAAIA,IAAI,CAACsB,UAAU,EAAE;UAC3B,IAAI,CAACnE,cAAc,GAAG6C,IAAI,CAACsB,UAAU;QACvC;MACF,CAAC;MACDlB,KAAK,EAAGA,KAAc,IAAI;QACxBrC,OAAO,CAACqC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD;KACD,CAAC;EACJ;EAEA;EACAmB,iBAAiBA,CAACC,SAAc;IAC9B,IAAI,CAACvE,aAAa,GAAGuE,SAAS,CAACC,YAAY,IAAI,EAAE;IACjD,IAAI,CAACvE,aAAa,GAChB,IAAI,CAACD,aAAa,CAACyC,MAAM,KAAK,IAAI,CAACnF,iBAAiB,CAACmF,MAAM;IAC7D,IAAI,CAACjC,eAAe,GAAG,IAAI,CAACR,aAAa,CAACyC,MAAM,GAAG,CAAC;EACtD;EAEA;EACAgC,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACxE,aAAa,EAAE;MACtB,IAAI,CAACD,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC5B,CAAC,MAAM;MACL,IAAI,CAACD,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC1C,iBAAiB,CAAC;MAChD,IAAI,CAAC2C,aAAa,GAAG,IAAI;IAC3B;IACA,IAAI,CAACO,eAAe,GAAG,IAAI,CAACR,aAAa,CAACyC,MAAM,GAAG,CAAC;EACtD;EAEA;EACAiC,UAAUA,CAACC,IAAS;IAClB,IACEC,OAAO,CACL,wCAAwCD,IAAI,CAACE,SAAS,IAAIF,IAAI,CAACG,QAAQ,GAAG,CAC3E,EACD;MACA;MACA,IAAI,CAACrI,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,SAAS,GAAG,IAAI;MAErB,MAAMqI,UAAU,GAAG;QACjBvN,MAAM,EAAEmN,IAAI,CAACnN,MAAM;QACnBqL,cAAc,EAAE,IAAI,CAACpF,SAAS,CAACjG,MAAM,IAAI;OAC1C;MAED,IAAI,CAACoF,YAAY,CAAC8H,UAAU,CAACK,UAAU,CAAC,CAAC9D,SAAS,CAAC;QACjDsB,IAAI,EAAGyC,QAAa,IAAI;UACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,OAAO,EAAE;YAChC;YAC4B,IAAI,CAAC/H,wBAAwB,CAACgI,WAAW,CAACF,QAAQ,CAACC,OAAO,EAAE,EAAE,CAAC;YAE3F,IAAI,CAAC1I,SAAS,EAAE,CAAC,CAAC;YAClB,IAAI,CAACkF,kBAAkB,EAAE,CAAC,CAAC;UAC7B;QACF,CAAC;QACD0B,KAAK,EAAGA,KAAc,IAAI;UACxBrC,OAAO,CAACqC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC1B,IAAI,CAACjG,wBAAwB,CAACiI,SAAS,CAAC,qBAAqB,EAAE,EAAE,CAAC;UAEpF;UACA;UACA,IAAI,CAAC1I,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ;EACF;EAEA;EACA0I,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACpF,aAAa,CAACyC,MAAM,KAAK,CAAC,EAAE;MACnC;MAC4B,IAAI,CAACvF,wBAAwB,CAACgI,WAAW,CAAC,iCAAiC,EAAE,EAAE,CAAC;MAE5G;IACF;IAEA,IACEN,OAAO,CACL,mCAAmC,IAAI,CAAC5E,aAAa,CAACyC,MAAM,qBAAqB,IAAI,CAAChC,gBAAgB,GAAG,CAC1G,EACD;MACA;MACA,IAAI,CAAChE,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,SAAS,GAAG,IAAI;MAErB,MAAM2I,cAAc,GAAG;QACrBC,OAAO,EAAE,IAAI,CAACtF,aAAa,CAAC8B,GAAG,CAAE6C,IAAI,IAAKA,IAAI,CAACnN,MAAM,CAAC;QACtDpB,MAAM,EAAE,IAAI,CAACqK,gBAAgB;QAC7BoC,cAAc,EAAE,IAAI,CAACpF,SAAS,CAACjG,MAAM,IAAI;OAC1C;MAED,IAAI,CAACoF,YAAY,CAACwI,oBAAoB,CAACC,cAAc,CAAC,CAACpE,SAAS,CAAC;QAC/DsB,IAAI,EAAGyC,QAAa,IAAI;UACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,OAAO,EAAE;YAChC;YACA,IAAI,CAAC1I,SAAS,EAAE,CAAC,CAAC;YAClB,IAAI,CAACkF,kBAAkB,EAAE,CAAC,CAAC;YAC3B,IAAI,CAACzB,aAAa,GAAG,EAAE,CAAC,CAAC;YACzB,IAAI,CAACQ,eAAe,GAAG,KAAK;UAC9B;QACF,CAAC;QACD2C,KAAK,EAAGA,KAAc,IAAI;UACxBrC,OAAO,CAACqC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC7C;UAC4B,IAAI,CAACjG,wBAAwB,CAACiI,SAAS,CAAC,wCAAwC,EAAE,EAAE,CAAC;UAEjH;UACA,IAAI,CAAC1I,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ;EACF;EAEA;EACAtF,UAAUA,CAACuN,IAAS;IAClB,IACEC,OAAO,CACL,wCAAwCD,IAAI,CAACY,SAAS,IAAIZ,IAAI,CAACa,QAAQ,GAAG,CAC3E,EACD;MACA;MACA,IAAI,CAAC/I,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,SAAS,GAAG,IAAI;MAErB,MAAM+I,UAAU,GAAG;QACjBjO,MAAM,EAAEmN,IAAI,CAACnN,MAAM;QACnBqL,cAAc,EAAE,IAAI,CAACpF,SAAS,CAACjG,MAAM,IAAI;OAC1C;MAED,IAAI,CAACoF,YAAY,CAACxF,UAAU,CAACqO,UAAU,CAAC,CAACxE,SAAS,CAAC;QACjDsB,IAAI,EAAGyC,QAAa,IAAI;UACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACJ,IAAI,CAAC/H,wBAAwB,CAACgI,WAAW,CAACF,QAAQ,CAACC,OAAO,EAAE,EAAE,CAAC;YAE3F;YACA,IAAI,CAAC1I,SAAS,EAAE,CAAC,CAAC;YAClB,IAAI,CAACkF,kBAAkB,EAAE,CAAC,CAAC;UAC7B;QACF,CAAC;QACD0B,KAAK,EAAGA,KAAc,IAAI;UACxBrC,OAAO,CAACqC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UACjB,IAAI,CAACjG,wBAAwB,CAACiI,SAAS,CAAC,sBAAsB,EAAE,EAAE,CAAC;UAE/F;UACA;UACA,IAAI,CAAC1I,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ;EACF;EAEA/H,eAAeA,CAAC0M,KAAoB;IAClCP,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEM,KAAK,CAACqE,GAAG,EAAE,cAAc,EAAE,IAAI,CAACnR,UAAU,CAAC;IAChF,IAAI8M,KAAK,CAACqE,GAAG,KAAK,OAAO,EAAE;MACzB;MACA5E,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7C,IAAI,CAACrD,WAAW,CAAC6E,IAAI,CAAC,IAAI,CAAChO,UAAU,IAAI,EAAE,CAAC;IAC9C;EACF;EAEA;EACAK,cAAcA,CAAA;IACZ;IACAkM,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACxM,UAAU,CAAC;IACrDuM,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C;IACA,IAAI,CAACrD,WAAW,CAAC6E,IAAI,CAAC,IAAI,CAAChO,UAAU,IAAI,EAAE,CAAC;EAC9C;EAEA;EACAoR,mBAAmBA,CAAA;IASjB,IAAI/H,MAAM,GAQN;MACFgI,QAAQ,EAAE,IAAI;MACdhD,MAAM,EAAE,EAAE;MACViD,YAAY,EAAE;KACf;IAED;IACA,IAAIC,UAAkB;IACtB,IAAI,IAAI,CAACvR,UAAU,KAAK,IAAI,IAAI,IAAI,CAACA,UAAU,KAAKwR,SAAS,EAAE;MAC7DD,UAAU,GAAG,EAAE;IACjB,CAAC,MAAM;MACLA,UAAU,GAAG,IAAI,CAACvR,UAAU;IAC9B;IACAqJ,MAAM,CAACgF,MAAM,GAAGkD,UAAU,CAACE,IAAI,EAAE;IAEjC;IACA,IAAI,IAAI,CAAChI,aAAa,IAAI,IAAI,CAACA,aAAa,CAACyE,MAAM,GAAG,CAAC,EAAE;MACvD7E,MAAM,CAACiI,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC7H,aAAa,CAAC;IAC/C;IAEA;IACA,IAAI,IAAI,CAAC7H,cAAc,CAACC,MAAM,IAAI,IAAI,CAACD,cAAc,CAACC,MAAM,KAAK,IAAI,EAAE;MACrEwH,MAAM,CAACiI,YAAY,CAACI,IAAI,CAAC;QACvBnH,KAAK,EAAE,YAAY;QACnBoH,QAAQ,EAAE,IAAI;QACdhI,KAAK,EAAE,IAAI,CAAC/H,cAAc,CAACC;OAC5B,CAAC;IACJ;IAEA,IAAI,IAAI,CAACD,cAAc,CAACG,IAAI,IAAI,IAAI,CAACH,cAAc,CAACG,IAAI,KAAK,IAAI,EAAE;MACjEsH,MAAM,CAACiI,YAAY,CAACI,IAAI,CAAC;QACvBnH,KAAK,EAAE,UAAU;QACjBoH,QAAQ,EAAE,IAAI;QACdhI,KAAK,EAAE,IAAI,CAAC/H,cAAc,CAACG;OAC5B,CAAC;IACJ;IAEA,OAAOsH,MAAM;EACf;EAEA;EACOuI,UAAUA,CAAC9E,KAAqC;IACrD,IAAI,CAACvB,IAAI,GAAGuB,KAAK,CAACvB,IAAI;IACtB,IAAI,CAACjK,IAAI,CAAC6J,UAAU,GAAG2B,KAAK,CAACvB,IAAI,GAAGuB,KAAK,CAACsB,IAAI;IAC9C,IAAI,CAAC9M,IAAI,CAAC4J,IAAI,GAAG4B,KAAK,CAACsB,IAAI;IAC3B;IACA,IAAI,CAAClG,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,SAAS,EAAE;EAClB;EAEO6J,YAAYA,CAAC/G,IAAsB;IACxC;IACA,MAAMgH,YAAY,GAAGhH,IAAI,CAACoD,MAAM,GAAG,CAAC,IAAIpD,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,KAAKyG,SAAS;IAE5E,IAAIM,YAAY,EAAE;MAChB;MACA,IAAI,CAAChH,IAAI,GAAG,EAAE;MACd,IAAI,CAACxJ,IAAI,CAAC+J,OAAO,GAAG,iBAAiB;MACrC,IAAI,CAAC/J,IAAI,CAACgK,QAAQ,GAAG,MAAM;IAC7B,CAAC,MAAM,IAAIR,IAAI,CAACoD,MAAM,GAAG,CAAC,IAAIpD,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,EAAE;MACpD;MACA,IAAI,CAACD,IAAI,GAAGA,IAAI;MAChB,IAAI,CAACxJ,IAAI,CAAC+J,OAAO,GAAGP,IAAI,CAAC,CAAC,CAAC,CAACP,KAAK,IAAI,iBAAiB;MACtD,IAAI,CAACjJ,IAAI,CAACgK,QAAQ,GAAGR,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG;IAClC,CAAC,MAAM;MACL;MACA,IAAI,CAACD,IAAI,GAAG,EAAE;MACd,IAAI,CAACxJ,IAAI,CAAC+J,OAAO,GAAG,iBAAiB;MACrC,IAAI,CAAC/J,IAAI,CAACgK,QAAQ,GAAG,MAAM;IAC7B;IAEA;IACA,IAAI,CAACpD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,SAAS,EAAE;EAClB;EAEO+J,YAAYA,CAAC1I,MAAiC;IACnD,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACG,UAAU,GAAGH,MAAM;IACxB,IAAI,CAACI,aAAa,GAAG,IAAI,CAACuI,cAAc,CAAC3I,MAAM,CAAC;IAChD,IAAI,CAAC/H,IAAI,CAAC6J,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb;IACA,IAAI,CAACrD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,SAAS,EAAE;EAClB;EAEA;EAEA;EACOxB,cAAcA,CACnB6C,MAAiC,EACjChD,MAAyB;IAEzB,IAAI,CAACgD,MAAM,IAAI,CAACA,MAAM,CAACE,OAAO,IAAI,CAAClD,MAAM,EAAE;MACzC,OAAO,IAAI;IACb;IACA,MAAM4L,SAAS,GAAG5I,MAAM,CAACE,OAAO,CAAC2I,IAAI,CAClCC,CAAM,IAAKA,CAAC,IAAI,OAAO,IAAIA,CAAC,IAAIA,CAAC,CAAC5H,KAAK,KAAKlE,MAAM,CAACkE,KAAK,CAC1D;IACD,OAAO0H,SAAS,IAAI,OAAO,IAAIA,SAAS,GAAGA,SAAS,CAACtI,KAAK,GAAG,IAAI;EACnE;EAEA;EACOrD,oBAAoBA,CACzBqD,KAAoB,EACpBN,MAAiC,EACjChD,MAAyB;IAEzB,IAAI,CAACgD,MAAM,IAAI,CAACA,MAAM,CAACE,OAAO,IAAI,CAAClD,MAAM,EAAE;MACzCkG,OAAO,CAACqC,KAAK,CAAC,2BAA2B,EAAE;QAAEvF,MAAM;QAAEhD;MAAM,CAAE,CAAC;MAC9D;IACF;IAEA,MAAM+L,MAAM,GAAG/I,MAAM,CAACE,OAAO,CAAC8I,SAAS,CACpCF,CAAM,IAAKA,CAAC,IAAI,OAAO,IAAIA,CAAC,IAAIA,CAAC,CAAC5H,KAAK,KAAKlE,MAAM,CAACkE,KAAK,CAC1D;IACD,IAAI6H,MAAM,GAAG,CAAC,CAAC,EAAE;MACf/I,MAAM,CAACE,OAAO,CAAC+I,MAAM,CAACF,MAAM,EAAE,CAAC,CAAC;IAClC;IAEA,IAAIzI,KAAK,KAAK,IAAI,EAAE;MAClBN,MAAM,CAACE,OAAO,CAACmI,IAAI,CAAC;QAClBnH,KAAK,EAAElE,MAAM,CAACkE,KAAK;QACnBoH,QAAQ,EAAE,IAAI;QACdhI,KAAK,EAAEA;OACR,CAAC;IACJ;IAEA,IAAI,CAACoI,YAAY,CAAC1I,MAAM,CAAC;EAC3B;EAEA;EACQ2I,cAAcA,CAAC3I,MAAiC;IAKtD,MAAME,OAAO,GAIR,EAAE;IAEP,IAAI,CAACF,MAAM,IAAI,CAACA,MAAM,CAACE,OAAO,EAAE;MAC9B,OAAOA,OAAO;IAChB;IAEAF,MAAM,CAACE,OAAO,CAACgJ,OAAO,CAAEJ,CAAM,IAAI;MAChC,IAAIA,CAAC,IAAI,OAAO,IAAIA,CAAC,EAAE;QACrB;QACA5I,OAAO,CAACmI,IAAI,CAAC;UACXnH,KAAK,EAAE4H,CAAC,CAAC5H,KAAK;UACdoH,QAAQ,EAAEQ,CAAC,CAACR,QAAQ;UACpBhI,KAAK,EAAEwI,CAAC,CAACxI;SACV,CAAC;MACJ,CAAC,MAAM,IAAIwI,CAAC,IAAI,SAAS,IAAIA,CAAC,EAAE;QAC9B;QACA5I,OAAO,CAACmI,IAAI,CAAC,GAAG,IAAI,CAACM,cAAc,CAACG,CAAC,CAAC,CAAC;MACzC;IACF,CAAC,CAAC;IAEF,OAAO5I,OAAO;EAChB;EAEA;EACQyD,aAAaA,CAAA;IACnB,IAAI;MACF,MAAMwF,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACzH,cAAc,CAAC;MAE5D,IAAI,CAACuH,UAAU,EAAE;QACf;MACF;MAEA,MAAMrE,KAAK,GAwBPwE,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;MAE1B;MACA,IAAIrE,KAAK,IAAIA,KAAK,CAACrD,IAAI,EAAE;QACvB,IAAI,CAACA,IAAI,GAAGqD,KAAK,CAACrD,IAAI;QACtB,IAAI,IAAI,CAACA,IAAI,IAAI,IAAI,CAACA,IAAI,CAACoD,MAAM,GAAG,CAAC,IAAI,IAAI,CAACpD,IAAI,CAAC,CAAC,CAAC,EAAE;UACrD,IAAI,CAACxJ,IAAI,CAAC+J,OAAO,GAAG,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC,CAACP,KAAK,IAAI,iBAAiB;UAC3D,IAAI,CAACjJ,IAAI,CAACgK,QAAQ,GAAG,IAAI,CAACR,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,IAAI,MAAM;QACjD;MACF;MAEA;MACA,IAAIoD,KAAK,IAAIA,KAAK,CAAC9E,MAAM,EAAE;QACzB,IAAI,CAACA,MAAM,GAAG8E,KAAK,CAAC9E,MAAM;QAC1B,IAAI,CAACG,UAAU,GAAG2E,KAAK,CAAC9E,MAAM;QAC9B,IAAI,CAACI,aAAa,GAAG0E,KAAK,CAAC1E,aAAa,IAAI,EAAE;MAChD;MAEA;MACA,IAAI0E,KAAK,IAAIA,KAAK,CAAC7M,IAAI,EAAE;QACvB,IAAI,CAACA,IAAI,GAAG6M,KAAK,CAAC7M,IAAI;MACxB;MAEA,IAAI6M,KAAK,IAAIA,KAAK,CAAC5C,IAAI,KAAKiG,SAAS,EAAE;QACrC,IAAI,CAACjG,IAAI,GAAG4C,KAAK,CAAC5C,IAAI;MACxB;MAEA;MACA,IAAI4C,KAAK,IAAIA,KAAK,CAACtD,iBAAiB,EAAE;QACpC,IAAI,CAACA,iBAAiB,GAAGsD,KAAK,CAACtD,iBAAiB;MAClD;MAEA;MACA,IAAIsD,KAAK,IAAIA,KAAK,CAACnO,UAAU,EAAE;QAC7B,IAAI,CAACA,UAAU,GAAGmO,KAAK,CAACnO,UAAU;MACpC;MAEA;MACA,IAAImO,KAAK,IAAIA,KAAK,CAACvM,cAAc,EAAE;QACjC,IAAI,CAACA,cAAc,GAAGuM,KAAK,CAACvM,cAAc;MAC5C;MAEA,IAAIuM,KAAK,IAAIA,KAAK,CAAC3L,mBAAmB,KAAKgP,SAAS,EAAE;QACpD,IAAI,CAAChP,mBAAmB,GAAG2L,KAAK,CAAC3L,mBAAmB;MACtD;IACF,CAAC,CAAC,OAAOoM,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD;IACF;EACF;EAEA;EAEA;EACQ7B,aAAaA,CAAA;IACnB,MAAMoB,KAAK,GAwBP;MACFrD,IAAI,EAAE,IAAI,CAACA,IAAI;MACfzB,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB/H,IAAI,EAAE,IAAI,CAACA,IAAI;MACfiK,IAAI,EAAE,IAAI,CAACA,IAAI;MACfV,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzC7K,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3ByJ,aAAa,EAAE,IAAI,CAACA,aAAa;MACjC7H,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCY,mBAAmB,EAAE,IAAI,CAACA;KAC3B;IAEDiQ,YAAY,CAACI,OAAO,CAAC,IAAI,CAAC5H,cAAc,EAAE0H,IAAI,CAACG,SAAS,CAAC3E,KAAK,CAAC,CAAC;EAClE;EAEA;EACAzN,GAAGA,CAAA;IACD,IAAI,CAACsC,IAAI,CAAC,CAAC,CAAC;EACd;EAEA;EACAA,IAAIA,CAAC+P,EAAU;IACbxG,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,sBAAsB,EAAEuG,EAAE,CAAC;IACpD;IACA,MAAMC,eAAe,GAKjB;MACF9H,IAAI,EAAE,IAAI;MAAE;MACZ+H,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMC,QAAQ,GAAG,IAAI,CAAC3K,YAAY,CAAC4K,IAAI,CAAClU,gBAAgB,EAAE6T,eAAe,CAAC;IAC1E;IACAI,QAAQ,CAACE,iBAAiB,CAACP,EAAE,GAAGA,EAAE;IAClCK,QAAQ,CAACE,iBAAiB,CAACC,kBAAkB,GAAG,IAAI,CAACpH,eAAe;IACpE;IACAiH,QAAQ,CAACE,iBAAiB,CAACE,SAAS,CAAC9G,SAAS,CAAE+G,aAAsB,IAAI;MACxE,IAAIA,aAAa,KAAK,IAAI,EAAE;QAC1B;QACA,IAAI,CAACzL,SAAS,EAAE;MAClB;IACF,CAAC,CAAC;EACJ;EAEO0L,cAAcA,CAACC,IAAc;IAClCpH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEmH,IAAI,CAAC;IACrC;EACF;EACO/S,YAAYA,CAAA;IACjB;IACA,MAAMgT,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAC1C,iBAAiB,CACH;IAChB,IAAIF,aAAa,EAAE;MACjBA,aAAa,CAACG,SAAS,CAACC,MAAM,CAAC,iBAAiB,CAAC;MACjD,IAAI,CAACvS,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;MAClC;MACA,IAAI,IAAI,CAACqH,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACmL,OAAO,EAAE;MACrB;IACF;EACF;EAEA;EACOC,aAAaA,CAACpH,KAAkC;IACrD,QAAQA,KAAK,CAAC6G,IAAI,CAAChK,KAAK;MACtB,KAAK,KAAK;QACR,IAAI,CAACwK,cAAc,EAAE;QACrB;MACF,KAAK,UAAU;QACb,IAAI,CAACC,mBAAmB,EAAE;QAC1B;MACF,KAAK,UAAU;QACb,IAAI,CAACC,mBAAmB,EAAE;QAC1B;MACF;QACE9H,OAAO,CAAC+H,IAAI,CAAC,wBAAwB,EAAExH,KAAK,CAAC6G,IAAI,CAAChK,KAAK,CAAC;IAC5D;EACF;EAEQwK,cAAcA,CAAA;IACpB,MAAMI,YAAY,GAAG;MACnBhL,OAAO,EAAE,EAAE;MACXiL,MAAM,EAAE;KACT;IAED,IAAI,CAACnM,YAAY,CAACoM,WAAW,CAACF,YAAY,CAAC,CAAC7H,SAAS,CAAC;MACpDsB,IAAI,EAAGyC,QAAa,IAAI;QACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACiE,UAAU,EAAE;UACnC,IAAI,CAACC,aAAa,CAAClE,QAAQ,CAACiE,UAAU,EAAE,WAAW,CAAC;QACtD;MACF,CAAC;MACD9F,KAAK,EAAGA,KAAc,IAAI;QACxBrC,OAAO,CAACqC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAClB,IAAI,CAACjG,wBAAwB,CAACiI,SAAS,CAAC,uBAAuB,EAAE,EAAE,CAAC;QAEhG;MACF;KACD,CAAC;EACJ;EAEQwD,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAAC3I,aAAa,CAACyC,MAAM,KAAK,CAAC,EAAE;MACP,IAAI,CAACvF,wBAAwB,CAACiI,SAAS,CAAC,+BAA+B,EAAE,EAAE,CAAC;MAExG;MACA;IACF;IAEA,MAAM2D,YAAY,GAAG;MACnBhL,OAAO,EAAE;QACPwH,OAAO,EAAE,IAAI,CAACtF,aAAa,CAAC8B,GAAG,CAAE6C,IAAI,IAAKA,IAAI,CAACwE,MAAM;OACtD;MACDJ,MAAM,EAAE;KACT;IAED,IAAI,CAACnM,YAAY,CAACoM,WAAW,CAACF,YAAY,CAAC,CAAC7H,SAAS,CAAC;MACpDsB,IAAI,EAAGyC,QAAa,IAAI;QACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACiE,UAAU,EAAE;UACnC,IAAI,CAACC,aAAa,CAAClE,QAAQ,CAACiE,UAAU,EAAE,gBAAgB,CAAC;QAC3D;MACF,CAAC;MACD9F,KAAK,EAAGA,KAAc,IAAI;QACxBrC,OAAO,CAACqC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QAC3B,IAAI,CAACjG,wBAAwB,CAACiI,SAAS,CAAC,gCAAgC,EAAE,EAAE,CAAC;QAEzG;MACF;KACD,CAAC;EACJ;EAEQyD,mBAAmBA,CAAA;IACzB,MAAME,YAAY,GAAG;MACnBhL,OAAO,EAAE;QACP1H,MAAM,EAAE,IAAI,CAACD,cAAc,CAACC,MAAM;QAClCE,IAAI,EAAE,IAAI,CAACH,cAAc,CAACG,IAAI;QAC9B4K,UAAU,EAAE,IAAI,CAAC3M;OAClB;MACDwU,MAAM,EAAE;KACT;IAED,IAAI,CAACnM,YAAY,CAACoM,WAAW,CAACF,YAAY,CAAC,CAAC7H,SAAS,CAAC;MACpDsB,IAAI,EAAGyC,QAAa,IAAI;QACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACiE,UAAU,EAAE;UACnC,IAAI,CAACC,aAAa,CAAClE,QAAQ,CAACiE,UAAU,EAAE,gBAAgB,CAAC;QAC3D;MACF,CAAC;MACD9F,KAAK,EAAGA,KAAc,IAAI;QACxBrC,OAAO,CAACqC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QAC3B,IAAI,CAACjG,wBAAwB,CAACiI,SAAS,CAAC,gCAAgC,EAAE,EAAE,CAAC;QAEzG;MACF;KACD,CAAC;EACJ;EAEQ+D,aAAaA,CAACnG,IAAW,EAAEqG,QAAgB;IACjD;IACA;IACA,MAAMC,UAAU,GAAG,IAAI,CAACC,YAAY,CAACvG,IAAI,CAAC;IAC1C,MAAMwG,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,UAAU,CAAC,EAAE;MAAEpK,IAAI,EAAE;IAAyB,CAAE,CAAC;IACxE,MAAMwK,IAAI,GAAGrB,QAAQ,CAACsB,aAAa,CAAC,GAAG,CAAC;IACxC,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;IACrCE,IAAI,CAACK,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;IAC9BF,IAAI,CAACK,YAAY,CACf,UAAU,EACV,GAAGV,QAAQ,IAAI,IAAIW,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAC5D;IACDR,IAAI,CAACS,KAAK,CAACC,UAAU,GAAG,QAAQ;IAChC/B,QAAQ,CAACgC,IAAI,CAACC,WAAW,CAACZ,IAAI,CAAC;IAC/BA,IAAI,CAACa,KAAK,EAAE;IACZlC,QAAQ,CAACgC,IAAI,CAACG,WAAW,CAACd,IAAI,CAAC;EACjC;EAEQH,YAAYA,CAACvG,IAAW;IAC9B,IAAIA,IAAI,CAACN,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEhC,MAAM+H,OAAO,GAAGC,MAAM,CAACC,IAAI,CAAC3H,IAAI,CAAC,CAAC,CAAC,CAAC;IACpC,MAAM4H,OAAO,GAAG,CAACH,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC,CAAC;IAEnC,KAAK,MAAMC,GAAG,IAAI9H,IAAI,EAAE;MACtB,MAAM+H,MAAM,GAAGN,OAAO,CAAC1I,GAAG,CAAEiJ,MAAM,IAAI;QACpC,MAAM7M,KAAK,GAAG2M,GAAG,CAACE,MAAM,CAAC;QACzB,OAAO,OAAO7M,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAAClG,QAAQ,CAAC,GAAG,CAAC,GACnD,IAAIkG,KAAK,GAAG,GACZA,KAAK;MACX,CAAC,CAAC;MACFyM,OAAO,CAAC1E,IAAI,CAAC6E,MAAM,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC;IAChC;IAEA,OAAOD,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC;EAC3B;EAEA;EAEA;;;;;;EAMAI,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAACvN,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACjG,MAAM,EAAE;MAC7CsJ,OAAO,CAACqC,KAAK,CAAC,0BAA0B,EAAE,IAAI,CAAC1F,SAAS,CAAC;MACzD,IAAI,CAACP,wBAAwB,CAACiI,SAAS,CACrC,8CAA8C,EAC9C,EAAE,CACH;MACD;IACF;IAEA,MAAM8F,gBAAgB,GAAU,EAAE;IAClC,MAAMC,aAAa,GAAU,EAAE;IAE/B,IAAI,IAAI,CAAC7N,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC8N,OAAO,EAAE;MAClC,IAAI,CAAC9N,IAAI,CAAC8N,OAAO,CAACrE,OAAO,CAAElM,MAAW,IAAI;QACxC,IAAI,CAACA,MAAM,CAACwQ,MAAM,EAAE;UAClB,MAAMC,UAAU,GAAG;YACjBnS,KAAK,EAAE0B,MAAM,CAAC1B,KAAK;YACnB4F,KAAK,EAAElE,MAAM,CAACkE,KAAK;YACnBsM,MAAM,EAAExQ,MAAM,CAACwQ;WAChB;UACDH,gBAAgB,CAAChF,IAAI,CAACoF,UAAU,CAAC;QACnC,CAAC,MAAM;UACL,MAAMA,UAAU,GAAG;YACjBnS,KAAK,EAAE0B,MAAM,CAAC1B,KAAK;YACnB4F,KAAK,EAAElE,MAAM,CAACkE,KAAK;YACnBsM,MAAM,EAAExQ,MAAM,CAACwQ;WAChB;UACDF,aAAa,CAACjF,IAAI,CAACoF,UAAU,CAAC;QAChC;MACF,CAAC,CAAC;IACJ;IAEA,MAAMC,qBAAqB,GAAG,IAAI,CAAC9M,WAAW,CAC3CZ,MAAM,CAAEmE,GAAG,IAAK,CAAC,IAAI,CAAChK,YAAY,CAACC,QAAQ,CAAC+J,GAAG,CAAC,CAAC,CACjDD,GAAG,CAAC,CAAChD,KAAK,EAAEyM,KAAK,MAAM;MACtBzM,KAAK;MACL0M,UAAU,EAAED;KACb,CAAC,CAAC;IAEL;IACA,MAAMlI,QAAQ,GAAG;MACfoI,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,IAAI,CAACjO,SAAS,CAACjG,MAAM;MAC7B4G,UAAU,EAAE8M,aAAa;MACzB7M,aAAa,EAAEiN,qBAAqB;MACpCK,QAAQ,EAAE,IAAI,CAAClO,SAAS,CAACjG;KAC1B;IAED;IACA,IAAI,CAAC2F,eAAe,CAACmF,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,IAAI,CAACnF,kBAAkB,CAACwO,gBAAgB,CAACvI,QAAQ,CAAC,CAACpC,SAAS,CAAC;MAC3DsB,IAAI,EAAGsJ,GAAG,IAAI;QACZ,IAAI,CAAC1O,eAAe,CAACmF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAACsJ,GAAG,CAAC7I,OAAO,EAAE;UAChB;UACA,IAAI,CAAC5E,UAAU,GAAG8M,aAAa;UAC/B,IAAI,CAAC7M,aAAa,GAAGiN,qBAAqB;UAC1C,IAAI,CAAC/M,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC0D,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAACjD,KAAK,CAAC;UAEhE;UACA,IAAI,CAAC1B,kBAAkB,CAAC0O,kBAAkB,CAACzI,QAAQ,CAAC;UAEpD,IAAI,CAACnG,wBAAwB,CAACgI,WAAW,CACvC2G,GAAG,CAAC5G,OAAO,IAAI,qCAAqC,EACpD,EAAE,CACH;QACH,CAAC,MAAM;UACL,IAAI,CAAC/H,wBAAwB,CAACiI,SAAS,CACrC0G,GAAG,CAAC5G,OAAO,IAAI,iCAAiC,EAChD,EAAE,CACH;QACH;QACA,IAAI,CAACpI,GAAG,CAACkP,YAAY,EAAE;MACzB,CAAC;MACD5I,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChG,eAAe,CAACmF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/CzB,OAAO,CAACqC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QAErD;QACA,IAAI,CAAC/F,kBAAkB,CAAC0O,kBAAkB,CAACzI,QAAQ,CAAC;QAEpD;QACA,IAAI,CAACjF,UAAU,GAAG8M,aAAa;QAC/B,IAAI,CAAC7M,aAAa,GAAGiN,qBAAqB;QAC1C,IAAI,CAAC/M,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC0D,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAACjD,KAAK,CAAC;QAEhE,IAAI,CAAC5B,wBAAwB,CAACiI,SAAS,CACrC,mDAAmD,EACnD,EAAE,CACH;QACD,IAAI,CAACtI,GAAG,CAACkP,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;;;;EAIA1W,UAAUA,CAAA;IACR;IACA,IAAI,CAAC,IAAI,CAACoI,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACjG,MAAM,EAAE;MAC7CsJ,OAAO,CAACqC,KAAK,CAAC,0BAA0B,EAAE,IAAI,CAAC1F,SAAS,CAAC;MACzD,IAAI,CAACP,wBAAwB,CAACiI,SAAS,CACrC,4DAA4D,EAC5D,EAAE,CACH;MACD;IACF;IAEA;IACA,MAAM6G,KAAK,GAAG,IAAI,CAAC/O,UAAU,CAACgP,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC;IACtE,IAAI,CAACD,KAAK,EAAE;MACVlL,OAAO,CAACqC,KAAK,CAAC,gCAAgC,CAAC;MAC/C,IAAI,CAACjG,wBAAwB,CAACiI,SAAS,CACrC,qDAAqD,EACrD,EAAE,CACH;MACD;IACF;IAEA;IACA,IAAI,CAAC5Q,UAAU,GAAG,EAAE;IACpB,IAAI,CAACyJ,aAAa,GAAG,EAAE;IACvB,IAAI,CAACJ,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAACgC,IAAI,GAAG,CAAC;IACb,IAAI,CAACjK,IAAI,CAAC6J,UAAU,GAAG,CAAC;IACxB,IAAI,CAAClB,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;IAE3C;IACA,IAAI,CAACY,IAAI,GAAG,CAAC;MAAEP,KAAK,EAAE,iBAAiB;MAAEQ,GAAG,EAAE;IAAM,CAAE,CAAC;IACvD,IAAI,CAACzJ,IAAI,CAAC+J,OAAO,GAAG,iBAAiB;IACrC,IAAI,CAAC/J,IAAI,CAACgK,QAAQ,GAAG,MAAM;IAE3B;IACA,IAAI,CAAC1J,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAACY,mBAAmB,GAAG,KAAK;IAEhC;IACA,IAAI,IAAI,CAACsG,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC8N,OAAO,EAAE;MAClC,IAAI,CAAC9N,IAAI,CAAC8N,OAAO,CAACrE,OAAO,CAAElM,MAAW,IAAI;QACxC,MAAM2Q,KAAK,GAAG,IAAI,CAAC/M,WAAW,CAAC0N,OAAO,CAACtR,MAAM,CAACkE,KAAK,CAAC;QACpD,IAAIyM,KAAK,KAAK,CAAC,CAAC,EAAE;UAChB3Q,MAAM,CAAC4Q,UAAU,GAAGD,KAAK;QAC3B;QACA;QACA,IAAI3Q,MAAM,CAACkE,KAAK,IAAIlE,MAAM,CAACkE,KAAK,KAAK,QAAQ,EAAE;UAC7ClE,MAAM,CAACwQ,MAAM,GAAG,KAAK;QACvB;MACF,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,CAAChN,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACE,YAAY,GAAG,EAAE;IAEtB;IACA,IAAI,IAAI,CAAClB,IAAI,EAAE;MACb;MACA,IAAI,CAACA,IAAI,CAACO,MAAM,GAAG;QAAEC,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAE,CAAE;MAEhD;MACA,IAAI,CAACT,IAAI,CAACgC,IAAI,GAAG,CAAC;QAAEP,KAAK,EAAE,iBAAiB;QAAEQ,GAAG,EAAE;MAAM,CAAE,CAAC;MAE5D;MACA,IAAI,CAACjC,IAAI,CAACyC,IAAI,GAAG,CAAC;MAClB,IAAI,CAACzC,IAAI,CAACwG,QAAQ,GAAG,IAAI,CAAChO,IAAI,CAAC4J,IAAI;IACrC;IAEA;IACA,MAAM4D,QAAQ,GAAG;MACfoI,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,IAAI,CAACjO,SAAS,CAACjG,MAAM;MAC7B4G,UAAU,EAAE,EAAE;MACdC,aAAa,EAAE,EAAE;MACjBsN,QAAQ,EAAE,IAAI,CAAClO,SAAS,CAACjG;KAC1B;IAED;IACA,IAAI,CAAC4F,kBAAkB,CAAC+O,qBAAqB,CAAC,OAAO,CAAC;IAEtD;IACA,IAAI,CAAC1P,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACS,eAAe,CAACmF,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAAC1F,GAAG,CAACsE,aAAa,EAAE;IAExB;IACA,IAAI,IAAI,CAAC9D,IAAI,EAAE;MACbuE,UAAU,CAAC,MAAK;QACd,IAAI,CAACvE,IAAI,CAACmL,OAAO,EAAE;QACnB,IAAI,CAACnL,IAAI,CAAC+O,KAAK,EAAE;MACnB,CAAC,EAAE,GAAG,CAAC;IACT;IAEA,IAAI,CAAC7P,SAAS,EAAE;EAClB;EAEA;;;;;EAKA8P,oBAAoBA,CAAChO,aAAkB;IACrC,IAAI;MACF,MAAMiO,UAAU,GAAGjO,aAAa;MAChC,IAAIiO,UAAU,EAAE;QACd,MAAMC,WAAW,GAAGD,UAAU;QAC9B,IAAIE,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,IAAIA,WAAW,CAAC9J,MAAM,GAAG,CAAC,EAAE;UACxD;UACA,MAAMiK,qBAAqB,GAAGH,WAAW,CACtClN,IAAI,CAAC,CAACsN,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACnB,UAAU,GAAGoB,CAAC,CAACpB,UAAU,CAAC,CAC3C1J,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACjD,KAAK,CAAC,CACvBlB,MAAM,CAAEkB,KAAK,IAAK,CAAC,IAAI,CAAC/G,YAAY,CAACC,QAAQ,CAAC8G,KAAK,CAAC,CAAC;UAExD;UACA,MAAM+N,cAAc,GAAG,IAAI,CAACnO,gBAAgB,CAACd,MAAM,CAChDmE,GAAG,IAAK,CAAC2K,qBAAqB,CAAC1U,QAAQ,CAAC+J,GAAG,CAAC,CAC9C;UAED;UACA,IAAI,CAACvD,WAAW,GAAG,CACjB,GAAG,IAAI,CAACzG,YAAY,EACpB,GAAG2U,qBAAqB,EACxB,GAAGG,cAAc,CAClB;QACH,CAAC,MAAM;UACL,IAAI,CAACrO,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;QAC7C;MACF,CAAC,MAAM;QACL,IAAI,CAACD,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;MAC7C;IACF,CAAC,CAAC,OAAO0E,KAAK,EAAE;MACd,IAAI,CAAC3E,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;IAC7C;EACF;EAEA;;;;EAIAvG,cAAcA,CAAC4U,UAAe;IAC5B,OAAO,IAAI,CAACvO,YAAY,CAAC2N,OAAO,CAACY,UAAU,CAAC,GAAG,CAAC,CAAC;EACnD;EAEA;;;;;EAKAC,eAAeA,CAAC1L,KAAU;IACxB,MAAM;MAAE8J,OAAO;MAAE6B,QAAQ;MAAEC;IAAQ,CAAE,GAAG5L,KAAK;IAE7C;IACA,IACE,IAAI,CAACtJ,YAAY,CAACC,QAAQ,CAACmT,OAAO,CAAC8B,QAAQ,CAAC,CAACnO,KAAK,CAAC,IACnD,IAAI,CAAC/G,YAAY,CAACC,QAAQ,CAACmT,OAAO,CAAC6B,QAAQ,CAAC,CAAClO,KAAK,CAAC,EACnD;MACA;IACF;IAEA;IACA,MAAMoO,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC1O,WAAW,CAAC;IAC9C,MAAM,CAAC2O,WAAW,CAAC,GAAGD,gBAAgB,CAACrG,MAAM,CAACoG,QAAQ,EAAE,CAAC,CAAC;IAC1DC,gBAAgB,CAACrG,MAAM,CAACmG,QAAQ,EAAE,CAAC,EAAEG,WAAW,CAAC;IAEjD,IAAI,CAAC3O,WAAW,GAAG0O,gBAAgB;IACnC,IAAI,CAACrQ,GAAG,CAACkP,YAAY,EAAE;EACzB;EAEA;;;;EAIAqB,sBAAsBA,CAAC/L,KAAU;IAC/B,IAAI,IAAI,CAACrL,UAAU,KAAK,KAAK,EAAE;MAC7B,IAAI,IAAI,CAACqH,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC8N,OAAO,EAAE;QAClC,IAAI,CAAC9N,IAAI,CAAC8N,OAAO,CAACrE,OAAO,CAAElM,MAAW,IAAI;UACxC,MAAMyQ,UAAU,GAAG;YACjBnS,KAAK,EAAE0B,MAAM,CAAC1B,KAAK;YACnB4F,KAAK,EAAElE,MAAM,CAACkE,KAAK;YACnBsM,MAAM,EAAExQ,MAAM,CAACwQ;WAChB;UACD,IAAIxQ,MAAM,CAACwQ,MAAM,EAAE;YACjB,MAAMzE,MAAM,GAAG,IAAI,CAACvI,UAAU,CAACiP,IAAI,CAChCnF,IAAS,IACRA,IAAI,CAACpJ,KAAK,KAAKuM,UAAU,CAACvM,KAAK,IAAIoJ,IAAI,CAACkD,MAAM,KAAK,IAAI,CAC1D;YACD,IAAI,CAACzE,MAAM,EAAE;cACX,IAAI,CAACvI,UAAU,CAAC6H,IAAI,CAACoF,UAAU,CAAC;YAClC;UACF,CAAC,MAAM;YACL,IAAIiC,WAAW,GAAG,IAAI,CAAClP,UAAU,CAACwI,SAAS,CACxCsB,IAAS,IACRA,IAAI,CAACpJ,KAAK,KAAKuM,UAAU,CAACvM,KAAK,IAAIoJ,IAAI,CAACkD,MAAM,KAAK,IAAI,CAC1D;YACD,IAAIkC,WAAW,KAAK,CAAC,CAAC,EAAE;cACtB,IAAI,CAAClP,UAAU,CAACyI,MAAM,CAACyG,WAAW,EAAE,CAAC,CAAC;YACxC;UACF;QACF,CAAC,CAAC;QACF,IAAI,CAAC/O,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC0D,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAACjD,KAAK,CAAC;QAChE,IAAI,CAACjC,GAAG,CAACkP,YAAY,EAAE;MACzB;IACF;EACF;EAEA;;;;EAIQlK,4BAA4BA,CAAA;IAClC,IAAI;MACF;MACA,IAAI,IAAI,CAACpE,SAAS,IAAI,IAAI,CAACA,SAAS,CAACjG,MAAM,EAAE;QAC3C,IAAI,CAAC4F,kBAAkB,CACpBmQ,aAAa,CAAC;UACb9B,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE,IAAI,CAACjO,SAAS,CAACjG;SACxB,CAAC,CACDyJ,SAAS,CAAC;UACTsB,IAAI,EAAGsJ,GAAG,IAAI;YACZ,IAAI,CAACA,GAAG,CAAC7I,OAAO,IAAI6I,GAAG,CAAC2B,IAAI,EAAE;cAC5B,IAAI,CAACrP,SAAS,GAAG0N,GAAG,CAAC2B,IAAI;cACzB,IAAI,CAACpP,UAAU,GAAGyN,GAAG,CAAC2B,IAAI,CAACC,QAAQ,GAC/BvG,IAAI,CAACC,KAAK,CAAC0E,GAAG,CAAC2B,IAAI,CAACC,QAAQ,CAAC,GAC7B,EAAE;cACN,IAAI,CAACnP,iBAAiB,GAAGuN,GAAG,CAAC2B,IAAI,CAACnP,aAAa,GAC3C6I,IAAI,CAACC,KAAK,CAAC0E,GAAG,CAAC2B,IAAI,CAACnP,aAAa,CAAC,GAClC,EAAE;cACN,IAAI,CAACE,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC0D,GAAG,CACpCC,GAAQ,IAAKA,GAAG,CAACjD,KAAK,CACxB;cAED;cACA,IAAI,IAAI,CAACzB,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC8N,OAAO,EAAE;gBAClC,IAAI,CAAC9N,IAAI,CAAC8N,OAAO,CAACrE,OAAO,CAAElM,MAAW,IAAI;kBACxC,IACE,IAAI,CAACwD,UAAU,CAACiP,IAAI,CACjBnF,IAAS,IACRA,IAAI,CAAChP,KAAK,KAAK0B,MAAM,CAAC1B,KAAK,IAAIgP,IAAI,CAACkD,MAAM,CAC7C,EACD;oBACAxQ,MAAM,CAAC8S,gBAAgB,GAAG,IAAI;oBAC9B9S,MAAM,CAACwQ,MAAM,GAAG,IAAI;kBACtB,CAAC,MAAM;oBACLxQ,MAAM,CAACwQ,MAAM,GAAG,KAAK;kBACvB;gBACF,CAAC,CAAC;cACJ;cAEA;cACA,IAAI,CAACiB,oBAAoB,CAAC,IAAI,CAAC/N,iBAAiB,CAAC;cAEjD;cACA,IAAI,CAAClB,kBAAkB,CAAC0O,kBAAkB,CAAC;gBACzCL,QAAQ,EAAE,OAAO;gBACjBC,MAAM,EAAE,IAAI,CAACjO,SAAS,CAACjG,MAAM;gBAC7B4G,UAAU,EAAE,IAAI,CAACA,UAAU;gBAC3BC,aAAa,EAAE,IAAI,CAACC;eACrB,CAAC;YACJ;UACF,CAAC;UACD6E,KAAK,EAAGA,KAAK,IAAI;YACfrC,OAAO,CAACqC,KAAK,CACX,2DAA2D,EAC3DA,KAAK,CACN;YACD,IAAI,CAACwK,4BAA4B,EAAE;UACrC;SACD,CAAC;MACN,CAAC,MAAM;QACL;QACA,IAAI,CAACA,4BAA4B,EAAE;MACrC;IACF,CAAC,CAAC,OAAOxK,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,IAAI,CAACwK,4BAA4B,EAAE;IACrC;EACF;EAEA;;;EAGQA,4BAA4BA,CAAA;IAClC,IAAI;MACF,MAAMC,WAAW,GAAG,IAAI,CAACxQ,kBAAkB,CAACyQ,mBAAmB,CAC7D,OAAO,EACP,IAAI,CAACpQ,SAAS,EAAE0L,MAAM,IAAI,CAAC,CAC5B;MACD,IAAIyE,WAAW,EAAE;QACf,IAAI,CAACzP,SAAS,GAAGyP,WAAW;QAC5B,IAAI,CAACxP,UAAU,GAAGwP,WAAW,CAACxP,UAAU,IAAI,EAAE;QAC9C,IAAI,CAACE,iBAAiB,GAAGsP,WAAW,CAACvP,aAAa,IAAI,EAAE;QACxD,IAAI,CAACE,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC0D,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAACjD,KAAK,CAAC;QAEhE;QACA,IAAI,IAAI,CAACzB,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC8N,OAAO,EAAE;UAClC,IAAI,CAAC9N,IAAI,CAAC8N,OAAO,CAACrE,OAAO,CAAElM,MAAW,IAAI;YACxC,IACE,IAAI,CAACwD,UAAU,CAACiP,IAAI,CACjBnF,IAAS,IAAKA,IAAI,CAAChP,KAAK,KAAK0B,MAAM,CAAC1B,KAAK,IAAIgP,IAAI,CAACkD,MAAM,CAC1D,EACD;cACAxQ,MAAM,CAAC8S,gBAAgB,GAAG,IAAI;cAC9B9S,MAAM,CAACwQ,MAAM,GAAG,IAAI;YACtB,CAAC,MAAM;cACLxQ,MAAM,CAACwQ,MAAM,GAAG,KAAK;YACvB;UACF,CAAC,CAAC;QACJ;QAEA;QACA,IAAI,CAACiB,oBAAoB,CAAC,IAAI,CAAC/N,iBAAiB,CAAC;MACnD;IACF,CAAC,CAAC,OAAO6E,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;IACnE;EACF;;qCA/mDWxG,iBAAiB,EAAAhJ,EAAA,CAAAma,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAra,EAAA,CAAAma,iBAAA,CAAAna,EAAA,CAAAsa,iBAAA,GAAAta,EAAA,CAAAma,iBAAA,CAAAI,EAAA,CAAAC,MAAA,GAAAxa,EAAA,CAAAma,iBAAA,CAAAI,EAAA,CAAAE,cAAA,GAAAza,EAAA,CAAAma,iBAAA,CAAAO,EAAA,CAAAC,QAAA,GAAA3a,EAAA,CAAAma,iBAAA,CAAAS,EAAA,CAAAtR,UAAA,GAAAtJ,EAAA,CAAAma,iBAAA,CAAAU,EAAA,CAAAC,wBAAA,GAAA9a,EAAA,CAAAma,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAAhb,EAAA,CAAAma,iBAAA,CAAAc,EAAA,CAAAC,kBAAA;EAAA;;UAAjBlS,iBAAiB;IAAAmS,SAAA;IAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;QCnE9Btb,EAAA,CAAAkD,UAAA,IAAAsY,gCAAA,iBAAqE;QAUnExb,EADF,CAAAC,cAAA,aAA4B,uBA6BzB;QADCD,EAZA,CAAAc,UAAA,2BAAA2a,+DAAAnb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAmb,GAAA;UAAA,OAAA1b,EAAA,CAAAa,WAAA,CAAiB0a,GAAA,CAAAnC,eAAA,CAAA9Y,MAAA,CAAuB;QAAA,EAAC,6BAAAqb,iEAAArb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAmb,GAAA;UAAA,OAAA1b,EAAA,CAAAa,WAAA,CACtB0a,GAAA,CAAA5K,iBAAA,CAAArQ,MAAA,CAAyB;QAAA,EAAC,0BAAAsb,8DAAAtb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAmb,GAAA;UAAA,OAAA1b,EAAA,CAAAa,WAAA,CAQ7B0a,GAAA,CAAA5I,YAAA,CAAArS,MAAA,CAAoB;QAAA,EAAC,wBAAAub,4DAAAvb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAmb,GAAA;UAAA,OAAA1b,EAAA,CAAAa,WAAA,CACvB0a,GAAA,CAAA/I,UAAA,CAAAlS,MAAA,CAAkB;QAAA,EAAC,wBAAAwb,4DAAAxb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAmb,GAAA;UAAA,OAAA1b,EAAA,CAAAa,WAAA,CACnB0a,GAAA,CAAA9I,YAAA,CAAAnS,MAAA,CAAoB;QAAA,EAAC,oCAAAyb,wEAAAzb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAmb,GAAA;UAAA,OAAA1b,EAAA,CAAAa,WAAA,CACT0a,GAAA,CAAA9B,sBAAA,CAAAnZ,MAAA,CAA8B;QAAA,EAAC;QAyczDN,EAvcA,CAAAkD,UAAA,IAAA8Y,wCAAA,2BAAsC,IAAAC,wCAAA,yBAqFA,IAAAC,yCAAA,0BAgDW,IAAAC,wCAAA,yBAkUT;QAY5Cnc,EADE,CAAAG,YAAA,EAAa,EACT;;;QA1fAH,EAAA,CAAAgC,UAAA,SAAAuZ,GAAA,CAAAzS,OAAA,IAAAyS,GAAA,CAAAxS,SAAA,CAA0B;QAY5B/I,EAAA,CAAA6B,SAAA,GAA0B;QAqB1B7B,EArBA,CAAAgC,UAAA,SAAAuZ,GAAA,CAAA5R,iBAAA,CAA0B,aAAA4R,GAAA,CAAArZ,IAAA,CAAA4J,IAAA,CACJ,SAAAyP,GAAA,CAAA7P,IAAA,CACT,aAAA1L,EAAA,CAAAoc,eAAA,KAAAC,GAAA,EAAArc,EAAA,CAAAkE,eAAA,KAAAoY,GAAA,GAOX,aAAAtc,EAAA,CAAAkE,eAAA,KAAAqY,GAAA,EACgD,oBAC/B,eAAAvc,EAAA,CAAAkE,eAAA,KAAAsY,GAAA,EACoC,qBAGnC,oBAED,eACL,SAAAjB,GAAA,CAAArZ,IAAA,CAAA6J,UAAA,GAAAwP,GAAA,CAAArZ,IAAA,CAAA4J,IAAA,CACsB,WAAAyP,GAAA,CAAAtR,MAAA,CACnB,eAAAjK,EAAA,CAAAkE,eAAA,KAAAuY,GAAA,EACc;QA2IEzc,EAAA,CAAA6B,SAAA,GAAc;QAAd7B,EAAA,CAAAgC,UAAA,YAAAuZ,GAAA,CAAA1Q,WAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}